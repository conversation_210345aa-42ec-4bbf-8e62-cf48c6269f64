.printarea {
    position: relative;
}

.printarea.print-ready {
    display: none;
}

.printarea.preview {
    overflow: hidden !important;
    background: #fff !important;
}

@media print {

    html,
    html > body.joint-print {
        position: relative !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    html > body.joint-print > * {
        display: none !important;
    }

    html > body.joint-print > .printarea {
        display: block !important;
    }

    .printarea {
        page-break-after: always;
        left: 0 !important;
        top: 0 !important;
        overflow: hidden !important;
        background: #fff !important;
        margin: 0mm !important;
        padding: 0mm !important;
    }

    .printarea.print-ready {
        display: none;
    }
}

