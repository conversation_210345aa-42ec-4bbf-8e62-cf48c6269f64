export class DependencyService {

    constructor() {
        this.callbacks = {}; // { [key: string]: ((dependency, value, path) => void)[] }
        this.dependencies = {}; // { [key: string]: { path: string, name: string } }
    }

    subscribe(dependencies, callback) {
        dependencies.forEach(dep => {
            this.dependencies[dep.path] = dep;
            if (!this.callbacks[dep.path])
                this.callbacks[dep.path] = [];
            this.callbacks[dep.path].push(callback);
        });
    }

    changed(path) {
        let paths = Object.getOwnPropertyNames(this.callbacks);
        paths = paths.filter(dep => path.startsWith(dep));
        paths.forEach(dep => {
            this.callbacks[dep].forEach(callback => {
                callback(this.dependencies[dep], path);
            })
        })
    }

    clear() {
        this.dependencies = {};
        this.callbacks = {};
    }
}
