.joint-inspector.joint-theme-material {
    color: #55627b;
    background: #ecf0f8;
    font-family: lato-light, Arial, sans-serif;
}

.joint-inspector.joint-theme-material label {
    font-size: 12px;
    padding-right: 4px;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-material input,
.joint-inspector.joint-theme-material .content-editable,
.joint-inspector.joint-theme-material textarea {
    color: #55627b;
    border: none;
    outline: none;
    background: transparent;
}
.joint-inspector.joint-theme-material input[type="text"],
.joint-inspector.joint-theme-material input[type="number"],
.joint-inspector.joint-theme-material .content-editable,
.joint-inspector.joint-theme-material textarea {
    border-bottom: 2px solid #5fa9ee;
    padding: 4px 0;
    line-height: 16px;
}

.joint-inspector.joint-theme-material .content-editable {
    width: calc(100% - 8px); /* twice the padding from above */
    box-sizing: content-box;
    min-height: 1em;
}
.joint-inspector.joint-theme-material textarea {
    width: 100%;
    resize: vertical;
}

.joint-inspector.joint-theme-material select.select {
    font-size: 14px;
    font-family: lato-light, Arial, sans-serif;
    background: #ecf0f8;
    color: #55627b;
    -webkit-appearance: none;
    appearance: none;
    -webkit-padding-end: 20px;
    -webkit-padding-start: 2px;
    background-image: url('data:image/png;base64,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');
    background-position: right center;
    background-repeat: no-repeat;
    overflow: hidden;
    padding: 5px 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 200px;
    cursor: pointer;
}

.joint-inspector.joint-theme-material select.select::-ms-expand {
    visibility: hidden;
}

.joint-inspector.joint-theme-material output,
.joint-inspector.joint-theme-material .units {
    font-size: 12px;
    font-weight: 700;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-material .group {
    height: auto;
    padding: 0;
    padding-bottom: 20px;
    margin-bottom: 1px;
    max-height: 5000px;
    transition: max-height 0.25s cubic-bezier(0.5, 0, 1, 0) -.1s;
	transition-delay: 0s;
}
.joint-inspector.joint-theme-material .group.closed {
    height: auto;
    max-height: 31px;
    padding: 0;
    transition: max-height 0.25s cubic-bezier(0, 1, 0, 1) -.1s;
}

.joint-inspector.joint-theme-material .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #55627b;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
    background: #d0d8e8;
    padding: 0 5px 0 15px;
}

.joint-inspector.joint-theme-material .group > .group-label:hover {
    color: #5faaee;
}

.joint-inspector.joint-theme-material .toggle {
    height: 14px;
    width: 36px;
    border-radius: 14px;
}
.joint-inspector.joint-theme-material .toggle input:checked + span {
    background: #5fa9ee;
}
.joint-inspector.joint-theme-material .toggle span {
    background: rgba(0,0,0,.26);
    color: #f6f6f6;
    border-radius: 14px;
    box-shadow: none;
}
.joint-inspector.joint-theme-material .toggle span i {
    right: 50%;
    width: 50%;
    top: -2px;
    height: 130%;
    left: 0;
    border-radius: 50%;
    cursor: pointer;
    background: #fafafa;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12);
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: left;
}
.joint-inspector.joint-theme-material .toggle input:checked + span i {
    position: absolute;
    left: 20px;
    background: #3f51b5;
    box-shadow: 0 3px 4px 0 rgba(0,0,0,.14),0 3px 3px -2px rgba(0,0,0,.2),0 1px 8px 0 rgba(0,0,0,.12);
}
.joint-inspector.joint-theme-material .btn-list-add,
.joint-inspector.joint-theme-material .btn-list-del {
    background: #5fa9ee;
    color: #deebfb;
    font-weight: bold;
    font-size: 17px;
    border: none;
}
.joint-inspector.joint-theme-material .btn-list-add:hover,
.joint-inspector.joint-theme-material .btn-list-del:hover {
    background-color: #4C88BE;
}

.joint-inspector.joint-theme-material .select-box {
    color: #000;
}

.joint-inspector.joint-theme-material .select-box.color-palette.joint-theme-material div.select-box-selection {
    color: #ddd;
    border: 1px solid transparent;
}

.select-box[data-attribute$="/stroke"].color-palette.joint-theme-material .select-box-selection div.select-box-option-content:after {
    left: 5px;
    top: 5px;
}

.joint-inspector.joint-theme-material .group .field {
    box-sizing: border-box;
    padding: 0 18px;
    margin-top: 12px;
}

.joint-inspector.joint-theme-material input {
    width: 100%;
    height: auto;
    text-shadow: none;
    box-shadow: none;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0;
}

.joint-inspector.joint-theme-material input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
    height: 30px;
}

.joint-inspector.joint-theme-material input[type="range"]:focus {
    outline: none;
}

.joint-inspector.joint-theme-material input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-inspector.joint-theme-material input[type="range"]::-webkit-slider-thumb {
    margin-top: -5px;
    cursor: move;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-thumb {
    margin-top: 0;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-webkit-slider-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-ms-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-thumb {
    cursor: move;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-moz-range-thumb {

    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-lower,
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-lower {
    background: #3f51b5
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
    cursor: pointer;
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-progress {
    height: 3px;
    background: #3f51b5;
}
.joint-inspector.joint-theme-material input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
    cursor: pointer;
}

.joint-inspector.joint-theme-material select {
    width: 100%;
    height: auto;
    line-height: 14px;
    text-shadow: none;
    box-shadow: none;
    border: 2px solid #5fa9ee;
    box-sizing: border-box;
    outline: none;
    padding: 6px;
    overflow: auto;
}

.joint-inspector.joint-theme-material select:not([multiple]) {
    height: 33px;
}

/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    /* change "6px padding" for visible text and same size as other browser */
    .joint-inspector.joint-theme-material input[type="text"],
    .joint-inspector.joint-theme-material input[type="number"] {
        padding: 0 0 4px 10px;
    }
}

.joint-inspector.joint-theme-material option {
    background: white;
    padding: 0 10px
}

.joint-inspector.joint-theme-material input[type="color"] {
    width: 40px;
    height: 40px;
}

/*  Lists  */
.joint-inspector.joint-theme-material .list-item {
    background: #d0d8e8;
    box-shadow: none;
    border: none;
    padding: 16px;
}
.joint-inspector.joint-theme-material .list-item .field {
    padding: 0;
}
/*  Lists  */

/* animation ripple toggle */
.joint-inspector.joint-theme-material input.toggle + span:after {
    content: '';
    position: absolute;
    z-index: 2;
    box-sizing: border-box;
    border-radius: 50%;
    background: #3f51b5;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    top: -12px;
    left: -12px;
    overflow: hidden;
    width: 40px;
    height: 40px;
}
.joint-inspector.joint-theme-material .toggle-field.is-in-action input.toggle + span:after {
    opacity: 0.3;
}
.joint-inspector.joint-theme-material input.toggle:checked + span:after {
    transform: translate(20px, 0);
}

/* input animation */
.joint-inspector.joint-theme-material .textarea-field > .input-wrapper,
.joint-inspector.joint-theme-material .number-field > .input-wrapper,
.joint-inspector.joint-theme-material .content-editable-field > .input-wrapper,
.joint-inspector.joint-theme-material .text-field > .input-wrapper {
    position: relative;
}

.joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .content-editable-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .number-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .text-field > .input-wrapper:after {
    background: #3f51b5;
    bottom: 0;
    content: '';
    height: 2px;
    left: 45%;
    position: absolute;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    z-index: -1;
    width: 10px;
    transition-property: width, left, z-index;
    display: block;
}

.joint-inspector.joint-theme-material .is-focused.textarea-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.content-editable-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.number-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.text-field > .input-wrapper:after {
    z-index: 1000;
    left:0;
    width: 100%;
}

@-moz-document url-prefix() {
    .joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after {
        bottom: 1px;
    }
}
@media (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {
    .joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after {
        bottom: 3px;
    }
}
