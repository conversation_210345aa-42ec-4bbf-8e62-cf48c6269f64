.joint-stencil {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.joint-stencil > .content {
    position: absolute;
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.joint-stencil.searchable > .content {
    top: 30px;
}

.joint-stencil.collapsible > .content {
    top: 30px;
}

.joint-stencil.collapsible.searchable > .content {
    top: 50px;
}

.joint-stencil .joint-elements {
    height: 100%;
    width: 100%;
}

/* This element is being dragged when a new element is about to be dropped into the main paper. */
.stencil-paper-drag {
    position: absolute;
    z-index: 100;
    top: -10000px;
    left: -10000px;
    display: none;
    background: none !important;
    opacity: .7;
    cursor: none;
    pointer-events: none;
}
.stencil-paper-drag.dragging {
    display: inline-block;
}
.stencil-paper-drag.dragging * {
    pointer-events: none !important;
}

.joint-stencil .group {
    overflow: hidden;
    padding: 0;
    padding-bottom: 10px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.joint-stencil .group.closed {
    height: auto;
    padding-bottom: 0;
}

.joint-stencil .group > .group-label {
    position: relative;
    padding: 5px 4px;
    margin-top: 0;
    margin-bottom: 0;
    cursor: pointer;
}

.joint-stencil .joint-element [magnet]:not([magnet="passive"]) {
    pointer-events: none;
}

/* .group.unmatched and .joint-element.unmatched */
.joint-stencil .unmatched {
    opacity: 0.3;
}

.joint-stencil .search {
    width: 100%;
    box-sizing: border-box;
    height: 30px;
    max-height: 30px;
    line-height: 30px;
    z-index: 1;
    outline: none;
    position: relative;
}

/* Not found popover. */

.joint-stencil:after {
    display: block;
    content: '';
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
    height: 20px;
    line-height: 20px;
    padding: 8px 0;
    text-align: center;
    opacity: 0;
    transition: top 100ms linear, opacity 100ms linear;
}

.joint-stencil.not-found:after {
    content: attr(data-text-no-matches-found);
    opacity: 1;
}

.joint-stencil.not-found.searchable:after {
    top: 30px;
}

.joint-stencil.not-found.searchable.collapsible:after {
    top: 50px;
}

.joint-stencil .groups-toggle .group-label:hover {
    cursor: pointer;
}

.joint-stencil .group > .group-label,
.joint-stencil .groups-toggle > .group-label {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.joint-stencil .groups-toggle > .group-label {
    padding: 0 5px 0 53px;
}

/* By default, unmatched elements are hidden. See below for making them opaque instead. */
.joint-stencil .joint-element.unmatched {
    display: none;
}
.joint-stencil .group.unmatched {
    display: none;
}

/*
Use the following in your custom CSS to make
unmatched elements opaque instead of completely invisible which is the default.
*/
/*
.joint-stencil .joint-element.unmatched {
    display: block;
}
.joint-stencil .group.unmatched {
    display: block;
}
*/
