.joint-stencil.joint-theme-material {
    color: #55627b;
    background: #717d98;
    font-family: lato-light;
}

.joint-stencil.joint-theme-material > .content {
    position: absolute;
}

.joint-stencil.joint-theme-material.searchable > .content{
    top: 48px;
    border: none;
    background: #ecf0f8;
}

.joint-stencil.joint-theme-material.collapsible > .content {
    top: 30px;
}

.joint-stencil.joint-theme-material.searchable.collapsible > .content {
    top: 80px;
}

.joint-stencil.joint-theme-material.not-found:after {
    position: absolute;
}

.joint-stencil.joint-theme-material.not-found.searchable.collapsible:after {
    top: 80px;
}

.joint-stencil.joint-theme-material.not-found.searchable:after {
    top: 60px;
}

.joint-stencil.joint-theme-material .group {
    height: auto;
    max-height: 5000px;
    padding: 0;
    margin-bottom: 1px;
    transition: max-height 0.25s cubic-bezier(0.5, 0, 1, 0) -.1s;
	transition-delay: 0s;
}

.joint-stencil.joint-theme-material .group > .elements {
    background: #ecf0f8;
    margin: 0;
}

.joint-stencil.joint-theme-material .group.closed {
    height: auto;
    max-height: 31px;
    overflow: hidden;
	transition: max-height 0.25s cubic-bezier(0, 1, 0, 1) -.1s;
}

.joint-stencil.joint-theme-material input[type="search"] {
    -webkit-appearance: textfield;
    appearance: textfield;
}

.joint-stencil.joint-theme-material input[type="search"]::-webkit-search-decoration,
.joint-stencil.joint-theme-material input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.joint-stencil.joint-theme-material input[type="search"]::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: #d2d7e2;
}

.joint-stencil.joint-theme-material input[type="search"]:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
   color: #d2d7e2;
   opacity: 1;
}

.joint-stencil.joint-theme-material input[type="search"]::-moz-placeholder { /* Mozilla Firefox 19+ */
   color: #d2d7e2;
   opacity: 1;
}

.joint-stencil.joint-theme-material input[type="search"]:-ms-input-placeholder { /* Internet Explorer 10-11 */
   color: #d2d7e2;
}

.joint-stencil.joint-theme-material .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #55627b;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
}

.joint-stencil.joint-theme-material .group > .group-label {
    padding: 0 5px 0 10px;
    background: #ecf0f8;
}
.joint-stencil.joint-theme-material .group:not(.closed) > div:after,
.joint-stencil.joint-theme-material .group > .group-label:after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: -3px;
}
.joint-stencil.joint-theme-material .group:not(.closed) > div:after,
.joint-stencil.joint-theme-material .group > .group-label:after {
  right: 0px;
  height: 4px;
  background-image: -webkit-gradient(linear, 0 0, #dfe3f0 0, from(transparent), to(#dfe3f0));
  background-image: -webkit-linear-gradient(right, #dfe3f0, transparent);
  background-image: -moz-linear-gradient(right, #dfe3f0, transparent);
  background-image: -o-linear-gradient(right, #dfe3f0, transparent);
}

.joint-stencil.joint-theme-material .group > .group-label:hover,
.joint-stencil.joint-theme-material .groups-toggle > .group-label:hover {
    color: #5faaee;
}
.joint-stencil.joint-theme-material .group > .group-label:hover {
    border-right: 5px solid #5fa9ee;
}
.joint-stencil.joint-theme-material .search {
    display: block;
    width: 90%;
    color: #ecf0f8;
    background: #717d98;
    border: none;
    border-bottom: 1px solid #ecf0f8;
    outline: none;
    padding-left: 8px;
    background: url('data:image/png;base64,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') no-repeat right center;
}

.joint-stencil.joint-theme-material .search:focus {
    outline: none;
}

.joint-stencil.joint-theme-material:after {
    font-size: 12px;
    font-weight: 700;
    background: transparent;
    color: #55627b;
}

.joint-stencil.joint-theme-material .groups-toggle > .group-label {
    padding: 0 5px 0 10px;
    position: relative;
    float: left;
    color: white;
    padding: 10px;
    font-weight: bold;
}

.joint-stencil.joint-theme-material .groups-toggle .btn {
    line-height: 25px;
    position: relative;
    display: inline-block;
    width: 28px;
    right: 0px;
    margin-left: 2px;
    float: right;
    cursor: pointer;
    background: #828da6;
    color: #deebfb;
    font-weight: bold;
    font-size: 17px;
    border: none;
    outline: none;
}

.joint-stencil.joint-theme-material .btn:hover {
    background-color: #5fa9ee;
}
.joint-stencil.joint-theme-material.searchable .search-wrap {
    position: relative;
    margin: 8px 5%;
}
.joint-stencil.joint-theme-material.searchable .search-wrap:after {
    background: #3f51b5;
    bottom: 0px;
    content: '';
    height: 2px;
    left: 40%;
    position: absolute;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    z-index: -1;
    width: 10px;
    transition-property: width, left, z-index;
    display: block;
}

.joint-stencil.joint-theme-material.searchable.is-focused .search-wrap:after {
    z-index: 1000;
    left: 0;
    width: 90%;
}
.joint-stencil.joint-theme-material.searchable .groups-toggle {
    height: 30px;
}
