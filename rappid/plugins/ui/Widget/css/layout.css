.joint-widget[data-type="zoomSlider"] output,
.joint-widget[data-type="range"] output {
    min-width: 1.6em;
    display: inline-block;
    text-align: right;
}

.joint-widget[data-type="separator"] {
    border-right: 1px solid #b3b3b3;
}

.joint-widget[data-type="separator"]:after {
    content: "\00a0";
}

.joint-widget input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-widget input[type="range"]::-ms-fill-lower {
    background: transparent;
    border-color: transparent;
}

/* Toggle */

.joint-widget .toggle {
   position: relative;
   width: 97px;
   height: 14px;
}
.joint-widget .toggle input {
   top: 0;
   right: 0;
   bottom: 0;
   left: 0;
   -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
   filter: alpha(opacity=0);
   -moz-opacity: 0;
   opacity: 0;
   z-index: 100;
   position: absolute;
   width: 100%;
   height: 100%;
   cursor: pointer;
   box-sizing: border-box;
   padding: 0;
   box-shadow: none;
   -webkit-appearance: none;
}
.joint-widget .toggle span {
   display: block;
   width: 100%;
   height: 100%;
   border-radius: 40px;
   position: relative;
}
.joint-widget .toggle input:checked + span i {
   right: 0;
}
.joint-widget .toggle span i {
   display: block;
   height: 100%;
   width: 60%;
   border-radius: inherit;
   position: absolute;
   z-index: 2;
   right: 40%;
   top: 0;
}

/* Color Picker */
.joint-widget input[type='color'] {
    padding: 0;
    width: 150%;
    height: 150%;
    margin: -25%;
}

.joint-widget[data-type="colorPicker"] {
    box-sizing: border-box;
    overflow: hidden;
    width: 30px;
    height: 30px;
    border-radius: 4px;
}

.joint-widget[data-type="colorPicker"] {
    border: 1px solid lightgrey;
}

.joint-widget[data-type="colorPicker"].disabled {
    opacity: 0.5;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-widget {
        float: left;
    }
}
