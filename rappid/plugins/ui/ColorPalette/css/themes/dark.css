.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option-content {
    border: none;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection,
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection .select-box-option-content {
	width: 30px;
	height: 30px;
    border: none;
    border-radius: 6px;
    padding: 0;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection:after,
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option.selected:after {
    display: none;
}

/*  Icons  */
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option-icon {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
}
/*  Icons  */


/*  Options  */
.joint-select-box-options.joint-color-palette.joint-theme-dark {
    width: 144px;
    border: 2px solid #383c3f;
    background: #92979b;
}

/*  Arrow  */
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-options-arrow {
    border-bottom-color: #383c3f;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-options-arrow:after {
    border-bottom-color: #92979b;
}
/*  Arrow  */

.joint-select-box-options.joint-color-palette.joint-theme-dark .select-box-option {
    width: 25px;
    height: 25px;
    border: none;
    border-radius: 5px;
    padding: 0;
}

.joint-select-box-options.joint-color-palette.joint-theme-dark .select-box-option.hover {
    border: 2px solid #b2ac9e;
}
/*  Options  */


/*  When color palette is used for a stroke attribute  */
.joint-select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-dark .select-box-selection .select-box-option-content:after {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 22px;
    height: 22px;
    background: #5e6366;
    border-radius: 4px;
    content: ' ';
}
/*  When color palette is used for a stroke attribute  */
