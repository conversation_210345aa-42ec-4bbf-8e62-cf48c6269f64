.joint-dialog.joint-theme-default {
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif !important;
}

.joint-dialog.joint-theme-default .bg {
    background-color: black;
}

.joint-dialog.joint-theme-default .fg {
    border-radius: 5px;
    background-color: white;
    border: 1px solid lightgrey;
}

.joint-dialog.joint-theme-default.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    margin: 0;
    z-index: auto;
}

.joint-dialog.joint-theme-default .titlebar {
    background-color: #7c68fc;
    padding: 10px;
    padding-right: 25px;
    color: white;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-bottom: 1px solid lightgrey;
}

.joint-dialog.joint-theme-default[data-type="alert"] .titlebar {
    background-color: #fe854f;
}
.joint-dialog.joint-theme-default[data-type="warning"] .titlebar {
    background-color: #feb663;
}
.joint-dialog.joint-theme-default[data-type="success"] .titlebar {
    background-color: #31d0c6;
}
.joint-dialog.joint-theme-default[data-type="neutral"] .titlebar {
    background-color: #efefef;
    color: #696c8a;
}

.joint-dialog.joint-theme-default .btn-close {
    background-color: transparent;
    border: 1px solid transparent;
    font-size: 16px;
    font-family: Arial;
    border-radius: 4px;
}

.joint-dialog.joint-theme-default .btn-close:hover {
    border: 1px solid black;
}

.joint-dialog.joint-theme-default .body {
    padding: 10px;
}

.joint-dialog.joint-theme-default .controls .control-button {
    border: 1px solid lightgrey;
    color: black;
    background-color: transparent;
    padding: 0 15px;
    font-size: 10pt;
    border-radius: 4px;
}

.joint-dialog.joint-theme-default .controls .control-button:hover {
    background-color: lightgrey;
}
