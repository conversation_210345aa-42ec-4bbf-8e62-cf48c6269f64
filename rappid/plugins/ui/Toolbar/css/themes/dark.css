.joint-toolbar.joint-theme-dark {
    padding: 8px;
    box-sizing: border-box;
    color: #d3d3d5;
    background: #5e6366;
}

.joint-toolbar.joint-theme-dark label,
.joint-toolbar.joint-theme-dark .units,
.joint-toolbar.joint-theme-dark output {
    font-size: .8em;
}

.joint-toolbar.joint-theme-dark .joint-widget {
    margin-left: 6px;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar.joint-theme-dark .joint-widget[data-type="selectButtonGroup"] {
        padding-top: 1px;
    }

    .joint-toolbar.joint-theme-dark .joint-widget[data-type="toggle"] span:first-child {
        line-height: 32px;
    }

    .joint-toolbar.joint-theme-dark label {
        line-height: 33px;
    }
}
