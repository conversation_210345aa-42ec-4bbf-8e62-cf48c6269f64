@font-face {
  font-family: 'select-box-material';
  src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-select-box.joint-theme-material {
    font-size: 14px;
    line-height: 1em;
    font-family: lato-light, Arial, sans-serif;
    background: #ecf0f8;
    color: #55627b;
}

.joint-select-box.joint-theme-material .select-box-selection {
    border: 2px solid #5fa9ee;
}

.joint-select-box.joint-theme-material .select-box-placeholder {
    color: #d6d6d6;
}
.joint-select-box.joint-theme-material .select-box-selection:before {
    height: 100%;
    content: ' ';
    position: absolute;
    width: 32px;
    top: 0;
    background: #5fa9ee;
    right: 2px;
}

.joint-select-box.joint-theme-material .select-box-selection:after {
    height: 15px;
    content: '\e800';
    font-family: "select-box-material";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    position: absolute;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    background: #5fa9ee;
    right: 2px;
    color: #FFFFFF;
    border: 9px solid #5fa9ee;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection:before,
.joint-select-box.joint-theme-material .select-box-option.selected {
    background: #d0d8e8;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection {
    border: 2px solid #d0d8e8;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection:after {
    border: 9px solid #d0d8e8;
    background: #d0d8e8;
}

.joint-select-box-options.joint-theme-material {
    font-size: 14px;
    border: 2px solid #5fa9ee;
    border-radius: 2px;
    background-color: #ecf0f8;
}

.joint-select-box.joint-theme-material .select-box-option.hover {
    background-color: #d0d8e8;
    transition: background-color 1.6s cubic-bezier(0, 0, 0.2, 1);
}

.joint-select-box.joint-theme-material.disabled .select-box-option-content {
    color: #d6d6d6;
}

.joint-select-box.joint-select-box-options.joint-theme-material.rendered{
    -webkit-animation: select-box-pulse 200ms cubic-bezier(0,0,.2,1);
    animation: select-box-pulse 200ms cubic-bezier(0,0,.2,1);
}
@-webkit-keyframes select-box-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

@keyframes select-box-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}