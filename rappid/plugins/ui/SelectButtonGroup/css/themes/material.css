.joint-select-button-group.joint-theme-material .select-button-group-button {
    text-align: center;
    border: 2px solid transparent;
    font-family: lato-light, Arial, sans-serif;
    border-radius: 4px;
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: border-color;
}
.joint-select-button-group.joint-theme-material .select-button-group-button.selected {
    color: #55627b;
    border: 2px solid #55627b;
}
.joint-select-button-group.joint-theme-material.disabled .select-button-group-button {
    color: #d0d8e8;
}
.joint-select-button-group.joint-theme-material.disabled .select-button-group-button.selected {
    border-color: #d0d8e8;
}
/* animation */
.joint-select-button-group.joint-theme-material:not(.disabled) .select-button-group-button:after {
    content: '';
    background: #3f51b5;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -20px 0 0 -20px;
    overflow: hidden;
    width: 40px;
    height: 40px;
}
.joint-select-button-group.joint-theme-material .select-button-group-button.is-in-action:after {
    opacity: 0.3;
}