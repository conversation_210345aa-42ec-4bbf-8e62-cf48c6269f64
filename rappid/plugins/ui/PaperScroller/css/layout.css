.joint-paper-scroller {
    position: relative;
    overflow: scroll;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    /* prevent an infinite loop when no size defined */
    max-height: 100000px;
    max-width: 100000px;
}

.joint-paper-scroller > .paper-scroller-background {
    margin: 0;
    position:relative;
    display: inline-block;
    vertical-align: top;
    overflow: hidden;
}

.joint-paper-scroller .joint-paper {
    margin: 0;
    position:absolute;
    display: inline-block;
}

.joint-paper-scroller .joint-paper > svg {
    display: block;
}

/* Cursors */

.joint-paper-scroller[data-cursor="grab"] {
    cursor: all-scroll; /* fallback: no `url()` support or images disabled */
    cursor: -webkit-grab; /* Chrome 1-21, Safari 4+ */
    cursor: -moz-grab; /* Firefox 1.5-26 */
    cursor: grab; /* W3C standards syntax, should come least */
}

.joint-paper-scroller[data-cursor="grab"].is-panning {
    cursor: -webkit-grabbing;
    cursor: -moz-grabbing;
    cursor: grabbing;
}
