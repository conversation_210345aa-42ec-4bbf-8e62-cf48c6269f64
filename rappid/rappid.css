
/*
A complete list of SVG properties that can be set through CSS is here:
http://www.w3.org/TR/SVG/styling.html

Important note: Presentation attributes have a lower precedence over CSS style rules.
*/


/* .viewport is a <g> node wrapping all diagram elements in the paper */
.joint-viewport {
   -webkit-user-select: none;
   -moz-user-select: none;
   user-select: none;
}

.joint-paper > svg,
.joint-paper-background,
.joint-paper-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/*
1. IE can't handle paths without the `d` attribute for bounding box calculation
2. IE can't even handle 'd' attribute as a css selector (e.g path[d]) so the following rule will
   break the links rendering.

path:not([d]) {
    display: none;
}

*/


/* magnet is an element that can be either source or a target of a link */
[magnet=true]:not(.joint-element) {
   cursor: crosshair;
}
[magnet=true]:not(.joint-element):hover {
   opacity: .7;
}

/*

Elements have CSS classes named by their types. E.g. type: basic.Rect has a CSS class "element basic Rect".
This makes it possible to easilly style elements in CSS and have generic CSS rules applying to
the whole group of elements. Each plugin can provide its own stylesheet.

*/

.joint-element {
   /* Give the user a hint that he can drag&drop the element. */
   cursor: move;
}

.joint-element * {
   -webkit-user-drag: none;
}

.joint-element .scalable * {
   /* The default behavior when scaling an element is not to scale the stroke in order to prevent the ugly effect of stroke with different proportions. */
   vector-effect: non-scaling-stroke;
}
/*

connection-wrap is a <path> element of the joint.dia.Link that follows the .connection <path> of that link.
In other words, the `d` attribute of the .connection-wrap contains the same data as the `d` attribute of the
.connection <path>. The advantage of using .connection-wrap is to be able to catch pointer events
in the neighborhood of the .connection <path>. This is especially handy if the .connection <path> is
very thin.

*/

.marker-source,
.marker-target {
   /* This makes the arrowheads point to the border of objects even though the transform: scale() is applied on them. */
   vector-effect: non-scaling-stroke;
}

/* Paper */
.joint-paper {
    position: relative;
}
/* Paper */

/*  Highlighting  */
.joint-highlight-opacity {
    opacity: 0.3;
}
/*  Highlighting  */

/*

Vertex markers are `<circle>` elements that appear at connection vertex positions.

*/

.joint-link .connection-wrap,
.joint-link .connection {
   fill: none;
}

/* <g> element wrapping .marker-vertex-group. */
.marker-vertices {
   opacity: 0;
   cursor: move;
}
.marker-arrowheads {
   opacity: 0;
   cursor: move;
   cursor: -webkit-grab;
   cursor: -moz-grab;
/*   display: none;   */   /* setting `display: none` on .marker-arrowheads effectivelly switches of links reconnecting */
}
.link-tools {
   opacity: 0;
   cursor: pointer;
}
.link-tools .tool-options {
   display: none;       /* by default, we don't display link options tool */
}
.joint-link:hover .marker-vertices,
.joint-link:hover .marker-arrowheads,
.joint-link:hover .link-tools {
   opacity: 1;
}

/* <circle> element used to remove a vertex */
.marker-vertex-remove {
   cursor: pointer;
   opacity: .1;
}

.marker-vertex-group:hover .marker-vertex-remove {
   opacity: 1;
}

.marker-vertex-remove-area {
   opacity: .1;
   cursor: pointer;
}
.marker-vertex-group:hover .marker-vertex-remove-area {
   opacity: 1;
}

/*
Example of custom changes (in pure CSS only!):

Do not show marker vertices at all:  .marker-vertices { display: none; }
Do not allow adding new vertices: .connection-wrap { pointer-events: none; }
*/

/* foreignObject inside the elements (i.e joint.shapes.basic.TextBlock) */
.joint-element .fobj {
    overflow: hidden;
}
.joint-element .fobj body {
    background-color: transparent;
    margin: 0px;
    position: static;
}
.joint-element .fobj div {
    text-align: center;
    vertical-align: middle;
    display: table-cell;
    padding: 0px 5px 0px 5px;
}

/* Paper */
.joint-paper.joint-theme-dark {
    background-color: #18191b;
}
/* Paper */

/*  Links  */
.joint-link.joint-theme-dark .connection-wrap {
    stroke: #8F8FF3;
    stroke-width: 15;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0;
    cursor: move;
}
.joint-link.joint-theme-dark .connection-wrap:hover {
    opacity: .4;
    stroke-opacity: .4;
}
.joint-link.joint-theme-dark .connection {
    stroke-linejoin: round;
}
.joint-link.joint-theme-dark .link-tools .tool-remove circle {
    fill: #F33636;
}
.joint-link.joint-theme-dark .link-tools .tool-remove path {
    fill: white;
}
.joint-link.joint-theme-dark .link-tools [event="link:options"] circle {
    fill: green;
}
/* <circle> element inside .marker-vertex-group <g> element */
.joint-link.joint-theme-dark .marker-vertex {
    fill: #5652DB;
}
.joint-link.joint-theme-dark .marker-vertex:hover {
    fill: #8E8CE1;
    stroke: none;
}
.joint-link.joint-theme-dark .marker-arrowhead {
    fill: #5652DB;
}
.joint-link.joint-theme-dark .marker-arrowhead:hover {
    fill: #8E8CE1;
    stroke: none;
}
/* <circle> element used to remove a vertex */
.joint-link.joint-theme-dark .marker-vertex-remove-area {
    fill: green;
    stroke: darkgreen;
}
.joint-link.joint-theme-dark .marker-vertex-remove {
    fill: white;
    stroke: white;
}
/*  Links  */
/* Paper */
.joint-paper.joint-theme-default {
    background-color: #FFFFFF;
}
/* Paper */

/*  Links  */
.joint-link.joint-theme-default .connection-wrap {
    stroke: #000000;
    stroke-width: 15;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0;
    cursor: move;
}
.joint-link.joint-theme-default .connection-wrap:hover {
    opacity: .4;
    stroke-opacity: .4;
}
.joint-link.joint-theme-default .connection {
    stroke-linejoin: round;
}
.joint-link.joint-theme-default .link-tools .tool-remove circle {
    fill: #FF0000;
}
.joint-link.joint-theme-default .link-tools .tool-remove path {
    fill: #FFFFFF;
}

/* <circle> element inside .marker-vertex-group <g> element */
.joint-link.joint-theme-default .marker-vertex {
    fill: #1ABC9C;
}
.joint-link.joint-theme-default .marker-vertex:hover {
    fill: #34495E;
    stroke: none;
}

.joint-link.joint-theme-default .marker-arrowhead {
    fill: #1ABC9C;
}
.joint-link.joint-theme-default .marker-arrowhead:hover {
    fill: #F39C12;
    stroke: none;
}

/* <circle> element used to remove a vertex */
.joint-link.joint-theme-default .marker-vertex-remove {
    fill: #FFFFFF;
}
/*  Links  */

@font-face {
    font-family: 'lato-light';
    src: url(data:application/font-woff;charset=utf-8;base64,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) format('woff');
    font-weight: normal;
    font-style: normal;
}

/*  Links  */
.joint-link.joint-theme-material .connection-wrap {
    stroke: #000000;
    stroke-width: 15;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0;
    cursor: move;
}
.joint-link.joint-theme-material .connection-wrap:hover {
    opacity: .4;
    stroke-opacity: .4;
}
.joint-link.joint-theme-material .connection {
    stroke-linejoin: round;
}
.joint-link.joint-theme-material .link-tools .tool-remove circle {
    fill: #C64242;
}
.joint-link.joint-theme-material .link-tools .tool-remove path {
    fill: #FFFFFF;
}

/* <circle> element inside .marker-vertex-group <g> element */
.joint-link.joint-theme-material .marker-vertex {
    fill: #d0d8e8;
}
.joint-link.joint-theme-material .marker-vertex:hover {
    fill: #5fa9ee;
    stroke: none;
}

.joint-link.joint-theme-material .marker-arrowhead {
    fill: #d0d8e8;
}
.joint-link.joint-theme-material .marker-arrowhead:hover {
    fill: #5fa9ee;
    stroke: none;
}

/* <circle> element used to remove a vertex */
.joint-link.joint-theme-material .marker-vertex-remove-area {
    fill: #5fa9ee;
}
.joint-link.joint-theme-material .marker-vertex-remove {
    fill: white;
}
/*  Links  */

/*  Links  */
.joint-link.joint-theme-modern .connection-wrap {
    stroke: #000000;
    stroke-width: 15;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0;
    cursor: move;
}
.joint-link.joint-theme-modern .connection-wrap:hover {
    opacity: .4;
    stroke-opacity: .4;
}
.joint-link.joint-theme-modern .connection {
    stroke-linejoin: round;
}
.joint-link.joint-theme-modern .link-tools .tool-remove circle {
    fill: #FF0000;
}
.joint-link.joint-theme-modern .link-tools .tool-remove path {
    fill: #FFFFFF;
}

/* <circle> element inside .marker-vertex-group <g> element */
.joint-link.joint-theme-modern .marker-vertex {
    fill: #1ABC9C;
}
.joint-link.joint-theme-modern .marker-vertex:hover {
    fill: #34495E;
    stroke: none;
}

.joint-link.joint-theme-modern .marker-arrowhead {
    fill: #1ABC9C;
}
.joint-link.joint-theme-modern .marker-arrowhead:hover {
    fill: #F39C12;
    stroke: none;
}

/* <circle> element used to remove a vertex */
.joint-link.joint-theme-modern .marker-vertex-remove {
    fill: white;
}
/*  Links  */

.joint-paper-scroller {
    position: relative;
    overflow: scroll;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    /* prevent an infinite loop when no size defined */
    max-height: 100000px;
    max-width: 100000px;
}

.joint-paper-scroller > .paper-scroller-background {
    margin: 0;
    position:relative;
    display: inline-block;
    vertical-align: top;
    overflow: hidden;
}

.joint-paper-scroller .joint-paper {
    margin: 0;
    position:absolute;
    display: inline-block;
}

.joint-paper-scroller .joint-paper > svg {
    display: block;
}

/* Cursors */

.joint-paper-scroller[data-cursor="grab"] {
    cursor: all-scroll; /* fallback: no `url()` support or images disabled */
    cursor: -webkit-grab; /* Chrome 1-21, Safari 4+ */
    cursor: -moz-grab; /* Firefox 1.5-26 */
    cursor: grab; /* W3C standards syntax, should come least */
}

.joint-paper-scroller[data-cursor="grab"].is-panning {
    cursor: -webkit-grabbing;
    cursor: -moz-grabbing;
    cursor: grabbing;
}

.joint-paper-scroller.joint-theme-dark > .paper-scroller-background {
    background: #18191b;
}
.joint-paper-scroller.joint-theme-dark .joint-paper {
    border: 1px dotted #fefefe;
}

.joint-paper-scroller.joint-theme-material {
    background-color: #f3f5fa;
}

.joint-paper-scroller.joint-theme-modern {
    background-color: #f6f6f6;
}
.joint-paper-scroller.joint-theme-modern .joint-paper {
    box-shadow: 0 0 2px #d3d3d3;
}

.joint-selection {
   display: none;
   touch-action: none;
}

.joint-selection.lasso {
   display: block;
   position: absolute;
   opacity: .3;
   overflow: visible;
}

.joint-selection.selected {
   display: block;
   background-color: transparent;
   opacity: 1;
   cursor: move;
   /* Position the selection rectangle static so that the selection-box's are contained within
     the paper container (which is supposed to be positioned relative). The height 0 !important
     makes sure the selection rectangle is not-visible, only the selection-boxes inside it (thanks to overflow: visible). */
   position: static;
   height: 0 !important;
}

.selection-box {
   position: absolute;
   padding-right: 5px;
   padding-bottom: 5px;
   margin-top: -4px;
   margin-left: -4px;
   box-sizing: content-box;
}

.selection-box-no-events {
   pointer-events: none;
}

.selection-wrapper {
    position: absolute;
    margin-left: -6px;
    margin-top: -6px;
    padding-right: 9px;
    padding-bottom: 9px;
    pointer-events: none;
    box-sizing: content-box;
}
/* If there is zero or only one element selected, we hide the
   selection wrapper by default. */
.selection-wrapper[data-selection-length="0"],
.selection-wrapper[data-selection-length="1"] {
    display: none;
}

.joint-selection .box {
   position: absolute;
   top: 100%;
   margin-top: 30px;
   left: -20px;
   right: -20px;
   text-align: center;
   line-height: 14px;
   border-radius: 6px;
   padding: 6px;
}

/* Handle Positions */

.joint-selection .handle.se {
   bottom: -25px;
   right: -25px;
}
.joint-selection .handle.nw {
   top: -21px;
   left: -25px;
}
.joint-selection .handle.n {
   top: -22px;
   left: 50%;
   margin-left: -10px;
}
.joint-selection .handle.e {
   right: -25px;
   top: -webkit-calc(50% - 10px);
   top: calc(50% - 10px);
}
.joint-selection .handle.ne {
   top: -21px;
   right: -25px;
}
.joint-selection .handle.w {
   left: -25px;
   top: 50%;
   margin-top: -10px;
}
.joint-selection .handle.sw {
   bottom: -25px;
   left: -25px;
}
.joint-selection .handle.s {
   bottom: -24px;
   left: 50%;
   margin-left: -10px;
}

/* Default handles. */

.joint-selection .handle {
   position: absolute;
   pointer-events: auto;
   width: 20px;
   height: 20px;
   background-size: 20px 20px;
   background-repeat: no-repeat;
   -moz-user-select: none;
   -webkit-user-select: none;
   -ms-user-select: none;
   user-select: none;
   -webkit-user-drag: none;
   cursor: pointer;
}

.joint-selection .remove {
   cursor: pointer;
}
.joint-selection .rotate {
   cursor: move;
}
.joint-selection .box:empty {
   display: none;
}

@font-face {
  font-family: 'selection-icons-dark';
  src: url('data:application/octet-stream;base64,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') format('woff');
}
.joint-selection.lasso.joint-theme-dark {
    background-color: #3498DB;
    border: 2px solid #2980B9;
}

.joint-selection.joint-theme-dark .selection-box {
    border: 2px dashed #feb663 ;
}

.joint-selection.joint-theme-dark .selection-wrapper {
    border: 2px solid #feb663 ;
}

.joint-selection.joint-theme-dark .box {
    font-size: 10px;
    color: #fff;
    background-color: #A2753F;
}

.joint-selection.joint-theme-dark .handle.resize:after,
.joint-selection.joint-theme-dark .handle.rotate:after,
.joint-selection.joint-theme-dark .handle.remove:after {
  font-family: "selection-icons-dark";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  margin: auto;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  font-size: 18px;
  color: #ffb68a;
}

.joint-selection.joint-theme-dark .handle.remove:after { content: '\e800'; }
.joint-selection.joint-theme-dark .handle.resize:after { content: '\e802'; }
.joint-selection.joint-theme-dark .handle.rotate:after { content: '\e801'; }

.joint-selection.joint-theme-dark .handle.remove:hover:after,
.joint-selection.joint-theme-dark .handle.resize:hover:after,
.joint-selection.joint-theme-dark .handle.rotate:hover:after {
  color: #ffd829;
}

/* selecting elements by holding Ctrl or Command (on OS X) */
.joint-selection.lasso.joint-theme-default {
    background-color: lightgrey;
    border: 2px solid red;
}

/* selection individual elements */
.joint-selection.joint-theme-default .selection-box {
    border: 2px dashed lightgrey;
}

/* selection for two or more elements */
.joint-selection.joint-theme-default .selection-wrapper {
    border: 2px solid lightgrey;
}

/* information box for two or more elements */
.joint-selection.joint-theme-default .box {
    background-color: lightgrey;
    color: black;
    font-size: 14px;
}

.joint-selection.joint-theme-default .remove {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAO5JREFUeNrUV9sNwyAMtLoAI3SEjJIRMgqjdBRG8CiMQGnlVHwEOBAE19L9OdwRGz+IcNsibISLCBk48dlooB0RXCDNgeXbbntWbovCyVlNtkf4AeQnvJwJ//IwCQdy8zAZeynm/gYBPpcT7gbyNDGb4/4CnyOLb1M+MED+MVPxZfEhQASnFQ4hp4qIlJxAEd+KaQGlpiIC8bmCRZOvRNBL/kvGltp+RdRLfqK5wZhCITMdjaury5lB5OFBCuxvQjAtCZc/w+WFaHkpXt6MVLTj5QOJipFs+VCqYixXsZioWM1GLaf7yK45ZT1/CzAAESidXQn9F/MAAAAASUVORK5CYII=);
}
.joint-selection.joint-theme-default .rotate {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjI1NTk5RUFBMkU3RjExRTI4OUIyQzYwMkMyN0MxMDE3IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjI1NTk5RUFCMkU3RjExRTI4OUIyQzYwMkMyN0MxMDE3Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MjU1OTlFQTgyRTdGMTFFMjg5QjJDNjAyQzI3QzEwMTciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MjU1OTlFQTkyRTdGMTFFMjg5QjJDNjAyQzI3QzEwMTciLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6W+5aDAAABJElEQVR42syXbRGDMAyGYTcBOBgSkICESWAOmAMcTAJzgAQksCnYHFRC13Jlx7qkDf0Acvf+6ZF7mjRNQ8o5T/ZqmVAt1AkxIa5JrvXqmywUsAVANkmf3BV6RqKjSvpWlqD+7OYBhKKHoMNS6EuddaPUqjUqfIJyPb2Ysyye0pC6Qm0I8680KJ/vhDmcFbU2mAb9glvk48KhMAtiYY7RYunxuRVWcI2cqa/ZegBYFGWA5jPYwAy4MrGhI1hf6FaA8gPg/PSA9tSbcAz8il2XOIRM9SILXVxki3GdEvUmD6bhIHYDQeFrtEwUvsYj0WBRx34Wc5cXJcQg8GMpMPrUBsBb6DHrbie1IdNUeRe6UNLVRB72Nh1v9zfQR/+FSbf6afsIMAB0elCwFZfPigAAAABJRU5ErkJggg==);
}
.joint-selection.joint-theme-default .resize {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2RpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDowQTc4MzUwQjJGMEIxMUUyOTFFNUE1RTAwQ0EwMjU5NyIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowQTc4MzUwQTJGMEIxMUUyOTFFNUE1RTAwQ0EwMjU5NyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pk3oY88AAAEMSURBVHja7JftDYMgEIbRdABHcARG6CalGziCG3QE3KAj0A0cod3AEa6YUEMpcKeI9oeXvP5QuCeA90EBAGwPK7SU1hkZ12ldiT6F1oUycARDRHLBgiTiEzCwTNhNuRT8XOEog/AyMqlOXPEuZzx7q29aXGtIhLvQwfNuAgtrYgrcB+VWqH2BhceBD45ZE4EyB/7zIQTvCeAWgdpw1CqT2Sri2LsRZ4cddtg/GLfislo55oNZxE2ZLcFXT8haU7YED9yXpxsCGMvTn4Uqe7DIXJnsAqGYB5CjFnNT6yEE3qr7iIJT+60YXJUZQ3G8ALyof+JWfTV6xrluEuqkHw/ESW3CoJsBRVubtwADAI2b6h9uJAFqAAAAAElFTkSuQmCC');
}

@font-face {
  font-family: 'selection-icons-dark';
  src: url('data:application/octet-stream;base64,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') format('woff');
}
.joint-selection.lasso.joint-theme-material {
    background-color: #3498DB;
    border: 2px solid #2980B9;
}

.joint-selection.joint-theme-material .selection-box {
    border: 2px dashed #5faaee ;
}

.joint-selection.joint-theme-material .selection-wrapper {
    border: 2px solid #5faaee ;
}

.joint-selection.joint-theme-material .box {
    font-size: 14px;
    color: #deebfb;
    background-color: #5fa9ee;
    font-family: lato-light, Arial, sans-serif;
}

.joint-selection.joint-theme-material .handle.resize:after,
.joint-selection.joint-theme-material .handle.rotate:after,
.joint-selection.joint-theme-material .handle.remove:after {
  font-family: "selection-icons-dark";
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  margin: auto;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  font-size: 18px;
  color: #5faaee;
}

.joint-selection.joint-theme-material .handle.remove:after { content: '\e800'; }
.joint-selection.joint-theme-material .handle.resize:after { content: '\e802'; }
.joint-selection.joint-theme-material .handle.rotate:after { content: '\e801'; }

.joint-selection.joint-theme-material .handle.remove:hover:after,
.joint-selection.joint-theme-material .handle.resize:hover:after,
.joint-selection.joint-theme-material .handle.rotate:hover:after {
  color: #717d98;
}

.joint-selection.lasso.joint-theme-modern {
    background-color: #3498DB;
    border: 2px solid #2980B9;
}

.joint-selection.joint-theme-modern .selection-box {
    border: 2px dashed #feb663 ;
    box-shadow: 2px 2px 5px lightgray;
}

.joint-selection.joint-theme-modern .selection-wrapper {
    border: 2px solid #feb663 ;
    box-shadow: 2px 2px 5px lightgray;
}

.joint-selection.joint-theme-modern .box {
    font-size: 10px;
    color: #fff;
    background-color: #6a6b8a;
}

.joint-selection.joint-theme-modern .handle {
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.joint-selection.joint-theme-modern .handle.remove {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15.386%2C3.365c-3.315-3.314-8.707-3.313-12.021%2C0c-3.314%2C3.315-3.314%2C8.706%2C0%2C12.02%20c3.314%2C3.314%2C8.707%2C3.314%2C12.021%2C0S18.699%2C6.68%2C15.386%2C3.365L15.386%2C3.365z%20M4.152%2C14.598C1.273%2C11.719%2C1.273%2C7.035%2C4.153%2C4.154%20c2.88-2.88%2C7.563-2.88%2C10.443%2C0c2.881%2C2.88%2C2.881%2C7.562%2C0%2C10.443C11.716%2C17.477%2C7.032%2C17.477%2C4.152%2C14.598L4.152%2C14.598z%22%2F%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M12.157%2C11.371L7.38%2C6.593C7.162%2C6.375%2C6.809%2C6.375%2C6.592%2C6.592c-0.218%2C0.219-0.218%2C0.572%2C0%2C0.79%20l4.776%2C4.776c0.218%2C0.219%2C0.571%2C0.219%2C0.79%2C0C12.375%2C11.941%2C12.375%2C11.588%2C12.157%2C11.371L12.157%2C11.371z%22%2F%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M11.369%2C6.593l-4.777%2C4.778c-0.217%2C0.217-0.217%2C0.568%2C0%2C0.787c0.219%2C0.219%2C0.571%2C0.217%2C0.788%2C0l4.777-4.777%20c0.218-0.218%2C0.218-0.571%2C0.001-0.789C11.939%2C6.375%2C11.587%2C6.375%2C11.369%2C6.593L11.369%2C6.593z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-selection.joint-theme-modern .handle.remove:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15.386%2C3.365c-3.315-3.314-8.707-3.313-12.021%2C0c-3.314%2C3.315-3.314%2C8.706%2C0%2C12.02%20c3.314%2C3.314%2C8.707%2C3.314%2C12.021%2C0S18.699%2C6.68%2C15.386%2C3.365L15.386%2C3.365z%22%2F%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M12.157%2C11.371L7.38%2C6.593C7.162%2C6.375%2C6.809%2C6.375%2C6.592%2C6.592c-0.218%2C0.219-0.218%2C0.572%2C0%2C0.79%20l4.776%2C4.776c0.218%2C0.219%2C0.571%2C0.219%2C0.79%2C0C12.375%2C11.941%2C12.375%2C11.588%2C12.157%2C11.371L12.157%2C11.371z%22%2F%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M11.369%2C6.593l-4.777%2C4.778c-0.217%2C0.217-0.217%2C0.568%2C0%2C0.787c0.219%2C0.219%2C0.571%2C0.217%2C0.788%2C0l4.777-4.777%20c0.218-0.218%2C0.218-0.571%2C0.001-0.789C11.939%2C6.375%2C11.587%2C6.375%2C11.369%2C6.593L11.369%2C6.593z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-selection.joint-theme-modern .handle.rotate {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M9.374%2C17.592c-4.176%2C0-7.57-3.401-7.57-7.575c0-4.175%2C3.395-7.574%2C7.57-7.574c0.28%2C0%2C0.56%2C0.018%2C0.837%2C0.05%20V1.268c0-0.158%2C0.099-0.3%2C0.239-0.36c0.151-0.058%2C0.315-0.026%2C0.428%2C0.086l2.683%2C2.688c0.152%2C0.154%2C0.152%2C0.399%2C0%2C0.553l-2.68%2C2.693%20c-0.115%2C0.112-0.279%2C0.147-0.431%2C0.087c-0.141-0.063-0.239-0.205-0.239-0.361V5.296C9.934%2C5.243%2C9.654%2C5.22%2C9.374%2C5.22%20c-2.646%2C0-4.796%2C2.152-4.796%2C4.797s2.154%2C4.798%2C4.796%2C4.798c2.645%2C0%2C4.798-2.153%2C4.798-4.798c0-0.214%2C0.174-0.391%2C0.391-0.391h1.991%20c0.217%2C0%2C0.394%2C0.177%2C0.394%2C0.391C16.947%2C14.19%2C13.549%2C17.592%2C9.374%2C17.592L9.374%2C17.592z%20M9.374%2C17.592%22%2F%3E%3C%2Fsvg%3E%20');
}
.joint-selection.joint-theme-modern .handle.rotate:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M9.374%2C17.592c-4.176%2C0-7.57-3.401-7.57-7.575c0-4.175%2C3.395-7.574%2C7.57-7.574c0.28%2C0%2C0.56%2C0.018%2C0.837%2C0.05%20V1.268c0-0.158%2C0.099-0.3%2C0.239-0.36c0.151-0.058%2C0.315-0.026%2C0.428%2C0.086l2.683%2C2.688c0.152%2C0.154%2C0.152%2C0.399%2C0%2C0.553l-2.68%2C2.693%20c-0.115%2C0.112-0.279%2C0.147-0.431%2C0.087c-0.141-0.063-0.239-0.205-0.239-0.361V5.296C9.934%2C5.243%2C9.654%2C5.22%2C9.374%2C5.22%20c-2.646%2C0-4.796%2C2.152-4.796%2C4.797s2.154%2C4.798%2C4.796%2C4.798c2.645%2C0%2C4.798-2.153%2C4.798-4.798c0-0.214%2C0.174-0.391%2C0.391-0.391h1.991%20c0.217%2C0%2C0.394%2C0.177%2C0.394%2C0.391C16.947%2C14.19%2C13.549%2C17.592%2C9.374%2C17.592L9.374%2C17.592z%20M9.374%2C17.592%22%2F%3E%3C%2Fsvg%3E%20');
}
.joint-selection.joint-theme-modern .handle.resize {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3Csvg%20height%3D%2224px%22%20version%3D%221.1%22%20viewBox%3D%220%200%2024%2024%22%20width%3D%2224px%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Asketch%3D%22http%3A%2F%2Fwww.bohemiancoding.com%2Fsketch%2Fns%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%2F%3E%3Cdesc%2F%3E%3Cdefs%2F%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20id%3D%22miu%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%3E%3Cg%20id%3D%22Artboard-1%22%20transform%3D%22translate(-251.000000%2C%20-443.000000)%22%3E%3Cg%20id%3D%22slice%22%20transform%3D%22translate(215.000000%2C%20119.000000)%22%2F%3E%3Cpath%20d%3D%22M252%2C448%20L256%2C448%20L256%2C444%20L252%2C444%20L252%2C448%20Z%20M257%2C448%20L269%2C448%20L269%2C446%20L257%2C446%20L257%2C448%20Z%20M257%2C464%20L269%2C464%20L269%2C462%20L257%2C462%20L257%2C464%20Z%20M270%2C444%20L270%2C448%20L274%2C448%20L274%2C444%20L270%2C444%20Z%20M252%2C462%20L252%2C466%20L256%2C466%20L256%2C462%20L252%2C462%20Z%20M270%2C462%20L270%2C466%20L274%2C466%20L274%2C462%20L270%2C462%20Z%20M254%2C461%20L256%2C461%20L256%2C449%20L254%2C449%20L254%2C461%20Z%20M270%2C461%20L272%2C461%20L272%2C449%20L270%2C449%20L270%2C461%20Z%22%20fill%3D%22%236A6C8A%22%20id%3D%22editor-crop-glyph%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
}
.joint-selection.joint-theme-modern .handle.resize:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3Csvg%20height%3D%2224px%22%20version%3D%221.1%22%20viewBox%3D%220%200%2024%2024%22%20width%3D%2224px%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Asketch%3D%22http%3A%2F%2Fwww.bohemiancoding.com%2Fsketch%2Fns%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%2F%3E%3Cdesc%2F%3E%3Cdefs%2F%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20id%3D%22miu%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%3E%3Cg%20id%3D%22Artboard-1%22%20transform%3D%22translate(-251.000000%2C%20-443.000000)%22%3E%3Cg%20id%3D%22slice%22%20transform%3D%22translate(215.000000%2C%20119.000000)%22%2F%3E%3Cpath%20d%3D%22M252%2C448%20L256%2C448%20L256%2C444%20L252%2C444%20L252%2C448%20Z%20M257%2C448%20L269%2C448%20L269%2C446%20L257%2C446%20L257%2C448%20Z%20M257%2C464%20L269%2C464%20L269%2C462%20L257%2C462%20L257%2C464%20Z%20M270%2C444%20L270%2C448%20L274%2C448%20L274%2C444%20L270%2C444%20Z%20M252%2C462%20L252%2C466%20L256%2C466%20L256%2C462%20L252%2C462%20Z%20M270%2C462%20L270%2C466%20L274%2C466%20L274%2C462%20L270%2C462%20Z%20M254%2C461%20L256%2C461%20L256%2C449%20L254%2C449%20L254%2C461%20Z%20M270%2C461%20L272%2C461%20L272%2C449%20L270%2C449%20L270%2C461%20Z%22%20fill%3D%22%23FD6EB6%22%20id%3D%22editor-crop-glyph%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
}

.joint-halo {
   position: absolute;
   pointer-events: none;
}

.joint-halo .handle {
   position: absolute;
   pointer-events: auto;
   width: 20px;
   height: 20px;
   background-size: 20px 20px;
   background-repeat: no-repeat;
   -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
}

.joint-halo .handle {
   cursor: pointer;
}

.joint-halo .handle.hidden {
    display: none;
}

/* Built-in handles. */
/* remove and unlink handles should have a pointer cursor */

.joint-halo .resize {
   cursor: se-resize;
}

.joint-halo .clone {
   cursor: move;
}

.joint-halo .link {
   cursor: move;
   cursor: -moz-grabbing;
   cursor: -webkit-grabbing;
}

.joint-halo .fork {
   cursor: move;
}

.joint-halo .rotate {
   cursor: move;
}

/* Box */

.joint-halo .box {
   position: absolute;
   top: 100%;
   text-align: center;
   font-size: 10px;
   line-height: 14px;
   border-radius: 6px;
   padding: 6px;
}

/* Type surrounding */

.joint-halo.surrounding .box {
   left: -20px;
   right: -20px;
   margin-top: 30px;
}


.joint-halo.surrounding.small .box {
   margin-top: 25px;
}

.joint-halo.surrounding.tiny .box {
   margin-top: 20px;
}

.joint-halo.surrounding.animate .handle {
   transition: background-size 80ms, width 80ms, height 80ms, top 150ms, left 150ms, bottom 150ms, right 150ms;
}

.joint-halo.surrounding.small .handle {
   width: 15px;
   height: 15px;
   background-size: 15px 15px;
   font-size: 15px;
}

.joint-halo.surrounding.tiny .handle {
   width: 10px;
   height: 10px;
   background-size: 10px 10px;
   font-size: 10px;
}

/* Positions */

.joint-halo.surrounding .handle.se {
   bottom: -25px;
   right: -25px;
}
.joint-halo.surrounding.small .handle.se {
   bottom: -19px;
   right: -19px;
}
.joint-halo.surrounding.tiny .handle.se {
   bottom: -13px;
   right: -15px;
}

.joint-halo.surrounding .handle.nw {
   top: -21px;
   left: -25px;
}
.joint-halo.surrounding.small .handle.nw {
   top: -19px;
   left: -19px;
}
.joint-halo.surrounding.tiny .handle.nw {
   top: -13px;
   left: -15px;
}

.joint-halo.surrounding .handle.n {
   top: -22px;
   left: 50%;
   margin-left: -10px;
}
.joint-halo.surrounding.small .handle.n {
   top: -19px;
   margin-left: -7.5px;
}
.joint-halo.surrounding.tiny .handle.n {
   top: -13px;
   margin-left: -5px;
}

.joint-halo.surrounding .handle.e {
   right: -26px;
   top: -webkit-calc(50% - 10px);
   top: calc(50% - 9px);
}
.joint-halo.surrounding.small .handle.e {
   right: -19px;
   top: -webkit-calc(50% - 8px);
   top: calc(50% - 8px);
}
.joint-halo.surrounding.tiny .handle.e {
   right: -15px;
   top: -webkit-calc(50% - 5px);
   top: calc(50% - 5px);
}

.joint-halo.surrounding .handle.ne {
   top: -21px;
   right: -25px;
}
.joint-halo.surrounding.small .handle.ne {
   top: -19px;
   right: -19px;
}
.joint-halo.surrounding.tiny .handle.ne {
   top: -13px;
   right: -15px;
}

.joint-halo.surrounding .handle.w {
   left: -25px;
   top: 50%;
   margin-top: -10px;
}
.joint-halo.surrounding.small .handle.w {
   left: -19px;
   margin-top: -8px;
}
.joint-halo.surrounding.tiny .handle.w {
   left: -15px;
   margin-top: -5px;
}

.joint-halo.surrounding .handle.sw {
   bottom: -25px;
   left: -25px;
}
.joint-halo.surrounding.small .handle.sw {
   bottom: -19px;
   left: -19px;
}
.joint-halo.surrounding.tiny .handle.sw {
   bottom: -13px;
   left: -15px;
}

.joint-halo.surrounding .handle.s {
   bottom: -24px;
   left: 50%;
   margin-left: -10px;
}
.joint-halo.surrounding.small .handle.s {
   bottom: -19px;
   margin-left: -7.5px;
}
.joint-halo.surrounding.tiny .handle.s {
   bottom: -13px;
   margin-left: -5px;
}

.joint-halo.surrounding .handle.selected {
    background-color: rgba(0,0,0,0.1);
    border-radius: 3px;
}

/* Pie type */

.joint-halo.pie .box {
    margin-top: 10px;
    left: 0;
    right: 0;
}

@-webkit-keyframes pie-visibility {
    0% { visibility: hidden; }
    100% { visibility: visible; }
}

@-moz-keyframes pie-visibility {
    0% { visibility: hidden; }
    100% { visibility: visible; }
}

@-o-keyframes pie-visibility {
    0% { visibility: hidden; }
    100% { visibility: visible; }
}

@keyframes pie-visibility {
    0% { visibility: hidden; }
    100% { visibility: visible; }
}

@-webkit-keyframes pie-opening {
    0% { transform: scale(0.4) rotate(-20deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@-moz-keyframes pie-opening {
    0% { transform: scale(0.4) rotate(-20deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@-o-keyframes pie-opening {
    0% { transform: scale(0.4) rotate(-20deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@keyframes pie-opening {
    0% { transform: scale(0.4) rotate(-20deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.joint-halo.pie {
    margin: -2px 0 0 -2px;
}

.joint-halo.pie .handles {
    display: none;
    z-index: 1;
    pointer-events: visiblePainted;
    height: 100px;
    width: 100px;
    position: absolute;
    right: -50px;
    top: -webkit-calc(50% - 50px);
    top: calc(50% - 50px);
    margin: -2px -2px 0 0;
    border-radius: 50%;
    cursor: default;
}

.joint-halo.pie.open .handles {
    display: block;
    -webkit-animation: pie-visibility 0.1s, pie-opening 0.1s;
    -moz-animation: pie-visibility 0.1s, pie-opening 0.1s;
    -o-animation: pie-visibility 0.1s, pie-opening 0.1s;
    animation: pie-visibility 0.1s, pie-opening 0.1s;
    -webkit-animation-delay: 0s, 0.1s;
    -moz-animation-delay: 0s, 0.1s;
    -o-animation-delay: 0s, 0.1s;
    animation-delay: 0s, 0.1s;
    -webkit-animation-timing-function: step-end, ease;
    -moz-animation-timing-function: step-end, ease;
    -o-animation-timing-function: step-end, ease;
    animation-timing-function: step-end, ease;
}

/* It's not possible to override the pointer-events in IE on SVG elements.
   So we make the parent element of the slice really small and set the
   overflow: visible. */
.joint-halo.pie .handle {
    pointer-events: visiblePainted;
    height: auto;
    width: 1px;
}

.joint-halo.pie .slice-text-icon,
.joint-halo.pie .slice-img-icon {
    pointer-events: none;
    display: none;
}

.joint-halo.pie .slice {
   pointer-events: auto;
}

.joint-halo.pie .slice-svg {
    overflow: visible;
    pointer-events: none;
}

/* toggle pie button  */

.joint-halo.pie .pie-toggle {
    z-index: 2;
    pointer-events: visiblePainted;
    cursor: pointer;
    display: block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    box-sizing: border-box;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
    position: absolute;
    right: -15px;
    top: -webkit-calc(50% - 15px);
    top: calc(50% - 15px);
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
}

.joint-halo.pie .pie-toggle.e {
    top: -webkit-calc(50% - 15px);
    top: calc(50% - 15px);
    right: -15px;
    left: auto;
    bottom: auto;
}
.joint-halo.pie .pie-toggle.w {
    top: -webkit-calc(50% - 15px);
    top: calc(50% - 15px);
    left: -15px;
    right: auto;
    bottom: auto;
}
.joint-halo.pie .pie-toggle.n {
    left: -webkit-calc(50% - 15px);
    left: calc(50% - 15px);
    top: -15px;
    right: auto;
    bottom: auto;
}
.joint-halo.pie .pie-toggle.s {
    left: -webkit-calc(50% - 15px);
    left: calc(50% - 15px);
    bottom: -15px;
    right: auto;
    top: auto;
}

.joint-halo.pie[data-pie-toggle-position="e"] .handles {
    left: auto;
    right: -50px;
    top: -webkit-calc(50% - 50px);
    top: calc(50% - 50px);
}
.joint-halo.pie[data-pie-toggle-position="w"] .handles {
    left: -52px;
    right: auto;
    top: -webkit-calc(50% - 50px);
    top: calc(50% - 50px);
}
.joint-halo.pie[data-pie-toggle-position="n"] .handles {
    bottom: auto;
    top: -50px;
    right: auto;
    left: -webkit-calc(50% - 52px);
    left: calc(50% - 52px);
}
.joint-halo.pie[data-pie-toggle-position="s"] .handles {
    top: auto;
    bottom: -52px;
    right: auto;
    left: -webkit-calc(50% - 52px);
    left: calc(50% - 52px);
}

.joint-halo.pie.open .pie-toggle {
    -webkit-transition: 0.1s background-image;
    -moz-transition: 0.1s background-image;
    -ms-transition: 0.1s background-image;
    -o-transition: 0.1s background-image;
    transition: 0.1s background-image;
}

/* Type toolbar */

.joint-halo.toolbar .handles {
    display: table-row;
    position: absolute;
    top: -50px;
    padding: 7px 5px;
}

.joint-halo.toolbar .handles:after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: 100%;
    margin-top: 4px;
    left: 10px;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
}

.joint-halo.toolbar .handle {
    display: table-cell;
    position: relative;
    margin: 0 2px;
    background-size: 16px 16px;
    background-position: 3px 3px;
    /* disallow the cell shrinking */
    min-width: 20px;
}

.joint-halo.toolbar .handle.hidden {
    display: none;
}

/* It's important to add the pseudo element to the dom when we render the table cell (handle)
   otherwise FF would expand the entire table on hover. */
.joint-halo.toolbar .handle:after {
    content: '';
    position: absolute;
    /* top: 100%; margin-top: 7px;  does not work in IE. */
    bottom: -11px;
    width: 100%;
}

.joint-halo.toolbar .box {
   left: -20px;
   right: -20px;
   margin-top: 30px;
}

@font-face {
    font-family: 'halo-icons-dark';
    src: url('data:application/octet-stream;base64,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') format('woff');
}
.joint-halo.joint-theme-dark .handle {
    font-family: "halo-icons-dark";
    font-size: 18px;
    color: #acaca3;
}
.joint-halo.joint-theme-dark .handle:hover:before {
    color: #dadada;
}
.joint-halo.joint-theme-dark .handle.resize:before {
    content: '\e802';
}

.joint-halo.joint-theme-dark .handle.remove:before {
    content: '\e800';
}

.joint-halo.joint-theme-dark .handle.clone:before {
    content: '\e805';
}

.joint-halo.joint-theme-dark .handle.link:before {
    content: '\e803';
}

.joint-halo.joint-theme-dark .handle.fork:before {
    content: '\e804';
}

.joint-halo.joint-theme-dark .handle.unlink:before {
    content: '\e806';
}

.joint-halo.joint-theme-dark .handle.direction:before {
    content: '\e807';
}

.joint-halo.joint-theme-dark .handle.rotate:before {
    content: '\e801';
}

.joint-halo.joint-theme-dark.surrounding.tiny .handle.se,
.joint-halo.joint-theme-dark.surrounding.tiny .handle.e,
.joint-halo.joint-theme-dark.surrounding.tiny .handle.ne {
   right: -16px;
}

.joint-halo.joint-theme-dark.surrounding.tiny .handle.nw,
.joint-halo.joint-theme-dark.surrounding.tiny .handle.w,
.joint-halo.joint-theme-dark.surrounding.tiny .handle.sw {
   left: -16px;
}

joint-halo.joint-theme-dark.surrounding.tiny .handle.w {
    margin-top: -6px;
}

.joint-halo.joint-theme-dark.pie .handle:before {
    display: none;
}
.joint-halo.joint-theme-dark .box {
    color: #fff;
    background-color: #828278;
}

/*  Surrounding  */
/*  Surrounding for link */
.joint-halo.joint-theme-dark.surrounding.type-link .handle.remove {
    background-color: #fff;
    border-radius: 50%;
}
/*  Surrounding for link */
/*  Surrounding  */
/*  Pie  */
.joint-halo.joint-theme-dark.pie .handles {
    background: url('data:image/png;base64,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') repeat;
    border: 2px solid #acaca3;
}
.joint-halo.joint-theme-dark.pie .slice {
    stroke: #acaca3;
    stroke-width: 1;
    fill: transparent;
}
.joint-halo.joint-theme-dark.pie .slice:hover {
    fill: #92979b;
}
.joint-halo.joint-theme-dark.pie .slice-text-icon {
    display: block;
    fill: #acaca3;
}
.joint-halo.joint-theme-dark.pie .slice:hover + image + .slice-text-icon {
    fill: #52575b;
}
.joint-halo.joint-theme-dark.pie .handle.selected .slice {
    fill: #fff;
}
.joint-halo.joint-theme-dark.pie .pie-toggle {
    background-color: #fff;
    background-size: 16px 16px;
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20height%3D%2216px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%2016%2016%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20width%3D%2216px%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15%2C6h-5V1c0-0.55-0.45-1-1-1H7C6.45%2C0%2C6%2C0.45%2C6%2C1v5H1C0.45%2C6%2C0%2C6.45%2C0%2C7v2c0%2C0.55%2C0.45%2C1%2C1%2C1h5v5c0%2C0.55%2C0.45%2C1%2C1%2C1h2%20c0.55%2C0%2C1-0.45%2C1-1v-5h5c0.55%2C0%2C1-0.45%2C1-1V7C16%2C6.45%2C15.55%2C6%2C15%2C6z%22%2F%3E%3C%2Fsvg%3E');
    border: 2px solid #937b7b;
    -webkit-filter: brightness(0.5) invert(80%);
    filter: brightness(0.5) invert(80%);
}
.joint-halo.joint-theme-dark.pie .pie-toggle:hover {
    background-color: #acaca3;
    border-color: #828278;
    -webkit-filter: brightness(1);
    filter: brightness(1);
}
.joint-halo.joint-theme-dark.pie .pie-toggle.open {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Acc%3D%22http%3A%2F%2Fcreativecommons.org%2Fns%23%22%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%20xmlns%3Asvg%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20id%3D%22Layer_1%22%20xml%3Aspace%3D%22preserve%22%3E%3Cmetadata%20id%3D%22metadata9%22%3E%3Crdf%3ARDF%3E%3Ccc%3AWork%20rdf%3Aabout%3D%22%22%3E%3Cdc%3Aformat%3Eimage%2Fsvg%2Bxml%3C%2Fdc%3Aformat%3E%3Cdc%3Atype%20rdf%3Aresource%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Fdcmitype%2FStillImage%22%20%2F%3E%3Cdc%3Atitle%3E%3C%2Fdc%3Atitle%3E%3C%2Fcc%3AWork%3E%3C%2Frdf%3ARDF%3E%3C%2Fmetadata%3E%3Cdefs%20id%3D%22defs7%22%20%2F%3E%3Cpath%20d%3D%22M%2015%2C6%2010%2C6%20C%201.0301983%2C6.00505%2015.002631%2C6.011353%206%2C6%20L%201%2C6%20C%200.45%2C6%200%2C6.45%200%2C7%20l%200%2C2%20c%200%2C0.55%200.45%2C1%201%2C1%20l%205%2C0%20c%208.988585%2C-0.019732%20-5.02893401%2C-0.018728%204%2C0%20l%205%2C0%20c%200.55%2C0%201%2C-0.45%201%2C-1%20L%2016%2C7%20C%2016%2C6.45%2015.55%2C6%2015%2C6%20z%22%20id%3D%22path3%22%20style%3D%22fill%3A%236a6c8a%22%20%2F%3E%3C%2Fsvg%3E');
}
/*  Pie  */


/*  Toolbar  */
.joint-halo.joint-theme-dark.toolbar .handles {
    position: static;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    border: 1px solid #937b7b;
    border-bottom-width: 5px;
    border-radius: 3px;
    box-shadow: 0 1px 2px #202132;
    margin-top: -50px;
    margin-left: 45px;
    background: url('data:image/png;base64,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') repeat;
}
.joint-halo.joint-theme-dark.toolbar .handles:after {
    top: -12px;
    left: 55px;
    border-top: 6px solid #937b7b;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    margin-top: 3px;
}
.joint-halo.joint-theme-dark.toolbar .handle:hover:after {
    border-bottom: 3px solid #dadada;
    left: -1px;
}
.joint-halo.joint-theme-dark.toolbar .handle {
    display: inline-block;
    vertical-align: top;
}
.joint-halo.joint-theme-dark.toolbar .handle + .handle {
    margin-left: 4px;
}
.joint-halo.joint-theme-dark.toolbar .handle.rotate {
    position: absolute;
    right: 100%;
    top: 100%;
    margin-right: 6px;
    margin-top: 3px;
}
.joint-halo.joint-theme-dark.toolbar .handle.remove:hover:after,
.joint-halo.joint-theme-dark.toolbar .handle.rotate:hover:after {
    border-bottom: none;
}

/* Toolbar for element */
.joint-halo.joint-theme-dark.toolbar.type-element .handle.remove {
    position: absolute;
    right: 100%;
    bottom: 100%;
    margin-right: 6px;
    margin-bottom: 3px;
}
/* Toolbar for element */

/* Toolbar for link */
.joint-halo.joint-theme-dark.toolbar.type-link .handles {
    margin-left: -18px;
}

.joint-halo.joint-theme-dark.toolbar.type-link .handles:after {
    left:-9px;
}
.joint-halo.joint-theme-dark.toolbar.type-link .handles {
    margin-top: -60px;
}
.joint-halo.joint-theme-dark.toolbar.type-link .handles:after {
    top: -22px;
}

/* Toolbar for link */

/*  Toolbar  */

.joint-halo.joint-theme-default .handle {
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.joint-halo.joint-theme-default .handle.resize {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2RpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDowQTc4MzUwQjJGMEIxMUUyOTFFNUE1RTAwQ0EwMjU5NyIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDowQTc4MzUwQTJGMEIxMUUyOTFFNUE1RTAwQ0EwMjU5NyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pk3oY88AAAEMSURBVHja7JftDYMgEIbRdABHcARG6CalGziCG3QE3KAj0A0cod3AEa6YUEMpcKeI9oeXvP5QuCeA90EBAGwPK7SU1hkZ12ldiT6F1oUycARDRHLBgiTiEzCwTNhNuRT8XOEog/AyMqlOXPEuZzx7q29aXGtIhLvQwfNuAgtrYgrcB+VWqH2BhceBD45ZE4EyB/7zIQTvCeAWgdpw1CqT2Sri2LsRZ4cddtg/GLfislo55oNZxE2ZLcFXT8haU7YED9yXpxsCGMvTn4Uqe7DIXJnsAqGYB5CjFnNT6yEE3qr7iIJT+60YXJUZQ3G8ALyof+JWfTV6xrluEuqkHw/ESW3CoJsBRVubtwADAI2b6h9uJAFqAAAAAElFTkSuQmCC');
}

.joint-halo.joint-theme-default .handle.remove {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAO5JREFUeNrUV9sNwyAMtLoAI3SEjJIRMgqjdBRG8CiMQGnlVHwEOBAE19L9OdwRGz+IcNsibISLCBk48dlooB0RXCDNgeXbbntWbovCyVlNtkf4AeQnvJwJ//IwCQdy8zAZeynm/gYBPpcT7gbyNDGb4/4CnyOLb1M+MED+MVPxZfEhQASnFQ4hp4qIlJxAEd+KaQGlpiIC8bmCRZOvRNBL/kvGltp+RdRLfqK5wZhCITMdjaury5lB5OFBCuxvQjAtCZc/w+WFaHkpXt6MVLTj5QOJipFs+VCqYixXsZioWM1GLaf7yK45ZT1/CzAAESidXQn9F/MAAAAASUVORK5CYII=');
}

.joint-halo.joint-theme-default .handle.clone {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2RpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDoxNTM0NjJBRjJGMkQxMUUyQkRFM0FCRTMxMDhFQkE2QiIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDoxNTM0NjJBRTJGMkQxMUUyQkRFM0FCRTMxMDhFQkE2QiIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo2NjREODhDMjc4MkVFMjExODUyOEU5NTNCRjg5OEI3QiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PkJFWv4AAAD3SURBVHja5FfRDYMgED2bDsAIjsAIMAluoqs4CY7gCI7ABtTTnsEUNCVQanzJGT/Qx7t7HFBZa6EEHlAIxYh90HPYzCHul+pixM93TV1wfDRNA0qppGRSyh2x8A2q6xqEEIc/mqZpCcTZWJ/iaPR9D13XLe/fNqKiNd6lahxHMMb8jlhrvRlgGAbvYJwQTsytMcH9hjEGnPN0NUZS15khx2L2SMi1GwgqQfdSkKPJ1RRnau/ZMq9J3LbtVtfodezrw6H1nAp2NeWK2bm5Tx9lTyAfilNhXuOkTv/n7hTqwbFwN5DDVGcMHVIsM2fVu7lXt7s7vQQYAIMHB7xhVbHdAAAAAElFTkSuQmCC');
}

.joint-halo.joint-theme-default .handle.link {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjIwRkVFNkM3MkU3RjExRTJBMDA3RkZBQzMyMzExQzIzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjIwRkVFNkM4MkU3RjExRTJBMDA3RkZBQzMyMzExQzIzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MjBGRUU2QzUyRTdGMTFFMkEwMDdGRkFDMzIzMTFDMjMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MjBGRUU2QzYyRTdGMTFFMkEwMDdGRkFDMzIzMTFDMjMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5hjT/5AAAA8ElEQVR42syXwQ3DIAxFUbtAR+gIHLsSN2+SboA6CSOEMbghJqCAHKlNmwYwkWvpKwdinmRsY4Sos2sSJJkknxRX8rgG+C/ZJG4YG2XQt9kuSVMHcK0J96qGzgOgi+Ya+GhoFfwo6C5890wBIGqto5SScuYf2fvTKcMW895T4G/ZblrARLh5bQ5VTjnMg+ClyUCL0yA4iJ7ONABewu17koQIz8z+2iTCaY3hG7zG7yQYjS3UbMnFVk5sDYStZbJdEizX4hnBDqeD21bNOedECKF8lVLCWttTuvekx9+MPmzDHut4yzrQsz5hDn+0PQUYAOGQcmTsT0IpAAAAAElFTkSuQmCC');
}

.joint-halo.joint-theme-default .handle.fork {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QUUEAUZcNUVHAAAALtJREFUWMPtlt0RgjAMgL9zAkZglI7ACLoJm8RNHIERGMER6ksfsIeRtsGq9LvLW2i+oz8JNBoHYAZcTQEfQoCupoAH7sBZS1jGDAwbCgwh1yfEDejfCSx/3SsksXAcIxsTZYfiSQJrEiUCT1sQ45TFNQkJ33aphzB1f9ckZK9rKBkHM2YqfYgsJIr5aYnJshfkSJj3Ak3C5fQCSwmTh+hTEh4YTwUCF+D6DRNPcTuuPpD8/UhWfShtNFQe+d/oVK9MAB0AAAAASUVORK5CYII=');
}

.joint-halo.joint-theme-default .handle.unlink {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjJCNjcxNUZBMkU3RjExRTI5RURCRDA5NDlGRDBFMDgwIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjJCNjcxNUZCMkU3RjExRTI5RURCRDA5NDlGRDBFMDgwIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MkI2NzE1RjgyRTdGMTFFMjlFREJEMDk0OUZEMEUwODAiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MkI2NzE1RjkyRTdGMTFFMjlFREJEMDk0OUZEMEUwODAiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5htS6kAAABHElEQVR42uxW0Q2DIBBV0wEcwRHsBo7QERjBbkAnYARGaDdghI5gN9ANKCRHQy4HxFakH77kxeTAe95xd1JrrasSaKpCOIR3R2+oDLXHp+GQU3RAYhyezsZyCU8gwJGdgX3+wXcHfi1HyOwHGsQpuMjXprwFMU3QavGTtzHkwGJZIXoxFBBtyOer8opKog0ykQ0qrSoQpTsy7gfZg9EtKu/cnbBvm4iC454PijKUgQ4WYy9rot0Y6gBMhQvKoY70dYs+TERqAcOe4dXwsUXbWdF7IgsztM3/jsziqd69uLZqp/GbdgoNEJF7gMR+BC7KfuXInBIfwJrELF4Ss5yCLaiz4S3isyv6W8QXAbHXRaDI1ac+LvSHcC68BRgAHv/CnODh8mEAAAAASUVORK5CYII=');
}

.joint-halo.joint-theme-default .handle.direction {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20%20PUBLIC%20'-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN'%20%20'http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd'%3E%3Csvg%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%20512%20512%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%20512%20512%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%0A%09.st0%7Bfill%3A%236A6C8A%3Bstroke%3A%236A6C8A%3Bstroke-width%3A30%7D%0A%09.dot%7Bfill%3A%236A6C8A%3B%7D%0A%3C%2Fstyle%3E%3Cg%3E%3Cg%20id%3D%22XMLID_475_%22%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M133.1%2C277.1c1.8%2C0%2C3.7-0.6%2C5.4-1.7c4.1-3%2C5-8.7%2C2-12.8c-3-4.1-8.7-5-12.8-2c0%2C0%2C0%2C0%2C0%2C0%20%20%20%20%20c-4.1%2C3-5%2C8.7-2%2C12.8C127.5%2C275.8%2C130.3%2C277.1%2C133.1%2C277.1z%22%20id%3D%22XMLID_489_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M138.5%2C359.6c-4.1-3-9.8-2.1-12.8%2C2c-3%2C4.1-2.1%2C9.8%2C2%2C12.8c1.6%2C1.2%2C3.5%2C1.7%2C5.4%2C1.7%20%20%20%20%20c2.8%2C0%2C5.6-1.3%2C7.4-3.7C143.5%2C368.3%2C142.6%2C362.6%2C138.5%2C359.6z%22%20id%3D%22XMLID_726_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C327.7c-4.8%2C1.6-7.4%2C6.7-5.9%2C11.5c1.3%2C3.9%2C4.8%2C6.3%2C8.7%2C6.3c0.9%2C0%2C1.9-0.1%2C2.8-0.4%20%20%20%20%20c4.8-1.6%2C7.4-6.7%2C5.9-11.5C118%2C328.8%2C112.9%2C326.2%2C108.1%2C327.7z%22%20id%3D%22XMLID_776_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C307.3c0.9%2C0.3%2C1.9%2C0.4%2C2.8%2C0.4c3.8%2C0%2C7.4-2.4%2C8.7-6.3c1.6-4.8-1.1-9.9-5.9-11.5%20%20%20%20%20c-4.8-1.6-9.9%2C1.1-11.5%2C5.9C100.7%2C300.6%2C103.3%2C305.7%2C108.1%2C307.3z%22%20id%3D%22XMLID_777_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M169.2%2C265.4c2.4%2C0%2C4.7-1%2C6.5-2.6c1.7-1.7%2C2.7-4.1%2C2.7-6.5c0-2.4-1-4.8-2.7-6.5%20%20%20%20%20c-1.7-1.7-4.1-2.7-6.5-2.7s-4.7%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.5C164.4%2C264.4%2C166.8%2C265.4%2C169.2%2C265.4z%22%20id%3D%22XMLID_797_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M247.7%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C243.7%2C265.4%2C247.7%2C261.3%2C247.7%2C256.3z%22%20id%3D%22XMLID_798_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M213%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C208.9%2C265.4%2C213%2C261.3%2C213%2C256.3z%22%20id%3D%22XMLID_799_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M317.2%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C313.1%2C265.4%2C317.2%2C261.3%2C317.2%2C256.3z%22%20id%3D%22XMLID_800_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M282.5%2C256.3c0-5-4.1-9.1-9.1-9.1s-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20S282.5%2C261.3%2C282.5%2C256.3z%22%20id%3D%22XMLID_801_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M401.1%2C185.2c0.9%2C0%2C1.9-0.1%2C2.8-0.5c4.8-1.6%2C7.4-6.7%2C5.9-11.5c-1.6-4.8-6.7-7.4-11.5-5.8%20%20%20%20%20c-4.8%2C1.6-7.4%2C6.7-5.8%2C11.5C393.6%2C182.8%2C397.2%2C185.2%2C401.1%2C185.2z%22%20id%3D%22XMLID_802_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M403.9%2C205.2c-4.8-1.6-9.9%2C1-11.5%2C5.9l0%2C0c-1.6%2C4.8%2C1.1%2C9.9%2C5.9%2C11.5%20%20%20%20%20c0.9%2C0.3%2C1.9%2C0.5%2C2.8%2C0.5c3.9%2C0%2C7.4-2.5%2C8.7-6.3c0%2C0%2C0%2C0%2C0%2C0C411.3%2C211.9%2C408.7%2C206.8%2C403.9%2C205.2z%22%20id%3D%22XMLID_803_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C237.2L373.5%2C237.2c-4.1%2C3-5%2C8.7-2%2C12.8c1.8%2C2.4%2C4.6%2C3.7%2C7.4%2C3.7%20%20%20%20%20c1.8%2C0%2C3.7-0.6%2C5.4-1.8c4.1-3%2C4.9-8.7%2C2-12.8C383.3%2C235.1%2C377.6%2C234.2%2C373.5%2C237.2z%22%20id%3D%22XMLID_804_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C152.9c1.6%2C1.2%2C3.5%2C1.8%2C5.4%2C1.8c2.8%2C0%2C5.6-1.3%2C7.4-3.8c3-4.1%2C2.1-9.8-2-12.7%20%20%20%20%20c-4.1-3-9.8-2.1-12.7%2C2C368.5%2C144.2%2C369.4%2C149.9%2C373.5%2C152.9z%22%20id%3D%22XMLID_805_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M342.8%2C247.1c-2.4%2C0-4.8%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.4%20%20%20%20%20c1.7%2C1.7%2C4%2C2.7%2C6.5%2C2.7c2.4%2C0%2C4.7-1%2C6.5-2.7c1.7-1.7%2C2.7-4%2C2.7-6.4c0-2.4-1-4.8-2.7-6.5C347.6%2C248.1%2C345.2%2C247.1%2C342.8%2C247.1z%22%20id%3D%22XMLID_806_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M342.8%2C124.7H206.6l36.4-36.4c3.6-3.6%2C3.6-9.3%2C0-12.9c-3.6-3.6-9.3-3.6-12.9%2C0l-51.5%2C51.5%20%20%20%20%20c-1.9%2C1.9-2.8%2C4.4-2.7%2C6.9c-0.1%2C2.5%2C0.7%2C5%2C2.7%2C6.9l51.5%2C51.5c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7%20%20%20%20%20c3.6-3.6%2C3.6-9.3%2C0-12.9l-36.4-36.4h136.1c0%2C0%2C0.1%2C0%2C0.1%2C0c0.6%2C0%2C1.2-0.1%2C1.8-0.2c0.2%2C0%2C0.4-0.1%2C0.6-0.1c0.1%2C0%2C0.2%2C0%2C0.3-0.1%20%20%20%20%20c3.2-1%2C5.6-3.6%2C6.3-6.9c0.1-0.6%2C0.2-1.2%2C0.2-1.8c0-0.6-0.1-1.2-0.2-1.8C351%2C127.8%2C347.3%2C124.7%2C342.8%2C124.7z%22%20id%3D%22XMLID_807_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M322.1%2C371.3l-51.5-51.5c-3.6-3.6-9.3-3.6-12.9%2C0c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9l36.9%2C36.9H169.2%20%20%20%20%20c-2.8%2C0-5.4%2C1.3-7%2C3.3c-0.1%2C0.1-0.2%2C0.2-0.3%2C0.4c-0.1%2C0.1-0.2%2C0.2-0.2%2C0.3c-0.1%2C0.1-0.1%2C0.2-0.2%2C0.4c-0.1%2C0.1-0.2%2C0.3-0.2%2C0.4%20%20%20%20%20c0%2C0.1-0.1%2C0.2-0.1%2C0.2c-0.1%2C0.2-0.2%2C0.4-0.3%2C0.6c0%2C0%2C0%2C0%2C0%2C0.1c-0.4%2C1.1-0.7%2C2.2-0.7%2C3.4c0%2C1.5%2C0.4%2C2.9%2C1%2C4.2c0%2C0%2C0%2C0.1%2C0.1%2C0.1%20%20%20%20%20c0.1%2C0.1%2C0.1%2C0.2%2C0.2%2C0.3c0.4%2C0.7%2C0.9%2C1.3%2C1.4%2C1.8c0.4%2C0.4%2C0.7%2C0.7%2C1.2%2C1c0.1%2C0.1%2C0.1%2C0.1%2C0.2%2C0.2c0%2C0%2C0.1%2C0%2C0.1%2C0.1%20%20%20%20%20c1.4%2C0.9%2C3.1%2C1.5%2C5%2C1.5h124.4l-36%2C36c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7l51.5-51.5%20%20%20%20%20c1.9-1.9%2C2.8-4.4%2C2.7-6.9C324.8%2C375.7%2C324%2C373.2%2C322.1%2C371.3z%22%20id%3D%22XMLID_808_%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

.joint-halo.joint-theme-default .handle.rotate {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjI1NTk5RUFBMkU3RjExRTI4OUIyQzYwMkMyN0MxMDE3IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjI1NTk5RUFCMkU3RjExRTI4OUIyQzYwMkMyN0MxMDE3Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MjU1OTlFQTgyRTdGMTFFMjg5QjJDNjAyQzI3QzEwMTciIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MjU1OTlFQTkyRTdGMTFFMjg5QjJDNjAyQzI3QzEwMTciLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6W+5aDAAABJElEQVR42syXbRGDMAyGYTcBOBgSkICESWAOmAMcTAJzgAQksCnYHFRC13Jlx7qkDf0Acvf+6ZF7mjRNQ8o5T/ZqmVAt1AkxIa5JrvXqmywUsAVANkmf3BV6RqKjSvpWlqD+7OYBhKKHoMNS6EuddaPUqjUqfIJyPb2Ysyye0pC6Qm0I8680KJ/vhDmcFbU2mAb9glvk48KhMAtiYY7RYunxuRVWcI2cqa/ZegBYFGWA5jPYwAy4MrGhI1hf6FaA8gPg/PSA9tSbcAz8il2XOIRM9SILXVxki3GdEvUmD6bhIHYDQeFrtEwUvsYj0WBRx34Wc5cXJcQg8GMpMPrUBsBb6DHrbie1IdNUeRe6UNLVRB72Nh1v9zfQR/+FSbf6afsIMAB0elCwFZfPigAAAABJRU5ErkJggg==');
}

.joint-halo.joint-theme-default .box {
   color: black;
   background-color: lightgrey;
}

/*  Surrounding  */
.joint-halo.joint-theme-default.surrounding.type-link .handle.remove {
    background-color: white;
    border-radius: 50%;
}
/*  Surrounding  */


/*  Pie  */
.joint-halo.joint-theme-default.pie .handles {
    background-color: white;
    border: 2px solid black;
    overflow: hidden;
}
.joint-halo.joint-theme-default.pie .slice {
    stroke: lightgrey;
    stroke-width: 1;
    fill: transparent;
}
.joint-halo.joint-theme-default.pie .slice:hover {
    fill: lightgrey;
}
.joint-halo.joint-theme-default.pie .slice-img-icon {
    display: block;
}
.joint-halo.joint-theme-default.pie .handle.selected .slice {
    fill: white;
}
.joint-halo.joint-theme-default.pie .pie-toggle {
    background-color: white;
    background-size: 16px 16px;
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20height%3D%2216px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%2016%2016%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20width%3D%2216px%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cpath%20fill%3D%22%23000%22%20d%3D%22M15%2C6h-5V1c0-0.55-0.45-1-1-1H7C6.45%2C0%2C6%2C0.45%2C6%2C1v5H1C0.45%2C6%2C0%2C6.45%2C0%2C7v2c0%2C0.55%2C0.45%2C1%2C1%2C1h5v5c0%2C0.55%2C0.45%2C1%2C1%2C1h2%20c0.55%2C0%2C1-0.45%2C1-1v-5h5c0.55%2C0%2C1-0.45%2C1-1V7C16%2C6.45%2C15.55%2C6%2C15%2C6z%22%2F%3E%3C%2Fsvg%3E');
    border: 2px solid black;
}
.joint-halo.joint-theme-default.pie .pie-toggle.open {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Acc%3D%22http%3A%2F%2Fcreativecommons.org%2Fns%23%22%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%20xmlns%3Asvg%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20id%3D%22Layer_1%22%20xml%3Aspace%3D%22preserve%22%3E%3Cmetadata%20id%3D%22metadata9%22%3E%3Crdf%3ARDF%3E%3Ccc%3AWork%20rdf%3Aabout%3D%22%22%3E%3Cdc%3Aformat%3Eimage%2Fsvg%2Bxml%3C%2Fdc%3Aformat%3E%3Cdc%3Atype%20rdf%3Aresource%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Fdcmitype%2FStillImage%22%20%2F%3E%3Cdc%3Atitle%3E%3C%2Fdc%3Atitle%3E%3C%2Fcc%3AWork%3E%3C%2Frdf%3ARDF%3E%3C%2Fmetadata%3E%3Cdefs%20id%3D%22defs7%22%20%2F%3E%3Cpath%20d%3D%22M%2015%2C6%2010%2C6%20C%201.0301983%2C6.00505%2015.002631%2C6.011353%206%2C6%20L%201%2C6%20C%200.45%2C6%200%2C6.45%200%2C7%20l%200%2C2%20c%200%2C0.55%200.45%2C1%201%2C1%20l%205%2C0%20c%208.988585%2C-0.019732%20-5.02893401%2C-0.018728%204%2C0%20l%205%2C0%20c%200.55%2C0%201%2C-0.45%201%2C-1%20L%2016%2C7%20C%2016%2C6.45%2015.55%2C6%2015%2C6%20z%22%20id%3D%22path3%22%20style%3D%22fill%3A%23%23000%22%20%2F%3E%3C%2Fsvg%3E');
}
/*  Pie  */


/*  Toolbar  */
.joint-halo.joint-theme-default.toolbar .handles {
    position: static;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    background-color: white;
    border: 1px solid lightgrey;
    border-bottom: 3px solid black;
    border-radius: 5px;
    margin-top: -50px;
    margin-left: 45px;
}
.joint-halo.joint-theme-default.toolbar .handles:after {
    top: -12px;
    left: 55px;
    border-top: 6px solid black;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    margin-top: 0;
}

.joint-halo.joint-theme-default.toolbar .handle {
    display: inline-block;
    vertical-align: top;
}
.joint-halo.joint-theme-default.toolbar .handle + .handle {
    margin-left: 4px;
}
.joint-halo.joint-theme-default.toolbar .handle.rotate {
    position: absolute;
    right: 100%;
    top: 100%;
    margin-right: 6px;
    margin-top: 3px;
}
.joint-halo.joint-theme-default.toolbar .handle.remove:hover:after,
.joint-halo.joint-theme-default.toolbar .handle.rotate:hover:after {
    border-bottom: none;
}

/* Toolbar for element */
.joint-halo.joint-theme-default.toolbar.type-element .handle.remove {
    position: absolute;
    right: 100%;
    bottom: 100%;
    margin-right: 6px;
    margin-bottom: 3px;
}
/* Toolbar for element */

/* Toolbar for link */

.joint-halo.joint-theme-default.toolbar.type-link .handles {
    margin-left: -18px;
}
.joint-halo.joint-theme-default.toolbar.type-link .handles:after {
    left:-9px;
}
.joint-halo.joint-theme-default.toolbar.type-link .handles {
    margin-top: -60px;
}
.joint-halo.joint-theme-default.toolbar.type-link .handles:after {
    top: -22px;
}
/* Toolbar for link */

/*  Toolbar  */

@font-face {
    font-family: 'halo-icons-material';
    src: url('data:application/octet-stream;base64,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') format('woff');
}
.joint-halo.joint-theme-material .handle {
    font-family: "halo-icons-material";
    font-size: 18px;
    color: #5fa9ee;
}
.joint-halo.joint-theme-material .handle:hover:before {
    color: #717d98;
}
.joint-halo.joint-theme-material .handle.resize:before {
    content: '\e802';
}

.joint-halo.joint-theme-material .handle.remove:before {
    content: '\e800';
}

.joint-halo.joint-theme-material .handle.clone:before {
    content: '\e805';
}

.joint-halo.joint-theme-material .handle.link:before {
    content: '\e803';
}

.joint-halo.joint-theme-material .handle.fork:before {
    content: '\e804';
}

.joint-halo.joint-theme-material .handle.unlink:before {
    content: '\e806';
}

.joint-halo.joint-theme-material .handle.direction:before {
    content: '\e807';
}

.joint-halo.joint-theme-material .handle.rotate:before {
    content: '\e801';
}

.joint-halo.joint-theme-material.surrounding.tiny .handle.se,
.joint-halo.joint-theme-material.surrounding.tiny .handle.e,
.joint-halo.joint-theme-material.surrounding.tiny .handle.ne {
   right: -16px;
}

.joint-halo.joint-theme-material.surrounding.tiny .handle.nw,
.joint-halo.joint-theme-material.surrounding.tiny .handle.w,
.joint-halo.joint-theme-material.surrounding.tiny .handle.sw {
   left: -16px;
}

joint-halo.joint-theme-material.surrounding.tiny .handle.w {
    margin-top: -6px;
}

.joint-halo.joint-theme-material.pie .handle:before {
    display: none;
}
.joint-halo.joint-theme-material .box {
    color: #717d98;
    border: 1px solid #d0d8e8;
    background-color: #ecf0f8;
}

/*  Surrounding  */
/*  Surrounding for link */
.joint-halo.joint-theme-material.surrounding.type-link .handle.remove {
    background-color: #fff;
    border-radius: 50%;
}
/*  Surrounding for link */
/*  Surrounding  */
/*  Pie  */
.joint-halo.joint-theme-material.pie .handles {
    background: #ecf0f8;
    border: 2px solid #717d98;
}
.joint-halo.joint-theme-material.pie .slice {
    stroke: #717d98;
    stroke-width: 1;
    fill: transparent;
}
.joint-halo.joint-theme-material.pie .slice:hover {
    fill: #d0d8e8;
}
.joint-halo.joint-theme-material.pie .slice-text-icon {
    display: block;
    fill: #5fa9ee;
}
.joint-halo.joint-theme-material.pie .slice:hover + image + .slice-text-icon {
    fill: #717d98;
}
.joint-halo.joint-theme-material.pie .handle.selected .slice {
    fill: #fff;
}
.joint-halo.joint-theme-material.pie .pie-toggle {
    background-color: #ecf0f8;
    background-size: 16px 16px;
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20height%3D%2216px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%2016%2016%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20width%3D%2216px%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15%2C6h-5V1c0-0.55-0.45-1-1-1H7C6.45%2C0%2C6%2C0.45%2C6%2C1v5H1C0.45%2C6%2C0%2C6.45%2C0%2C7v2c0%2C0.55%2C0.45%2C1%2C1%2C1h5v5c0%2C0.55%2C0.45%2C1%2C1%2C1h2%20c0.55%2C0%2C1-0.45%2C1-1v-5h5c0.55%2C0%2C1-0.45%2C1-1V7C16%2C6.45%2C15.55%2C6%2C15%2C6z%22%2F%3E%3C%2Fsvg%3E');
    border: 2px solid #717d98;
}
.joint-halo.joint-theme-material.pie .pie-toggle:hover {
    background-color: #d0d8e8;
}
.joint-halo.joint-theme-material.pie .pie-toggle.open {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Acc%3D%22http%3A%2F%2Fcreativecommons.org%2Fns%23%22%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%20xmlns%3Asvg%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20id%3D%22Layer_1%22%20xml%3Aspace%3D%22preserve%22%3E%3Cmetadata%20id%3D%22metadata9%22%3E%3Crdf%3ARDF%3E%3Ccc%3AWork%20rdf%3Aabout%3D%22%22%3E%3Cdc%3Aformat%3Eimage%2Fsvg%2Bxml%3C%2Fdc%3Aformat%3E%3Cdc%3Atype%20rdf%3Aresource%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Fdcmitype%2FStillImage%22%20%2F%3E%3Cdc%3Atitle%3E%3C%2Fdc%3Atitle%3E%3C%2Fcc%3AWork%3E%3C%2Frdf%3ARDF%3E%3C%2Fmetadata%3E%3Cdefs%20id%3D%22defs7%22%20%2F%3E%3Cpath%20d%3D%22M%2015%2C6%2010%2C6%20C%201.0301983%2C6.00505%2015.002631%2C6.011353%206%2C6%20L%201%2C6%20C%200.45%2C6%200%2C6.45%200%2C7%20l%200%2C2%20c%200%2C0.55%200.45%2C1%201%2C1%20l%205%2C0%20c%208.988585%2C-0.019732%20-5.02893401%2C-0.018728%204%2C0%20l%205%2C0%20c%200.55%2C0%201%2C-0.45%201%2C-1%20L%2016%2C7%20C%2016%2C6.45%2015.55%2C6%2015%2C6%20z%22%20id%3D%22path3%22%20style%3D%22fill%3A%236a6c8a%22%20%2F%3E%3C%2Fsvg%3E');
}
/*  Pie  */


/*  Toolbar  */
.joint-halo.joint-theme-material.toolbar .handles {
    position: static;
    display: inline-block;
    vertical-align: top;
    white-space: nowrap;
    border: 1px solid #d0d8e8;
    border-bottom-width: 5px;
    border-radius: 3px;
    box-shadow: 0 1px 2px #d0d8e8;
    margin-top: -50px;
    margin-left: 45px;
    background: #ecf0f8;
}
.joint-halo.joint-theme-material.toolbar .handles:after {
    top: -12px;
    left: 55px;
    border-top: 6px solid #d0d8e8;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    margin-top: 3px;
}
.joint-halo.joint-theme-material.toolbar .handle:hover:after {
    border-bottom: 3px solid #717d98;
    left: -1px;
}
.joint-halo.joint-theme-material.toolbar .handle {
    display: inline-block;
    vertical-align: top;
}
.joint-halo.joint-theme-material.toolbar .handle + .handle {
    margin-left: 4px;
}
.joint-halo.joint-theme-material.toolbar .handle.rotate {
    position: absolute;
    right: 100%;
    top: 100%;
    margin-right: 6px;
    margin-top: 3px;
}
.joint-halo.joint-theme-material.toolbar .handle.remove:hover:after,
.joint-halo.joint-theme-material.toolbar .handle.rotate:hover:after {
    border-bottom: none;
}

/* Toolbar for element */
.joint-halo.joint-theme-material.toolbar.type-element .handle.remove {
    position: absolute;
    right: 100%;
    bottom: 100%;
    margin-right: 6px;
    margin-bottom: 3px;
}
/* Toolbar for element */

/* Toolbar for link */
.joint-halo.joint-theme-material.toolbar.type-link .handles {
    margin-left: -18px;
}

.joint-halo.joint-theme-material.toolbar.type-link .handles:after {
    left:-9px;
}
.joint-halo.joint-theme-material.toolbar.type-link .handles {
    margin-top: -60px;
}
.joint-halo.joint-theme-material.toolbar.type-link .handles:after {
    top: -22px;
}

/* Toolbar for link */

/*  Toolbar  */

.joint-halo.joint-theme-modern .handle {
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.joint-halo.joint-theme-modern .handle.resize {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3Csvg%20height%3D%2224px%22%20version%3D%221.1%22%20viewBox%3D%220%200%2024%2024%22%20width%3D%2224px%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Asketch%3D%22http%3A%2F%2Fwww.bohemiancoding.com%2Fsketch%2Fns%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%2F%3E%3Cdesc%2F%3E%3Cdefs%2F%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20id%3D%22miu%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%3E%3Cg%20id%3D%22Artboard-1%22%20transform%3D%22translate(-251.000000%2C%20-443.000000)%22%3E%3Cg%20id%3D%22slice%22%20transform%3D%22translate(215.000000%2C%20119.000000)%22%2F%3E%3Cpath%20d%3D%22M252%2C448%20L256%2C448%20L256%2C444%20L252%2C444%20L252%2C448%20Z%20M257%2C448%20L269%2C448%20L269%2C446%20L257%2C446%20L257%2C448%20Z%20M257%2C464%20L269%2C464%20L269%2C462%20L257%2C462%20L257%2C464%20Z%20M270%2C444%20L270%2C448%20L274%2C448%20L274%2C444%20L270%2C444%20Z%20M252%2C462%20L252%2C466%20L256%2C466%20L256%2C462%20L252%2C462%20Z%20M270%2C462%20L270%2C466%20L274%2C466%20L274%2C462%20L270%2C462%20Z%20M254%2C461%20L256%2C461%20L256%2C449%20L254%2C449%20L254%2C461%20Z%20M270%2C461%20L272%2C461%20L272%2C449%20L270%2C449%20L270%2C461%20Z%22%20fill%3D%22%236A6C8A%22%20id%3D%22editor-crop-glyph%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
}

.joint-halo.joint-theme-modern .handle.resize:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3Csvg%20height%3D%2224px%22%20version%3D%221.1%22%20viewBox%3D%220%200%2024%2024%22%20width%3D%2224px%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Asketch%3D%22http%3A%2F%2Fwww.bohemiancoding.com%2Fsketch%2Fns%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%2F%3E%3Cdesc%2F%3E%3Cdefs%2F%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20id%3D%22miu%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%3E%3Cg%20id%3D%22Artboard-1%22%20transform%3D%22translate(-251.000000%2C%20-443.000000)%22%3E%3Cg%20id%3D%22slice%22%20transform%3D%22translate(215.000000%2C%20119.000000)%22%2F%3E%3Cpath%20d%3D%22M252%2C448%20L256%2C448%20L256%2C444%20L252%2C444%20L252%2C448%20Z%20M257%2C448%20L269%2C448%20L269%2C446%20L257%2C446%20L257%2C448%20Z%20M257%2C464%20L269%2C464%20L269%2C462%20L257%2C462%20L257%2C464%20Z%20M270%2C444%20L270%2C448%20L274%2C448%20L274%2C444%20L270%2C444%20Z%20M252%2C462%20L252%2C466%20L256%2C466%20L256%2C462%20L252%2C462%20Z%20M270%2C462%20L270%2C466%20L274%2C466%20L274%2C462%20L270%2C462%20Z%20M254%2C461%20L256%2C461%20L256%2C449%20L254%2C449%20L254%2C461%20Z%20M270%2C461%20L272%2C461%20L272%2C449%20L270%2C449%20L270%2C461%20Z%22%20fill%3D%22%23FD6EB6%22%20id%3D%22editor-crop-glyph%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
}

.joint-halo.joint-theme-modern .handle.remove {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15.386%2C3.365c-3.315-3.314-8.707-3.313-12.021%2C0c-3.314%2C3.315-3.314%2C8.706%2C0%2C12.02%20c3.314%2C3.314%2C8.707%2C3.314%2C12.021%2C0S18.699%2C6.68%2C15.386%2C3.365L15.386%2C3.365z%20M4.152%2C14.598C1.273%2C11.719%2C1.273%2C7.035%2C4.153%2C4.154%20c2.88-2.88%2C7.563-2.88%2C10.443%2C0c2.881%2C2.88%2C2.881%2C7.562%2C0%2C10.443C11.716%2C17.477%2C7.032%2C17.477%2C4.152%2C14.598L4.152%2C14.598z%22%2F%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M12.157%2C11.371L7.38%2C6.593C7.162%2C6.375%2C6.809%2C6.375%2C6.592%2C6.592c-0.218%2C0.219-0.218%2C0.572%2C0%2C0.79%20l4.776%2C4.776c0.218%2C0.219%2C0.571%2C0.219%2C0.79%2C0C12.375%2C11.941%2C12.375%2C11.588%2C12.157%2C11.371L12.157%2C11.371z%22%2F%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M11.369%2C6.593l-4.777%2C4.778c-0.217%2C0.217-0.217%2C0.568%2C0%2C0.787c0.219%2C0.219%2C0.571%2C0.217%2C0.788%2C0l4.777-4.777%20c0.218-0.218%2C0.218-0.571%2C0.001-0.789C11.939%2C6.375%2C11.587%2C6.375%2C11.369%2C6.593L11.369%2C6.593z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.remove:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15.386%2C3.365c-3.315-3.314-8.707-3.313-12.021%2C0c-3.314%2C3.315-3.314%2C8.706%2C0%2C12.02%20c3.314%2C3.314%2C8.707%2C3.314%2C12.021%2C0S18.699%2C6.68%2C15.386%2C3.365L15.386%2C3.365z%22%2F%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M12.157%2C11.371L7.38%2C6.593C7.162%2C6.375%2C6.809%2C6.375%2C6.592%2C6.592c-0.218%2C0.219-0.218%2C0.572%2C0%2C0.79%20l4.776%2C4.776c0.218%2C0.219%2C0.571%2C0.219%2C0.79%2C0C12.375%2C11.941%2C12.375%2C11.588%2C12.157%2C11.371L12.157%2C11.371z%22%2F%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M11.369%2C6.593l-4.777%2C4.778c-0.217%2C0.217-0.217%2C0.568%2C0%2C0.787c0.219%2C0.219%2C0.571%2C0.217%2C0.788%2C0l4.777-4.777%20c0.218-0.218%2C0.218-0.571%2C0.001-0.789C11.939%2C6.375%2C11.587%2C6.375%2C11.369%2C6.593L11.369%2C6.593z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.clone {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M12.852%2C0.875h-9.27c-0.853%2C0-1.547%2C0.694-1.547%2C1.547v10.816h1.547V2.422h9.27V0.875z%20M15.172%2C3.965h-8.5%20c-0.849%2C0-1.547%2C0.698-1.547%2C1.547v10.816c0%2C0.849%2C0.698%2C1.547%2C1.547%2C1.547h8.5c0.85%2C0%2C1.543-0.698%2C1.543-1.547V5.512%20C16.715%2C4.663%2C16.021%2C3.965%2C15.172%2C3.965L15.172%2C3.965z%20M15.172%2C16.328h-8.5V5.512h8.5V16.328z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.clone:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M12.852%2C0.875h-9.27c-0.853%2C0-1.547%2C0.694-1.547%2C1.547v10.816h1.547V2.422h9.27V0.875z%20M15.172%2C3.965h-8.5%20c-0.849%2C0-1.547%2C0.698-1.547%2C1.547v10.816c0%2C0.849%2C0.698%2C1.547%2C1.547%2C1.547h8.5c0.849%2C0%2C1.543-0.698%2C1.543-1.547V5.512%20C16.715%2C4.663%2C16.021%2C3.965%2C15.172%2C3.965L15.172%2C3.965z%20M15.172%2C16.328h-8.5V5.512h8.5V16.328z%20M15.172%2C16.328%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.link {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M9.884%2C9.838c0.54-0.551%2C1.005-0.955%2C1.384-1.201c0.463-0.308%2C0.749-0.352%2C0.887-0.352h1.34v1.367%20c0%2C0.104%2C0.061%2C0.2%2C0.154%2C0.242s0.204%2C0.027%2C0.284-0.038l3.168-2.669c0.06-0.051%2C0.096-0.125%2C0.096-0.203S17.16%2C6.83%2C17.101%2C6.781%20l-3.168-2.677c-0.08-0.067-0.19-0.081-0.284-0.038c-0.094%2C0.045-0.154%2C0.139-0.154%2C0.242v1.414h-1.343%20c-1.24%2C0.014-2.215%2C0.67-2.927%2C1.242c-0.797%2C0.65-1.533%2C1.447-2.245%2C2.217c-0.361%2C0.391-0.7%2C0.759-1.044%2C1.1%20c-0.541%2C0.549-1.011%2C0.951-1.395%2C1.199c-0.354%2C0.231-0.678%2C0.357-0.921%2C0.357h-1.8c-0.146%2C0-0.266%2C0.12-0.266%2C0.265v2.029%20c0%2C0.148%2C0.12%2C0.268%2C0.266%2C0.268h1.8l0%2C0c1.255-0.014%2C2.239-0.667%2C2.958-1.24c0.82-0.661%2C1.572-1.475%2C2.297-2.256%20C9.225%2C10.524%2C9.555%2C10.169%2C9.884%2C9.838z%22%2F%3E%3C%2Fsvg%3E%20');
}
.joint-halo.joint-theme-modern .handle.link:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M9.884%2C9.838c0.54-0.551%2C1.005-0.955%2C1.384-1.201c0.463-0.308%2C0.749-0.352%2C0.887-0.352h1.34v1.367%20c0%2C0.104%2C0.061%2C0.2%2C0.154%2C0.242s0.204%2C0.027%2C0.284-0.038l3.168-2.669c0.06-0.051%2C0.096-0.125%2C0.096-0.203S17.16%2C6.83%2C17.101%2C6.781%20l-3.168-2.677c-0.08-0.067-0.19-0.081-0.284-0.038c-0.094%2C0.045-0.154%2C0.139-0.154%2C0.242v1.414h-1.343%20c-1.24%2C0.014-2.215%2C0.67-2.927%2C1.242c-0.797%2C0.65-1.533%2C1.447-2.245%2C2.217c-0.361%2C0.391-0.7%2C0.759-1.044%2C1.1%20c-0.541%2C0.549-1.011%2C0.951-1.395%2C1.199c-0.354%2C0.231-0.678%2C0.357-0.921%2C0.357h-1.8c-0.146%2C0-0.266%2C0.12-0.266%2C0.265v2.029%20c0%2C0.148%2C0.12%2C0.268%2C0.266%2C0.268h1.8l0%2C0c1.255-0.014%2C2.239-0.667%2C2.958-1.24c0.82-0.661%2C1.572-1.475%2C2.297-2.256%20C9.225%2C10.524%2C9.555%2C10.169%2C9.884%2C9.838z%22%2F%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.fork {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cg%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%236A6C8A%22%20d%3D%22M13.307%2C11.593c-0.69%2C0-1.299%2C0.33-1.693%2C0.835l-4.136-2.387%20C7.552%2C9.82%2C7.602%2C9.589%2C7.602%2C9.344c0-0.25-0.051-0.487-0.129-0.71l4.097-2.364c0.393%2C0.536%2C1.022%2C0.888%2C1.737%2C0.888%20c1.193%2C0%2C2.16-0.967%2C2.16-2.159s-0.967-2.159-2.16-2.159c-1.191%2C0-2.158%2C0.967-2.158%2C2.159c0%2C0.076%2C0.014%2C0.149%2C0.021%2C0.223%20L6.848%2C7.716C6.469%2C7.39%2C5.982%2C7.185%2C5.442%2C7.185c-1.191%2C0-2.158%2C0.967-2.158%2C2.159s0.967%2C2.159%2C2.158%2C2.159%20c0.545%2C0%2C1.037-0.208%2C1.417-0.541l4.319%2C2.493c-0.014%2C0.098-0.029%2C0.194-0.029%2C0.296c0%2C1.193%2C0.967%2C2.159%2C2.158%2C2.159%20c1.193%2C0%2C2.16-0.966%2C2.16-2.159C15.467%2C12.559%2C14.5%2C11.593%2C13.307%2C11.593z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.fork:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cg%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23FD6EB6%22%20d%3D%22M13.307%2C11.593c-0.69%2C0-1.299%2C0.33-1.693%2C0.835l-4.136-2.387%20c0.075-0.22%2C0.125-0.452%2C0.125-0.697c0-0.25-0.051-0.487-0.129-0.71l4.097-2.365c0.394%2C0.536%2C1.022%2C0.888%2C1.737%2C0.888%20c1.193%2C0%2C2.16-0.967%2C2.16-2.159s-0.967-2.159-2.16-2.159c-1.191%2C0-2.158%2C0.967-2.158%2C2.159c0%2C0.076%2C0.015%2C0.148%2C0.022%2C0.223%20L6.848%2C7.716C6.469%2C7.39%2C5.981%2C7.185%2C5.442%2C7.185c-1.191%2C0-2.158%2C0.967-2.158%2C2.159s0.967%2C2.159%2C2.158%2C2.159%20c0.545%2C0%2C1.037-0.208%2C1.417-0.541l4.319%2C2.493c-0.013%2C0.098-0.029%2C0.194-0.029%2C0.296c0%2C1.193%2C0.967%2C2.159%2C2.158%2C2.159%20c1.193%2C0%2C2.16-0.966%2C2.16-2.159C15.467%2C12.559%2C14.5%2C11.593%2C13.307%2C11.593z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.unlink {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cg%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M12.285%2C9.711l-2.104-0.302L9.243%2C8.568L6.669%2C7.095C6.948%2C6.6%2C6.995%2C6.026%2C6.845%2C5.474%20c-0.191-0.698-0.695-1.36-1.438-1.786C4.068%2C2.922%2C2.464%2C3.214%2C1.82%2C4.338C1.536%2C4.836%2C1.489%2C5.414%2C1.64%2C5.97%20c0.189%2C0.698%2C0.694%2C1.36%2C1.438%2C1.787c0.328%2C0.187%2C0.67%2C0.31%2C1.01%2C0.372c0.002%2C0%2C0.006%2C0.002%2C0.008%2C0.004%20c0.027%2C0.004%2C0.057%2C0.009%2C0.088%2C0.011c2.12%2C0.316%2C3.203%2C0.915%2C3.73%2C1.337c-0.527%2C0.424-1.61%2C1.021-3.731%2C1.339%20c-0.029%2C0.003-0.058%2C0.007-0.087%2C0.012c-0.002%2C0.002-0.004%2C0.002-0.007%2C0.003c-0.341%2C0.062-0.684%2C0.187-1.013%2C0.374%20c-0.74%2C0.425-1.246%2C1.089-1.437%2C1.787c-0.149%2C0.555-0.105%2C1.133%2C0.181%2C1.632c0.011%2C0.018%2C0.021%2C0.033%2C0.033%2C0.049l0.883%2C0.783%20c0.765%2C0.366%2C1.775%2C0.328%2C2.67-0.184c0.744-0.425%2C1.248-1.088%2C1.439-1.786c0.148-0.552%2C0.104-1.126-0.176-1.62l2.573-1.473%20c0.573%2C0.287%2C2.299%2C1.292%2C2.299%2C1.292s3.602%2C1.445%2C4.241%2C1.812c0.773%2C0.191%2C0.566-0.151%2C0.566-0.151L12.285%2C9.711z%20M5.571%2C6.482%20C5.279%2C6.993%2C4.425%2C7.076%2C3.705%2C6.664C3.282%2C6.424%2C2.966%2C6.039%2C2.856%2C5.64C2.81%2C5.464%2C2.778%2C5.203%2C2.917%2C4.963%20c0.291-0.51%2C1.146-0.593%2C1.866-0.182C5.21%2C5.027%2C5.521%2C5.4%2C5.632%2C5.807C5.679%2C5.98%2C5.708%2C6.242%2C5.571%2C6.482z%20M5.632%2C13.159%20c-0.111%2C0.406-0.422%2C0.778-0.848%2C1.025c-0.719%2C0.409-1.576%2C0.327-1.867-0.184c-0.137-0.239-0.106-0.499-0.06-0.676%20c0.108-0.398%2C0.426-0.781%2C0.847-1.022c0.72-0.412%2C1.574-0.329%2C1.866%2C0.181C5.708%2C12.723%2C5.679%2C12.983%2C5.632%2C13.159z%20M16.181%2C5.139%20c-0.448%2C0.258-4.435%2C1.9-4.435%2C1.9s-1.556%2C0.855-2.104%2C1.13l0.937%2C0.843l2.057-0.229l4.11-3.638%20C16.745%2C5.146%2C17.013%2C4.664%2C16.181%2C5.139z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.unlink:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cg%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M12.285%2C9.711l-2.104-0.302L9.243%2C8.568L6.669%2C7.095C6.948%2C6.6%2C6.995%2C6.026%2C6.845%2C5.474%20c-0.191-0.698-0.695-1.36-1.438-1.786C4.068%2C2.922%2C2.464%2C3.214%2C1.82%2C4.338C1.536%2C4.836%2C1.489%2C5.414%2C1.64%2C5.97%20c0.189%2C0.698%2C0.694%2C1.36%2C1.438%2C1.787c0.328%2C0.187%2C0.67%2C0.31%2C1.01%2C0.372c0.002%2C0%2C0.006%2C0.002%2C0.008%2C0.004%20c0.027%2C0.004%2C0.057%2C0.009%2C0.088%2C0.011c2.12%2C0.316%2C3.203%2C0.915%2C3.73%2C1.337c-0.527%2C0.424-1.61%2C1.021-3.731%2C1.339%20c-0.029%2C0.003-0.058%2C0.007-0.087%2C0.012c-0.002%2C0.002-0.004%2C0.002-0.007%2C0.003c-0.341%2C0.062-0.684%2C0.187-1.013%2C0.374%20c-0.74%2C0.425-1.246%2C1.089-1.437%2C1.787c-0.149%2C0.555-0.105%2C1.133%2C0.181%2C1.632c0.011%2C0.018%2C0.021%2C0.033%2C0.033%2C0.049l0.883%2C0.783%20c0.765%2C0.366%2C1.775%2C0.328%2C2.67-0.184c0.744-0.425%2C1.248-1.088%2C1.439-1.786c0.148-0.552%2C0.104-1.126-0.176-1.62l2.573-1.473%20c0.573%2C0.287%2C2.299%2C1.292%2C2.299%2C1.292s3.602%2C1.445%2C4.241%2C1.812c0.773%2C0.191%2C0.566-0.151%2C0.566-0.151L12.285%2C9.711z%20M5.571%2C6.482%20C5.279%2C6.993%2C4.425%2C7.076%2C3.705%2C6.664C3.282%2C6.424%2C2.966%2C6.039%2C2.856%2C5.64C2.81%2C5.464%2C2.778%2C5.203%2C2.917%2C4.963%20c0.291-0.51%2C1.146-0.593%2C1.866-0.182C5.21%2C5.027%2C5.521%2C5.4%2C5.632%2C5.807C5.679%2C5.98%2C5.708%2C6.242%2C5.571%2C6.482z%20M5.632%2C13.159%20c-0.111%2C0.406-0.422%2C0.778-0.848%2C1.025c-0.719%2C0.409-1.576%2C0.327-1.867-0.184c-0.137-0.239-0.106-0.499-0.06-0.676%20c0.108-0.398%2C0.426-0.781%2C0.847-1.022c0.72-0.412%2C1.574-0.329%2C1.866%2C0.181C5.708%2C12.723%2C5.679%2C12.983%2C5.632%2C13.159z%20M16.181%2C5.139%20c-0.448%2C0.258-4.435%2C1.9-4.435%2C1.9s-1.556%2C0.855-2.104%2C1.13l0.937%2C0.843l2.057-0.229l4.11-3.638%20C16.745%2C5.146%2C17.013%2C4.664%2C16.181%2C5.139z%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.direction {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20%20PUBLIC%20'-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN'%20%20'http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd'%3E%3Csvg%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%20512%20512%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%20512%20512%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%0A%09.st0%7Bfill%3A%236A6C8A%3Bstroke%3A%236A6C8A%3Bstroke-width%3A30%7D%0A%09.dot%7Bfill%3A%236A6C8A%3B%7D%0A%3C%2Fstyle%3E%3Cg%3E%3Cg%20id%3D%22XMLID_475_%22%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M133.1%2C277.1c1.8%2C0%2C3.7-0.6%2C5.4-1.7c4.1-3%2C5-8.7%2C2-12.8c-3-4.1-8.7-5-12.8-2c0%2C0%2C0%2C0%2C0%2C0%20%20%20%20%20c-4.1%2C3-5%2C8.7-2%2C12.8C127.5%2C275.8%2C130.3%2C277.1%2C133.1%2C277.1z%22%20id%3D%22XMLID_489_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M138.5%2C359.6c-4.1-3-9.8-2.1-12.8%2C2c-3%2C4.1-2.1%2C9.8%2C2%2C12.8c1.6%2C1.2%2C3.5%2C1.7%2C5.4%2C1.7%20%20%20%20%20c2.8%2C0%2C5.6-1.3%2C7.4-3.7C143.5%2C368.3%2C142.6%2C362.6%2C138.5%2C359.6z%22%20id%3D%22XMLID_726_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C327.7c-4.8%2C1.6-7.4%2C6.7-5.9%2C11.5c1.3%2C3.9%2C4.8%2C6.3%2C8.7%2C6.3c0.9%2C0%2C1.9-0.1%2C2.8-0.4%20%20%20%20%20c4.8-1.6%2C7.4-6.7%2C5.9-11.5C118%2C328.8%2C112.9%2C326.2%2C108.1%2C327.7z%22%20id%3D%22XMLID_776_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C307.3c0.9%2C0.3%2C1.9%2C0.4%2C2.8%2C0.4c3.8%2C0%2C7.4-2.4%2C8.7-6.3c1.6-4.8-1.1-9.9-5.9-11.5%20%20%20%20%20c-4.8-1.6-9.9%2C1.1-11.5%2C5.9C100.7%2C300.6%2C103.3%2C305.7%2C108.1%2C307.3z%22%20id%3D%22XMLID_777_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M169.2%2C265.4c2.4%2C0%2C4.7-1%2C6.5-2.6c1.7-1.7%2C2.7-4.1%2C2.7-6.5c0-2.4-1-4.8-2.7-6.5%20%20%20%20%20c-1.7-1.7-4.1-2.7-6.5-2.7s-4.7%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.5C164.4%2C264.4%2C166.8%2C265.4%2C169.2%2C265.4z%22%20id%3D%22XMLID_797_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M247.7%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C243.7%2C265.4%2C247.7%2C261.3%2C247.7%2C256.3z%22%20id%3D%22XMLID_798_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M213%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C208.9%2C265.4%2C213%2C261.3%2C213%2C256.3z%22%20id%3D%22XMLID_799_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M317.2%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C313.1%2C265.4%2C317.2%2C261.3%2C317.2%2C256.3z%22%20id%3D%22XMLID_800_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M282.5%2C256.3c0-5-4.1-9.1-9.1-9.1s-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20S282.5%2C261.3%2C282.5%2C256.3z%22%20id%3D%22XMLID_801_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M401.1%2C185.2c0.9%2C0%2C1.9-0.1%2C2.8-0.5c4.8-1.6%2C7.4-6.7%2C5.9-11.5c-1.6-4.8-6.7-7.4-11.5-5.8%20%20%20%20%20c-4.8%2C1.6-7.4%2C6.7-5.8%2C11.5C393.6%2C182.8%2C397.2%2C185.2%2C401.1%2C185.2z%22%20id%3D%22XMLID_802_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M403.9%2C205.2c-4.8-1.6-9.9%2C1-11.5%2C5.9l0%2C0c-1.6%2C4.8%2C1.1%2C9.9%2C5.9%2C11.5%20%20%20%20%20c0.9%2C0.3%2C1.9%2C0.5%2C2.8%2C0.5c3.9%2C0%2C7.4-2.5%2C8.7-6.3c0%2C0%2C0%2C0%2C0%2C0C411.3%2C211.9%2C408.7%2C206.8%2C403.9%2C205.2z%22%20id%3D%22XMLID_803_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C237.2L373.5%2C237.2c-4.1%2C3-5%2C8.7-2%2C12.8c1.8%2C2.4%2C4.6%2C3.7%2C7.4%2C3.7%20%20%20%20%20c1.8%2C0%2C3.7-0.6%2C5.4-1.8c4.1-3%2C4.9-8.7%2C2-12.8C383.3%2C235.1%2C377.6%2C234.2%2C373.5%2C237.2z%22%20id%3D%22XMLID_804_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C152.9c1.6%2C1.2%2C3.5%2C1.8%2C5.4%2C1.8c2.8%2C0%2C5.6-1.3%2C7.4-3.8c3-4.1%2C2.1-9.8-2-12.7%20%20%20%20%20c-4.1-3-9.8-2.1-12.7%2C2C368.5%2C144.2%2C369.4%2C149.9%2C373.5%2C152.9z%22%20id%3D%22XMLID_805_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M342.8%2C247.1c-2.4%2C0-4.8%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.4%20%20%20%20%20c1.7%2C1.7%2C4%2C2.7%2C6.5%2C2.7c2.4%2C0%2C4.7-1%2C6.5-2.7c1.7-1.7%2C2.7-4%2C2.7-6.4c0-2.4-1-4.8-2.7-6.5C347.6%2C248.1%2C345.2%2C247.1%2C342.8%2C247.1z%22%20id%3D%22XMLID_806_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M342.8%2C124.7H206.6l36.4-36.4c3.6-3.6%2C3.6-9.3%2C0-12.9c-3.6-3.6-9.3-3.6-12.9%2C0l-51.5%2C51.5%20%20%20%20%20c-1.9%2C1.9-2.8%2C4.4-2.7%2C6.9c-0.1%2C2.5%2C0.7%2C5%2C2.7%2C6.9l51.5%2C51.5c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7%20%20%20%20%20c3.6-3.6%2C3.6-9.3%2C0-12.9l-36.4-36.4h136.1c0%2C0%2C0.1%2C0%2C0.1%2C0c0.6%2C0%2C1.2-0.1%2C1.8-0.2c0.2%2C0%2C0.4-0.1%2C0.6-0.1c0.1%2C0%2C0.2%2C0%2C0.3-0.1%20%20%20%20%20c3.2-1%2C5.6-3.6%2C6.3-6.9c0.1-0.6%2C0.2-1.2%2C0.2-1.8c0-0.6-0.1-1.2-0.2-1.8C351%2C127.8%2C347.3%2C124.7%2C342.8%2C124.7z%22%20id%3D%22XMLID_807_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M322.1%2C371.3l-51.5-51.5c-3.6-3.6-9.3-3.6-12.9%2C0c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9l36.9%2C36.9H169.2%20%20%20%20%20c-2.8%2C0-5.4%2C1.3-7%2C3.3c-0.1%2C0.1-0.2%2C0.2-0.3%2C0.4c-0.1%2C0.1-0.2%2C0.2-0.2%2C0.3c-0.1%2C0.1-0.1%2C0.2-0.2%2C0.4c-0.1%2C0.1-0.2%2C0.3-0.2%2C0.4%20%20%20%20%20c0%2C0.1-0.1%2C0.2-0.1%2C0.2c-0.1%2C0.2-0.2%2C0.4-0.3%2C0.6c0%2C0%2C0%2C0%2C0%2C0.1c-0.4%2C1.1-0.7%2C2.2-0.7%2C3.4c0%2C1.5%2C0.4%2C2.9%2C1%2C4.2c0%2C0%2C0%2C0.1%2C0.1%2C0.1%20%20%20%20%20c0.1%2C0.1%2C0.1%2C0.2%2C0.2%2C0.3c0.4%2C0.7%2C0.9%2C1.3%2C1.4%2C1.8c0.4%2C0.4%2C0.7%2C0.7%2C1.2%2C1c0.1%2C0.1%2C0.1%2C0.1%2C0.2%2C0.2c0%2C0%2C0.1%2C0%2C0.1%2C0.1%20%20%20%20%20c1.4%2C0.9%2C3.1%2C1.5%2C5%2C1.5h124.4l-36%2C36c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7l51.5-51.5%20%20%20%20%20c1.9-1.9%2C2.8-4.4%2C2.7-6.9C324.8%2C375.7%2C324%2C373.2%2C322.1%2C371.3z%22%20id%3D%22XMLID_808_%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
.joint-halo.joint-theme-modern .handle.direction:hover {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20%20PUBLIC%20'-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN'%20%20'http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd'%3E%3Csvg%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%20512%20512%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%20512%20512%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%0A%09.st0%7Bfill%3A%23FD6EB6%3Bstroke%3A%23FD6EB6%3Bstroke-width%3A30%7D%0A%09.dot%7Bfill%3A%23FD6EB6%3B%7D%0A%3C%2Fstyle%3E%3Cg%3E%3Cg%20id%3D%22XMLID_475_%22%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M133.1%2C277.1c1.8%2C0%2C3.7-0.6%2C5.4-1.7c4.1-3%2C5-8.7%2C2-12.8c-3-4.1-8.7-5-12.8-2c0%2C0%2C0%2C0%2C0%2C0%20%20%20%20%20c-4.1%2C3-5%2C8.7-2%2C12.8C127.5%2C275.8%2C130.3%2C277.1%2C133.1%2C277.1z%22%20id%3D%22XMLID_489_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M138.5%2C359.6c-4.1-3-9.8-2.1-12.8%2C2c-3%2C4.1-2.1%2C9.8%2C2%2C12.8c1.6%2C1.2%2C3.5%2C1.7%2C5.4%2C1.7%20%20%20%20%20c2.8%2C0%2C5.6-1.3%2C7.4-3.7C143.5%2C368.3%2C142.6%2C362.6%2C138.5%2C359.6z%22%20id%3D%22XMLID_726_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C327.7c-4.8%2C1.6-7.4%2C6.7-5.9%2C11.5c1.3%2C3.9%2C4.8%2C6.3%2C8.7%2C6.3c0.9%2C0%2C1.9-0.1%2C2.8-0.4%20%20%20%20%20c4.8-1.6%2C7.4-6.7%2C5.9-11.5C118%2C328.8%2C112.9%2C326.2%2C108.1%2C327.7z%22%20id%3D%22XMLID_776_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M108.1%2C307.3c0.9%2C0.3%2C1.9%2C0.4%2C2.8%2C0.4c3.8%2C0%2C7.4-2.4%2C8.7-6.3c1.6-4.8-1.1-9.9-5.9-11.5%20%20%20%20%20c-4.8-1.6-9.9%2C1.1-11.5%2C5.9C100.7%2C300.6%2C103.3%2C305.7%2C108.1%2C307.3z%22%20id%3D%22XMLID_777_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M169.2%2C265.4c2.4%2C0%2C4.7-1%2C6.5-2.6c1.7-1.7%2C2.7-4.1%2C2.7-6.5c0-2.4-1-4.8-2.7-6.5%20%20%20%20%20c-1.7-1.7-4.1-2.7-6.5-2.7s-4.7%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.5C164.4%2C264.4%2C166.8%2C265.4%2C169.2%2C265.4z%22%20id%3D%22XMLID_797_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M247.7%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C243.7%2C265.4%2C247.7%2C261.3%2C247.7%2C256.3z%22%20id%3D%22XMLID_798_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M213%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C208.9%2C265.4%2C213%2C261.3%2C213%2C256.3z%22%20id%3D%22XMLID_799_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M317.2%2C256.3c0-5-4.1-9.1-9.1-9.1c-5%2C0-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20C313.1%2C265.4%2C317.2%2C261.3%2C317.2%2C256.3z%22%20id%3D%22XMLID_800_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M282.5%2C256.3c0-5-4.1-9.1-9.1-9.1s-9.1%2C4.1-9.1%2C9.1c0%2C5%2C4.1%2C9.1%2C9.1%2C9.1%20%20%20%20%20S282.5%2C261.3%2C282.5%2C256.3z%22%20id%3D%22XMLID_801_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M401.1%2C185.2c0.9%2C0%2C1.9-0.1%2C2.8-0.5c4.8-1.6%2C7.4-6.7%2C5.9-11.5c-1.6-4.8-6.7-7.4-11.5-5.8%20%20%20%20%20c-4.8%2C1.6-7.4%2C6.7-5.8%2C11.5C393.6%2C182.8%2C397.2%2C185.2%2C401.1%2C185.2z%22%20id%3D%22XMLID_802_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M403.9%2C205.2c-4.8-1.6-9.9%2C1-11.5%2C5.9l0%2C0c-1.6%2C4.8%2C1.1%2C9.9%2C5.9%2C11.5%20%20%20%20%20c0.9%2C0.3%2C1.9%2C0.5%2C2.8%2C0.5c3.9%2C0%2C7.4-2.5%2C8.7-6.3c0%2C0%2C0%2C0%2C0%2C0C411.3%2C211.9%2C408.7%2C206.8%2C403.9%2C205.2z%22%20id%3D%22XMLID_803_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C237.2L373.5%2C237.2c-4.1%2C3-5%2C8.7-2%2C12.8c1.8%2C2.4%2C4.6%2C3.7%2C7.4%2C3.7%20%20%20%20%20c1.8%2C0%2C3.7-0.6%2C5.4-1.8c4.1-3%2C4.9-8.7%2C2-12.8C383.3%2C235.1%2C377.6%2C234.2%2C373.5%2C237.2z%22%20id%3D%22XMLID_804_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M373.5%2C152.9c1.6%2C1.2%2C3.5%2C1.8%2C5.4%2C1.8c2.8%2C0%2C5.6-1.3%2C7.4-3.8c3-4.1%2C2.1-9.8-2-12.7%20%20%20%20%20c-4.1-3-9.8-2.1-12.7%2C2C368.5%2C144.2%2C369.4%2C149.9%2C373.5%2C152.9z%22%20id%3D%22XMLID_805_%22%2F%3E%0A%3Cpath%20class%3D%22dot%22%20d%3D%22M342.8%2C247.1c-2.4%2C0-4.8%2C1-6.5%2C2.7c-1.7%2C1.7-2.7%2C4-2.7%2C6.5c0%2C2.4%2C1%2C4.7%2C2.7%2C6.4%20%20%20%20%20c1.7%2C1.7%2C4%2C2.7%2C6.5%2C2.7c2.4%2C0%2C4.7-1%2C6.5-2.7c1.7-1.7%2C2.7-4%2C2.7-6.4c0-2.4-1-4.8-2.7-6.5C347.6%2C248.1%2C345.2%2C247.1%2C342.8%2C247.1z%22%20id%3D%22XMLID_806_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M342.8%2C124.7H206.6l36.4-36.4c3.6-3.6%2C3.6-9.3%2C0-12.9c-3.6-3.6-9.3-3.6-12.9%2C0l-51.5%2C51.5%20%20%20%20%20c-1.9%2C1.9-2.8%2C4.4-2.7%2C6.9c-0.1%2C2.5%2C0.7%2C5%2C2.7%2C6.9l51.5%2C51.5c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7%20%20%20%20%20c3.6-3.6%2C3.6-9.3%2C0-12.9l-36.4-36.4h136.1c0%2C0%2C0.1%2C0%2C0.1%2C0c0.6%2C0%2C1.2-0.1%2C1.8-0.2c0.2%2C0%2C0.4-0.1%2C0.6-0.1c0.1%2C0%2C0.2%2C0%2C0.3-0.1%20%20%20%20%20c3.2-1%2C5.6-3.6%2C6.3-6.9c0.1-0.6%2C0.2-1.2%2C0.2-1.8c0-0.6-0.1-1.2-0.2-1.8C351%2C127.8%2C347.3%2C124.7%2C342.8%2C124.7z%22%20id%3D%22XMLID_807_%22%2F%3E%0A%3Cpath%20class%3D%22st0%22%20d%3D%22M322.1%2C371.3l-51.5-51.5c-3.6-3.6-9.3-3.6-12.9%2C0c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9l36.9%2C36.9H169.2%20%20%20%20%20c-2.8%2C0-5.4%2C1.3-7%2C3.3c-0.1%2C0.1-0.2%2C0.2-0.3%2C0.4c-0.1%2C0.1-0.2%2C0.2-0.2%2C0.3c-0.1%2C0.1-0.1%2C0.2-0.2%2C0.4c-0.1%2C0.1-0.2%2C0.3-0.2%2C0.4%20%20%20%20%20c0%2C0.1-0.1%2C0.2-0.1%2C0.2c-0.1%2C0.2-0.2%2C0.4-0.3%2C0.6c0%2C0%2C0%2C0%2C0%2C0.1c-0.4%2C1.1-0.7%2C2.2-0.7%2C3.4c0%2C1.5%2C0.4%2C2.9%2C1%2C4.2c0%2C0%2C0%2C0.1%2C0.1%2C0.1%20%20%20%20%20c0.1%2C0.1%2C0.1%2C0.2%2C0.2%2C0.3c0.4%2C0.7%2C0.9%2C1.3%2C1.4%2C1.8c0.4%2C0.4%2C0.7%2C0.7%2C1.2%2C1c0.1%2C0.1%2C0.1%2C0.1%2C0.2%2C0.2c0%2C0%2C0.1%2C0%2C0.1%2C0.1%20%20%20%20%20c1.4%2C0.9%2C3.1%2C1.5%2C5%2C1.5h124.4l-36%2C36c-3.6%2C3.6-3.6%2C9.3%2C0%2C12.9c1.8%2C1.8%2C4.1%2C2.7%2C6.5%2C2.7c2.3%2C0%2C4.7-0.9%2C6.5-2.7l51.5-51.5%20%20%20%20%20c1.9-1.9%2C2.8-4.4%2C2.7-6.9C324.8%2C375.7%2C324%2C373.2%2C322.1%2C371.3z%22%20id%3D%22XMLID_808_%22%2F%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

.joint-halo.joint-theme-modern .handle.rotate {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M9.374%2C17.592c-4.176%2C0-7.57-3.401-7.57-7.575c0-4.175%2C3.395-7.574%2C7.57-7.574c0.28%2C0%2C0.56%2C0.018%2C0.837%2C0.05%20V1.268c0-0.158%2C0.099-0.3%2C0.239-0.36c0.151-0.058%2C0.315-0.026%2C0.428%2C0.086l2.683%2C2.688c0.152%2C0.154%2C0.152%2C0.399%2C0%2C0.553l-2.68%2C2.693%20c-0.115%2C0.112-0.279%2C0.147-0.431%2C0.087c-0.141-0.063-0.239-0.205-0.239-0.361V5.296C9.934%2C5.243%2C9.654%2C5.22%2C9.374%2C5.22%20c-2.646%2C0-4.796%2C2.152-4.796%2C4.797s2.154%2C4.798%2C4.796%2C4.798c2.645%2C0%2C4.798-2.153%2C4.798-4.798c0-0.214%2C0.174-0.391%2C0.391-0.391h1.991%20c0.217%2C0%2C0.394%2C0.177%2C0.394%2C0.391C16.947%2C14.19%2C13.549%2C17.592%2C9.374%2C17.592L9.374%2C17.592z%20M9.374%2C17.592%22%2F%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .handle.rotate:hover {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M9.374%2C17.592c-4.176%2C0-7.57-3.401-7.57-7.575c0-4.175%2C3.395-7.574%2C7.57-7.574c0.28%2C0%2C0.56%2C0.018%2C0.837%2C0.05%20V1.268c0-0.158%2C0.099-0.3%2C0.239-0.36c0.151-0.058%2C0.315-0.026%2C0.428%2C0.086l2.683%2C2.688c0.152%2C0.154%2C0.152%2C0.399%2C0%2C0.553l-2.68%2C2.693%20c-0.115%2C0.112-0.279%2C0.147-0.431%2C0.087c-0.141-0.063-0.239-0.205-0.239-0.361V5.296C9.934%2C5.243%2C9.654%2C5.22%2C9.374%2C5.22%20c-2.646%2C0-4.796%2C2.152-4.796%2C4.797s2.154%2C4.798%2C4.796%2C4.798c2.645%2C0%2C4.798-2.153%2C4.798-4.798c0-0.214%2C0.174-0.391%2C0.391-0.391h1.991%20c0.217%2C0%2C0.394%2C0.177%2C0.394%2C0.391C16.947%2C14.19%2C13.549%2C17.592%2C9.374%2C17.592L9.374%2C17.592z%20M9.374%2C17.592%22%2F%3E%3C%2Fsvg%3E%20');
}

.joint-halo.joint-theme-modern .box {
    color: #fff;
    background-color: #6a6b8a;
}

    /*  Surrounding  */
    /*  Surrounding for link */
    .joint-halo.joint-theme-modern.surrounding.type-link .handle.remove {
        background-color: #fff;
        border-radius: 50%;
    }
    /*  Surrounding for link */
    /*  Surrounding  */
    /*  Pie  */
    .joint-halo.joint-theme-modern.pie .handles {
        background-color: #f6f6f6;
        border: 2px solid #3B425F;
    }
    .joint-halo.joint-theme-modern.pie .slice {
        stroke: #E2CEFF;
        stroke-width: 1;
        fill: transparent;
    }
    .joint-halo.joint-theme-modern.pie .slice:hover {
        fill: #fff;
    }
    .joint-halo.joint-theme-modern.pie .slice-img-icon {
        display: block;
    }
    .joint-halo.joint-theme-modern.pie .handle.selected .slice {
        fill: #fff;
    }
    .joint-halo.joint-theme-modern.pie .pie-toggle {
        background-color: #f6f6f6;
        background-size: 16px 16px;
        background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20height%3D%2216px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%2016%2016%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20width%3D%2216px%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cpath%20fill%3D%22%236A6C8A%22%20d%3D%22M15%2C6h-5V1c0-0.55-0.45-1-1-1H7C6.45%2C0%2C6%2C0.45%2C6%2C1v5H1C0.45%2C6%2C0%2C6.45%2C0%2C7v2c0%2C0.55%2C0.45%2C1%2C1%2C1h5v5c0%2C0.55%2C0.45%2C1%2C1%2C1h2%20c0.55%2C0%2C1-0.45%2C1-1v-5h5c0.55%2C0%2C1-0.45%2C1-1V7C16%2C6.45%2C15.55%2C6%2C15%2C6z%22%2F%3E%3C%2Fsvg%3E');
        border: 2px solid #3B425F;
    }
    .joint-halo.joint-theme-modern.pie .pie-toggle:hover {
        background-color: #fff;
        background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20height%3D%2216px%22%20id%3D%22Layer_1%22%20style%3D%22enable-background%3Anew%200%200%2016%2016%3B%22%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20width%3D%2216px%22%20xml%3Aspace%3D%22preserve%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Cpath%20fill%3D%22%23FD6EB6%22%20d%3D%22M15%2C6h-5V1c0-0.55-0.45-1-1-1H7C6.45%2C0%2C6%2C0.45%2C6%2C1v5H1C0.45%2C6%2C0%2C6.45%2C0%2C7v2c0%2C0.55%2C0.45%2C1%2C1%2C1h5v5c0%2C0.55%2C0.45%2C1%2C1%2C1h2%20c0.55%2C0%2C1-0.45%2C1-1v-5h5c0.55%2C0%2C1-0.45%2C1-1V7C16%2C6.45%2C15.55%2C6%2C15%2C6z%22%2F%3E%3C%2Fsvg%3E');
        border-color: #FD6EB6;
    }
    .joint-halo.joint-theme-modern.pie .pie-toggle.open {
        background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Acc%3D%22http%3A%2F%2Fcreativecommons.org%2Fns%23%22%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%20xmlns%3Asvg%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20id%3D%22Layer_1%22%20xml%3Aspace%3D%22preserve%22%3E%3Cmetadata%20id%3D%22metadata9%22%3E%3Crdf%3ARDF%3E%3Ccc%3AWork%20rdf%3Aabout%3D%22%22%3E%3Cdc%3Aformat%3Eimage%2Fsvg%2Bxml%3C%2Fdc%3Aformat%3E%3Cdc%3Atype%20rdf%3Aresource%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Fdcmitype%2FStillImage%22%20%2F%3E%3Cdc%3Atitle%3E%3C%2Fdc%3Atitle%3E%3C%2Fcc%3AWork%3E%3C%2Frdf%3ARDF%3E%3C%2Fmetadata%3E%3Cdefs%20id%3D%22defs7%22%20%2F%3E%3Cpath%20d%3D%22M%2015%2C6%2010%2C6%20C%201.0301983%2C6.00505%2015.002631%2C6.011353%206%2C6%20L%201%2C6%20C%200.45%2C6%200%2C6.45%200%2C7%20l%200%2C2%20c%200%2C0.55%200.45%2C1%201%2C1%20l%205%2C0%20c%208.988585%2C-0.019732%20-5.02893401%2C-0.018728%204%2C0%20l%205%2C0%20c%200.55%2C0%201%2C-0.45%201%2C-1%20L%2016%2C7%20C%2016%2C6.45%2015.55%2C6%2015%2C6%20z%22%20id%3D%22path3%22%20style%3D%22fill%3A%236a6c8a%22%20%2F%3E%3C%2Fsvg%3E');
    }
    .joint-halo.joint-theme-modern.pie .pie-toggle.open:hover {
        background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%20standalone%3D%22no%22%3F%3E%3Csvg%20xmlns%3Adc%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Felements%2F1.1%2F%22%20xmlns%3Acc%3D%22http%3A%2F%2Fcreativecommons.org%2Fns%23%22%20xmlns%3Ardf%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2F02%2F22-rdf-syntax-ns%23%22%20xmlns%3Asvg%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20id%3D%22Layer_1%22%20xml%3Aspace%3D%22preserve%22%3E%3Cmetadata%20id%3D%22metadata9%22%3E%3Crdf%3ARDF%3E%3Ccc%3AWork%20rdf%3Aabout%3D%22%22%3E%3Cdc%3Aformat%3Eimage%2Fsvg%2Bxml%3C%2Fdc%3Aformat%3E%3Cdc%3Atype%20rdf%3Aresource%3D%22http%3A%2F%2Fpurl.org%2Fdc%2Fdcmitype%2FStillImage%22%20%2F%3E%3Cdc%3Atitle%3E%3C%2Fdc%3Atitle%3E%3C%2Fcc%3AWork%3E%3C%2Frdf%3ARDF%3E%3C%2Fmetadata%3E%3Cdefs%20id%3D%22defs7%22%20%2F%3E%3Cpath%20d%3D%22M%2015%2C6%2010%2C6%20C%201.0301983%2C6.00505%2015.002631%2C6.011353%206%2C6%20L%201%2C6%20C%200.45%2C6%200%2C6.45%200%2C7%20l%200%2C2%20c%200%2C0.55%200.45%2C1%201%2C1%20l%205%2C0%20c%208.988585%2C-0.019732%20-5.02893401%2C-0.018728%204%2C0%20l%205%2C0%20c%200.55%2C0%201%2C-0.45%201%2C-1%20L%2016%2C7%20C%2016%2C6.45%2015.55%2C6%2015%2C6%20z%22%20id%3D%22path3%22%20style%3D%22fill%3A%23FD6EB6%22%20%2F%3E%3C%2Fsvg%3E');
    }
    /*  Pie  */


    /*  Toolbar  */
    .joint-halo.joint-theme-modern.toolbar .handles {
        position: static;
        display: inline-block;
        vertical-align: top;
        white-space: nowrap;
        background-color: #f7f7f7;
        border-bottom: 3px solid #3B425F;
        border-radius: 5px;
        box-shadow: 0 1px 2px #202132;
        margin-top: -50px;
        margin-left: 45px;
    }
    .joint-halo.joint-theme-modern.toolbar .handles:after {
        top: -12px;
        left: 55px;
        border-top: 6px solid #3B425F;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        margin-top: 0;
    }
    .joint-halo.joint-theme-modern.toolbar .handle:hover:after {
        border-bottom: 4px solid #FC6CB8;
    }
    .joint-halo.joint-theme-modern.toolbar .handle {
        display: inline-block;
        vertical-align: top;
    }
    .joint-halo.joint-theme-modern.toolbar .handle + .handle {
        margin-left: 4px;
    }
    .joint-halo.joint-theme-modern.toolbar .handle.rotate {
        position: absolute;
        right: 100%;
        top: 100%;
        margin-right: 6px;
        margin-top: 3px;
    }
    .joint-halo.joint-theme-modern.toolbar .handle.remove:hover:after,
    .joint-halo.joint-theme-modern.toolbar .handle.rotate:hover:after {
        border-bottom: none;
    }

    /* Toolbar for element */
    .joint-halo.joint-theme-modern.toolbar.type-element .handle.remove {
        position: absolute;
        right: 100%;
        bottom: 100%;
        margin-right: 6px;
        margin-bottom: 3px;
    }
    /* Toolbar for element */

    /* Toolbar for link */
    .joint-halo.joint-theme-modern.toolbar.type-link .handles {
        margin-left: -18px;
    }

    .joint-halo.joint-theme-modern.toolbar.type-link .handles:after {
        left:-9px;
    }
    .joint-halo.joint-theme-modern.toolbar.type-link .handles {
        margin-top: -60px;
    }
    .joint-halo.joint-theme-modern.toolbar.type-link .handles:after {
        top: -22px;
    }

    /* Toolbar for link */

/*  Toolbar  */

.joint-stencil {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.joint-stencil > .content {
    position: absolute;
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.joint-stencil.searchable > .content {
    top: 30px;
}

.joint-stencil.collapsible > .content {
    top: 30px;
}

.joint-stencil.collapsible.searchable > .content {
    top: 50px;
}

.joint-stencil .joint-elements {
    height: 100%;
    width: 100%;
}

/* This element is being dragged when a new element is about to be dropped into the main paper. */
.stencil-paper-drag {
    position: absolute;
    z-index: 100;
    top: -10000px;
    left: -10000px;
    display: none;
    background: none !important;
    opacity: .7;
    cursor: none;
    pointer-events: none;
}
.stencil-paper-drag.dragging {
    display: inline-block;
}
.stencil-paper-drag.dragging * {
    pointer-events: none !important;
}

.joint-stencil .group {
    overflow: hidden;
    padding: 0;
    padding-bottom: 10px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.joint-stencil .group.closed {
    height: auto;
    padding-bottom: 0;
}

.joint-stencil .group > .group-label {
    position: relative;
    padding: 5px 4px;
    margin-top: 0;
    margin-bottom: 0;
    cursor: pointer;
}

.joint-stencil .joint-element [magnet]:not([magnet="passive"]) {
    pointer-events: none;
}

/* .group.unmatched and .joint-element.unmatched */
.joint-stencil .unmatched {
    opacity: 0.3;
}

.joint-stencil .search {
    width: 100%;
    box-sizing: border-box;
    height: 30px;
    max-height: 30px;
    line-height: 30px;
    z-index: 1;
    outline: none;
    position: relative;
}

/* Not found popover. */

.joint-stencil:after {
    display: block;
    content: '';
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
    height: 20px;
    line-height: 20px;
    padding: 8px 0;
    text-align: center;
    opacity: 0;
    transition: top 100ms linear, opacity 100ms linear;
}

.joint-stencil.not-found:after {
    content: attr(data-text-no-matches-found);
    opacity: 1;
}

.joint-stencil.not-found.searchable:after {
    top: 30px;
}

.joint-stencil.not-found.searchable.collapsible:after {
    top: 50px;
}

.joint-stencil .groups-toggle .group-label:hover {
    cursor: pointer;
}

.joint-stencil .group > .group-label,
.joint-stencil .groups-toggle > .group-label {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.joint-stencil .groups-toggle > .group-label {
    padding: 0 5px 0 53px;
}

/* By default, unmatched elements are hidden. See below for making them opaque instead. */
.joint-stencil .joint-element.unmatched {
    display: none;
}
.joint-stencil .group.unmatched {
    display: none;
}

/*
Use the following in your custom CSS to make
unmatched elements opaque instead of completely invisible which is the default.
*/
/*
.joint-stencil .joint-element.unmatched {
    display: block;
}
.joint-stencil .group.unmatched {
    display: block;
}
*/

@font-face {
  font-family: 'stencil-icons-dark';
  src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-stencil.joint-theme-dark .joint-paper,
.joint-stencil.joint-theme-dark {
  color: #feffff;
  background: #5e6366;
}

.joint-stencil.joint-theme-dark > .content {
  position: absolute;
}

.joint-stencil.joint-theme-dark.searchable > .content{
  top: 90px;
}

.joint-stencil.joint-theme-dark.collapsible > .content {
  top: 30px;
}

.joint-stencil.joint-theme-dark.searchable.collapsible > .content {
  top: 110px;
}

.joint-stencil.joint-theme-dark.not-found:after {
  position: absolute;
}

.joint-stencil.joint-theme-dark.not-found.searchable.collapsible:after {
  top: 80px;
}

.joint-stencil.joint-theme-dark.not-found.searchable:after {
  top: 60px;
}

.joint-stencil.joint-theme-dark .group {
  height: auto;
  padding: 0;
  margin-bottom: 1px;
  transition: none;
}

.joint-stencil.joint-theme-dark .group > .joint-elements {
  margin: 0;
}

.joint-stencil.joint-theme-dark .group.closed {
  height: auto;
  max-height: 31px;
}

.joint-stencil.joint-theme-dark input[type="search"] {
  -webkit-appearance: textfield;
}

.joint-stencil.joint-theme-dark input[type="search"]::-webkit-search-decoration,
.joint-stencil.joint-theme-dark input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

.joint-stencil.joint-theme-dark .group > .group-label,
.joint-stencil.joint-theme-dark .groups-toggle > .group-label {
  position: relative;
  left: 0;
  width: 100%;
  height: 31px;
  line-height: 31px;
  color: #F5F5F5;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  box-sizing: border-box;
  border-bottom: 1px solid #383c3f;
  border-top: 1px solid #383c3f;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAAdCAYAAAA6lTUKAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AMWDTgSBLydUQAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAAP4SURBVFjDjVhBtuQgCCyI25nTzWYWc/9L/FCzUBCJprvfy2tjEBELCpU/f/8RAHgbKAAAiAhEBCQBAAqJb95HEqoabR9jZvHuv6zL+0lGH4BFl7+7rrCJgF4CY7e3mVkfBILGMCoPpipALJPmif1dVaPf9WZH1AXn/yxT9QLAfd+4RAFVkICB0KpAVRevZW/ld19g9bzL5vcsV3egjjn9rusKPe6YVo3frbp62+Xytvp3V5y9mfvevF6/VRsAWeZuoYCAAKBgxSI61lg8kWV2+K6O2DmnxsDJ+9FH9/rY+dhWnUG5YFUAqCwwqNu8g8IJdju4nOD5aXFavVi9u/v21veG25ORvlMZKnVxdUdJ9myzw15u3/cNWMoCggXHi1wxJqe9LHOKi6yrt11Xh3bv72PbLj1tsek7IYBAHlywC8RvdurzT8JYkjE3STTPzdnoXcqDueF7DJ/IaEd6GSJvYyocRftjRogAShpIO3osSAdP8tgRVpbZpdI6z+wnui3f62sVa9lTwbAbaOS268i7eJq4Mu6U8YXgyDUKtwkgDSqiA9F4MOMpJVYoCPcZZZcOd0zbZQTdlv3PGd1ZFkDONtwGSP+3x4Qkcd/WAxkE+YlNLfSv/a5PXhkWAG6zYWZfbMtePZHGjjWR9ss9904077rOpFb6BeFcnR80eV2i7c+E1/wGURCC285pL3u46l7bdQFuuM/p2Uk7SmhovTjmUj/EYokVUpnhbBCI10VeG9kKLQEA63ERpDN0uz4aITrn796aKxYRGBm1jeuNbGNmy5bmjGBmgAz8OUbBMU9vmSEyVA1yVQVhXS5h2pnT+wWTiVEqTaaFuV0tSGK4TmS6X2Q0BUtG2hZh/FyIVSKrmQkgOiqkP+DrnA3qtur58JFOSKcyQsYhJg+N7KRL1PVxFegbXUyxFD5Ncm3Hgn6aygS2Oyys3+xBUmsVeCYpkriuKxHeDdVrYd9+5ujOIQ0gp7ufpFEZVZZHREsG+kRUM8PkTPOJnHJSrra1yGGWIONtSl+0zUBZYGXEkq2MOSd2NuSUk9EO3OdicF5PxMmuZ8Q1qcdukGg+Xw9QwSiRYsH0+Enhw1Fj/9g9iCONG7IiMos5zv7+z5CFuEHusW7l7cWiX8eM6xeOYyAINJUc09yUt4SbtsYxZ2Z6nHaA56kXM8eP+PR8neVrXMRhBFyCXBSzPDhmkU1xtbun+eaIdzrfvpUmj/o/JYX2c9tmMicczgupYqQZo86Yhp3OtSk7GR9zzd1Kcsjn2Z5hhBIwgwENqIE4awgpeKxyeMCJh2sRHo+MKJCdi7DSLw8ktt+/fh3hcjqLflMZ1tR7Oh19um2oNwZ+VWNm+A8qy7Bl/n+ONAAAAABJRU5ErkJggg==') repeat;
}
.joint-stencil.joint-theme-dark .groups-toggle > .group-label {
  padding-left: 58px;
}
.joint-stencil.joint-theme-dark .group > .group-label {
  padding: 0 5px 0 34px;
  border-top: 1px solid #383c3f;
}


.joint-stencil.joint-theme-dark .groups-toggle > .group-label {
  position: absolute;
}

.joint-stencil.joint-theme-dark .groups-toggle .btn.btn-expand,
.joint-stencil.joint-theme-dark .groups-toggle .btn.btn-collapse {
  background: transparent;
  border: none;
  margin-top: 2px;
}

.joint-stencil.joint-theme-dark .groups-toggle .btn-expand:before,
.joint-stencil.joint-theme-dark .groups-toggle .btn-collapse:before,
.joint-stencil.joint-theme-dark .group > .group-label:before {
  position: absolute;
  color: #717276;
  font-family: stencil-icons-dark;
  font-style: normal;
  font-weight: 400;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  line-height: 1em;
  margin-left: .2em;
  font-size: 16px;
  text-shadow: 0px 1px #35393c;
}
.joint-stencil.joint-theme-dark .groups-toggle .btn-expand:before,
.joint-stencil.joint-theme-dark .groups-toggle .btn-collapse:before {
  left: 3px;
  top: 6px;
}
.joint-stencil.joint-theme-dark .group > .group-label:before {
  left: 5px;
  top: 7px;
}
.joint-stencil.joint-theme-dark .groups-toggle .btn {
  line-height: 20px;
  color: transparent;
  position: relative;
  display: inline-block;
  width: 23px;
}

.joint-stencil.joint-theme-dark .groups-toggle .btn:focus {
  outline: none;
}

.joint-stencil.joint-theme-dark .groups-toggle .btn:hover {
  cursor: pointer;
}

.joint-stencil.joint-theme-dark .groups-toggle .btn-collapse:before,
.joint-stencil.joint-theme-dark .group > .group-label:before {
  content: '\e80a';
}

.joint-stencil.joint-theme-dark .group > .group-label:hover,
.joint-stencil.joint-theme-dark .groups-toggle > .group-label:hover,
.joint-stencil.joint-theme-dark .groups-toggle .btn-expand:hover:before,
.joint-stencil.joint-theme-dark .groups-toggle .btn-collapse:hover:before,
.joint-stencil.joint-theme-dark .group.closed > .group-label:hover:before,
.joint-stencil.joint-theme-dark .group > .group-label:hover:before {
  color: #8b9094;
}
.joint-stencil.joint-theme-dark .groups-toggle .btn-expand:before,
.joint-stencil.joint-theme-dark .group.closed > .group-label:before {
  content: '\e80b';
}

.joint-stencil.joint-theme-dark .search {
  display: block;
  width: 90%;
  color: #24282b;
  background: #92979b;
  background: -webkit-linear-gradient(#8b9094, #92979b);
  background: -o-linear-gradient(#8b9094, #92979b);
  background: -moz-linear-gradient(#8b9094, #92979b);
  background: linear-gradient(#8b9094, #92979b);
  border: 1px solid #42474a;
  border-radius: 3px;
  outline: none;
  padding-left: 8px;
  margin: 30px 5% 24px 5%;
}

.joint-stencil.joint-theme-dark .search::-webkit-input-placeholder { /* WebKit, Blink, Edge */
  color: #444549;
}

.joint-stencil.joint-theme-dark .search:-moz-placeholder { /* Mozilla Firefox  */
  color: #444549;
  opacity:  1;
}

.joint-stencil.joint-theme-dark .search:-ms-input-placeholder { /* Internet Explorer 10-11 */
  color: #444549;
}

.joint-stencil.joint-theme-dark .search:placeholder-shown { /* Standard (https://drafts.csswg.org/selectors-4/#placeholder) */
  color: #444549;
}

.joint-stencil.joint-theme-dark .search:focus {
  outline: none;
}

.joint-stencil.joint-theme-dark:after {
  font-size: 12px;
  font-weight: 700;
  background: transparent;
  color: #92979b;
}

.joint-stencil.joint-theme-default {
    color: black;
    background: white;
    border: 1px solid lightgrey;
}

.joint-stencil.joint-theme-default.collapsible > .content {
    top: 20px;
}

.joint-stencil.joint-theme-default.collapsible.searchable > .content {
    top: 52px;
}

.joint-stencil.joint-theme-default .group.closed {
    height: auto;
    max-height: 24px;
}

.joint-stencil.joint-theme-default .groups-toggle {
    line-height: 20px;
}

.joint-stencil.joint-theme-default .group > .group-label {
    text-transform: uppercase;
    font-size: 10px;
}

.joint-stencil.joint-theme-default .group:first-child {
    border-top: 1px solid lightgrey;
}

.joint-stencil.joint-theme-default .group {
    border-bottom: 1px solid lightgrey;
}

.joint-stencil.joint-theme-default .groups-toggle > .group-label {
    display: block;
}

/* arrow */
.joint-stencil.joint-theme-default .group > .group-label:before {
    position: relative;
    top: 5px;
    content: '';
    width: 0;
    height: 0;
    display: inline-block;
    border: 5px solid transparent;
    border-top-color: black;
    margin-left: 2px;
    margin-right: 5px;
}

.joint-stencil.joint-theme-default .group.closed > .group-label:before {
    top: 2px;
    left: 2px;
    border: 5px solid transparent;
    border-left-color: black;
}

/* input search field */
.joint-stencil.joint-theme-default .search {
    color: black;
    border: none;
    background: white;
}

/* no matches found - search */
.joint-stencil.joint-theme-default:after {
    font-size: 12px;
    border-top: 1px solid lightgrey;
    background: white;
    color: black;
}

/* expand, collapse buttons */
.joint-stencil.joint-theme-default .groups-toggle .btn {
    position: absolute;
    top: 2px;
    left: 2px;
    background: none;
    outline: none;
    color: black;
    cursor: pointer;
    width: 20px;
    height: 16px;
    line-height: 12px;
    margin: 0;
    padding: 0;
    border: 1px solid lightgrey;
    border-radius: 4px;
}

.joint-stencil.joint-theme-default .groups-toggle .btn-collapse {
    left: 25px;
}

.joint-stencil.joint-theme-default .groups-toggle .btn:focus {
    outline: none;
}

.joint-stencil.joint-theme-default .groups-toggle .btn:hover {
    cursor: pointer;
    background: lightgrey;
}

.joint-stencil.joint-theme-material {
    color: #55627b;
    background: #717d98;
    font-family: lato-light;
}

.joint-stencil.joint-theme-material > .content {
    position: absolute;
}

.joint-stencil.joint-theme-material.searchable > .content{
    top: 48px;
    border: none;
    background: #ecf0f8;
}

.joint-stencil.joint-theme-material.collapsible > .content {
    top: 30px;
}

.joint-stencil.joint-theme-material.searchable.collapsible > .content {
    top: 80px;
}

.joint-stencil.joint-theme-material.not-found:after {
    position: absolute;
}

.joint-stencil.joint-theme-material.not-found.searchable.collapsible:after {
    top: 80px;
}

.joint-stencil.joint-theme-material.not-found.searchable:after {
    top: 60px;
}

.joint-stencil.joint-theme-material .group {
    height: auto;
    max-height: 5000px;
    padding: 0;
    margin-bottom: 1px;
    transition: max-height 0.25s cubic-bezier(0.5, 0, 1, 0) -.1s;
	transition-delay: 0s;
}

.joint-stencil.joint-theme-material .group > .elements {
    background: #ecf0f8;
    margin: 0;
}

.joint-stencil.joint-theme-material .group.closed {
    height: auto;
    max-height: 31px;
    overflow: hidden;
	transition: max-height 0.25s cubic-bezier(0, 1, 0, 1) -.1s;
}

.joint-stencil.joint-theme-material input[type="search"] {
    -webkit-appearance: textfield;
    appearance: textfield;
}

.joint-stencil.joint-theme-material input[type="search"]::-webkit-search-decoration,
.joint-stencil.joint-theme-material input[type="search"]::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.joint-stencil.joint-theme-material input[type="search"]::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: #d2d7e2;
}

.joint-stencil.joint-theme-material input[type="search"]:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
   color: #d2d7e2;
   opacity: 1;
}

.joint-stencil.joint-theme-material input[type="search"]::-moz-placeholder { /* Mozilla Firefox 19+ */
   color: #d2d7e2;
   opacity: 1;
}

.joint-stencil.joint-theme-material input[type="search"]:-ms-input-placeholder { /* Internet Explorer 10-11 */
   color: #d2d7e2;
}

.joint-stencil.joint-theme-material .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #55627b;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
}

.joint-stencil.joint-theme-material .group > .group-label {
    padding: 0 5px 0 10px;
    background: #ecf0f8;
}
.joint-stencil.joint-theme-material .group:not(.closed) > div:after,
.joint-stencil.joint-theme-material .group > .group-label:after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: -3px;
}
.joint-stencil.joint-theme-material .group:not(.closed) > div:after,
.joint-stencil.joint-theme-material .group > .group-label:after {
  right: 0px;
  height: 4px;
  background-image: -webkit-gradient(linear, 0 0, #dfe3f0 0, from(transparent), to(#dfe3f0));
  background-image: -webkit-linear-gradient(right, #dfe3f0, transparent);
  background-image: -moz-linear-gradient(right, #dfe3f0, transparent);
  background-image: -o-linear-gradient(right, #dfe3f0, transparent);
}

.joint-stencil.joint-theme-material .group > .group-label:hover,
.joint-stencil.joint-theme-material .groups-toggle > .group-label:hover {
    color: #5faaee;
}
.joint-stencil.joint-theme-material .group > .group-label:hover {
    border-right: 5px solid #5fa9ee;
}
.joint-stencil.joint-theme-material .search {
    display: block;
    width: 90%;
    color: #ecf0f8;
    background: #717d98;
    border: none;
    border-bottom: 1px solid #ecf0f8;
    outline: none;
    padding-left: 8px;
    background: url('data:image/png;base64,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') no-repeat right center;
}

.joint-stencil.joint-theme-material .search:focus {
    outline: none;
}

.joint-stencil.joint-theme-material:after {
    font-size: 12px;
    font-weight: 700;
    background: transparent;
    color: #55627b;
}

.joint-stencil.joint-theme-material .groups-toggle > .group-label {
    padding: 0 5px 0 10px;
    position: relative;
    float: left;
    color: white;
    padding: 10px;
    font-weight: bold;
}

.joint-stencil.joint-theme-material .groups-toggle .btn {
    line-height: 25px;
    position: relative;
    display: inline-block;
    width: 28px;
    right: 0px;
    margin-left: 2px;
    float: right;
    cursor: pointer;
    background: #828da6;
    color: #deebfb;
    font-weight: bold;
    font-size: 17px;
    border: none;
    outline: none;
}

.joint-stencil.joint-theme-material .btn:hover {
    background-color: #5fa9ee;
}
.joint-stencil.joint-theme-material.searchable .search-wrap {
    position: relative;
    margin: 8px 5%;
}
.joint-stencil.joint-theme-material.searchable .search-wrap:after {
    background: #3f51b5;
    bottom: 0px;
    content: '';
    height: 2px;
    left: 40%;
    position: absolute;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    z-index: -1;
    width: 10px;
    transition-property: width, left, z-index;
    display: block;
}

.joint-stencil.joint-theme-material.searchable.is-focused .search-wrap:after {
    z-index: 1000;
    left: 0;
    width: 90%;
}
.joint-stencil.joint-theme-material.searchable .groups-toggle {
    height: 30px;
}

.joint-stencil.joint-theme-modern {
  color: #c6c7e2;
  background: #383b61;
}

.joint-stencil.joint-theme-modern > .content {
  position: absolute;
}

.joint-stencil.joint-theme-modern.searchable > .content{
  top: 48px;
}

.joint-stencil.joint-theme-modern.collapsible > .content {
  top: 30px;
}

.joint-stencil.joint-theme-modern.searchable.collapsible > .content {
  top: 70px;
}

.joint-stencil.joint-theme-modern.not-found:after {
  position: absolute;
}

.joint-stencil.joint-theme-modern.not-found.searchable.collapsible:after {
  top: 80px;
}

.joint-stencil.joint-theme-modern.not-found.searchable:after {
  top: 60px;
}

.joint-stencil.joint-theme-modern .group {
  height: auto;
  padding: 0;
  margin-bottom: 1px;
  transition: none;
}

.joint-stencil.joint-theme-modern .group > .joint-elements {
  background: #4a4d6e;
  margin: 0;
}

.joint-stencil.joint-theme-modern .group.closed {
  height: auto;
  max-height: 31px;
}

.joint-stencil.joint-theme-modern input[type="search"] {
  -webkit-appearance: textfield;
}

.joint-stencil.joint-theme-modern input[type="search"]::-webkit-search-decoration,
.joint-stencil.joint-theme-modern input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}

.joint-stencil.joint-theme-modern .group > .group-label,
.joint-stencil.joint-theme-modern .groups-toggle > .group-label {
  position: relative;
  left: 0;
  width: 100%;
  height: 31px;
  line-height: 31px;
  color: #9093b1;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  box-sizing: border-box;
}

.joint-stencil.joint-theme-modern .group > .group-label {
  padding: 0 5px 0 34px;
  background: #424568;
}


.joint-stencil.joint-theme-modern .groups-toggle > .group-label {
  position: absolute;
}

.joint-stencil.joint-theme-modern .group > .group-label:hover,
.joint-stencil.joint-theme-modern .groups-toggle > .group-label:hover {
  color: #d8d8ec;
}

.joint-stencil.joint-theme-modern .groups-toggle .btn-expand,
.joint-stencil.joint-theme-modern .groups-toggle .btn-collapse,
.joint-stencil.joint-theme-modern .group > .group-label:before {
  position: absolute;
  left: 5px;
  top: 6px;
  display: block;
  width: 19px;
  height: 19px;
  background-color: transparent;
  background-position: 0 0;
  background-repeat: no-repeat;
  border: none;
  content: ' ';
  margin: 0;
  padding: 0;
}

.joint-stencil.joint-theme-modern .groups-toggle .btn {
  line-height: 30px;
  color: transparent;
  position: relative;
  display: inline-block;
}

.joint-stencil.joint-theme-modern .groups-toggle .btn:focus {
  outline: none;
}

.joint-stencil.joint-theme-modern .groups-toggle .btn:hover {
  cursor: pointer;
}

.joint-stencil.joint-theme-modern .groups-toggle .btn-expand {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .groups-toggle .btn-expand:hover {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .groups-toggle .btn-collapse {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .groups-toggle .btn-collapse:hover {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .group > .group-label:before {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .group > .group-label:hover:before {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .group.closed > .group-label:before {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .group.closed > .group-label:hover:before {
  background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-stencil.joint-theme-modern .search {
  display: block;
  width: 90%;
  color: #d8d8ec;
  background: #3a3c62;
  border: 1px solid #525676;
  border-radius: 12px;
  outline: none;
  padding-left: 8px;
  margin: 8px 5%;
}

.joint-stencil.joint-theme-modern .search:focus {
  outline: none;
}

.joint-stencil.joint-theme-modern:after {
  font-size: 12px;
  font-weight: 700;
  background: transparent;
  color: #d8d8ec;
}

.joint-inspector {
   position: absolute;
   top: 0;
   bottom: 0;
   right: 0;
   left: 0;
   overflow: auto;
}

.joint-inspector label {
   display: block;
   margin-top: 5px;
   margin-bottom: 10px;
}
.joint-inspector label:after {
    content: ':';
}
.joint-inspector input,
.joint-inspector textarea {
   width: 200px;
   height: 20px;
   line-height: 20px;
}

.joint-inspector label.with-output {
    float: left;
}

.joint-inspector output {
    float: left;
    margin: 5px 2px 10px 2px;
}

.joint-inspector .units {
    float: left;
    margin: 5px 0 10px 0;
}

.joint-inspector input[type="range"] {
   clear: both;
   display: block;
}

.joint-inspector select {
   display: block;
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE10 only */
    .joint-inspector .group > .field > form {
	height: 60px;
    }

    .joint-inspector input[type="range"] {
	height: 10px;
    }

    .joint-inspector input[type="range"]::-ms-tooltip {
	display: none;
    }
}

.joint-inspector .group {
    overflow: hidden;
    padding: 0;
    padding-bottom: 10px;
}
.joint-inspector .group.closed {
    height: auto;
    padding-bottom: 0;
}
.joint-inspector .group.empty {
    display: none;
}

/* prevent tabbing into a close group */
.joint-inspector .group.closed .field {
   display: none;
}

.joint-inspector .group > .group-label {
   position: relative;
   padding: 5px 4px;
   margin-top: 0;
   margin-bottom: 0;
   cursor: pointer;
   -webkit-user-select: none;
   -moz-user-select: none;
   -ms-user-select: none;
   user-select: none;
}
.joint-inspector .group > .group-label:before {
   content: '';
   width: 0;
   height: 0;
   display: inline-block;
   margin-left: 2px;
   margin-right: 5px;
   position: relative;
   top: 5px;
}
.joint-inspector .group.closed > .group-label:before {
   top: 2px;
   left: 2px;
}

.link-tools .tool-options {
   display: block;
}

/* Toggle */

.joint-inspector .toggle {
   position: relative;
   width: 97px;
   height: 14px;
}
.joint-inspector .toggle input {
   top: 0;
   right: 0;
   bottom: 0;
   left: 0;
   -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
   filter: alpha(opacity=0);
   -moz-opacity: 0;
   opacity: 0;
   z-index: 100;
   position: absolute;
   width: 100%;
   height: 100%;
   cursor: pointer;
   box-sizing: border-box;
   padding: 0;
   box-shadow: none;
   -webkit-appearance: none;
}
.joint-inspector .toggle span {
   display: block;
   width: 100%;
   height: 100%;
   border-radius: 40px;
   position: relative;
}
.joint-inspector .toggle span:before {
   box-sizing: border-box;
   padding: 0;
   margin: 0;
   content: "";
   position: absolute;
   z-index: -1;
   top: -18px;
   right: -18px;
   bottom: -18px;
   left: -18px;
   border-radius: inherit;
}
.joint-inspector .toggle input:checked + span i {
   right: 0;
}
.joint-inspector .toggle span i {
   display: block;
   height: 100%;
   width: 60%;
   border-radius: inherit;
   position: absolute;
   z-index: 2;
   right: 40%;
   top: 0;
}
.joint-inspector .btn-list-add,
.joint-inspector .btn-list-del {
   cursor: pointer;
   border-radius: 2px;
   min-width: 23px;
   margin: 2px;
   margin-right: 8px;
}

.joint-inspector .list-items {
   margin-top: 4px;
}
.joint-inspector .list-item {
   margin-top: 2px;
   padding: 10px;
}

.joint-inspector .list-item > .field > label {
   display: none;
}

.joint-inspector .field {
    display: block;
    box-sizing: border-box;
    padding: 4px 10px;
}

.joint-inspector .hidden {
   display: none !important;
}

/* Built-in types */

.joint-inspector .joint-select-box {
    width: 100%;
}
.joint-inspector .joint-color-palette {
    width: auto;
}

.joint-inspector .content-editable {
    white-space: pre-wrap;
    /* Safari & Firefox: `user-select: none` is preventing the cursor
    from ever being positioned within the contentEditable div */
    -webkit-user-select: auto;
    -moz-user-select: text;
}

/* IE: content-editable adds <p> tags, so remove their default spacing */
.joint-inspector .content-editable * {
    margin: 0;
    padding: 0;
}

@font-face {
    font-family: 'inspector-icons-dark';
    src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-inspector.joint-theme-dark {
    color: #dddfde;
    background: #5e6366;
}

.joint-inspector.joint-theme-dark label {
    font-size: 12px;
    padding-right: 4px;

}
.joint-inspector.joint-theme-dark label,
.joint-inspector.joint-theme-dark output,
.joint-inspector.joint-theme-dark .units {
    text-transform: uppercase;
    text-shadow: 0px 1px 1px #313538;
    font-size: 12px;
    margin-bottom: 6px;
}
.joint-inspector.joint-theme-dark select.select {
    margin: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAgCAYAAACcuBHKAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AMYCRwNn4qu7QAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAAPrSURBVFjDjZfPbu00EMZ/MzFSuwB2FxXeoW/RJ2CBxFsDUqWWQtUKaLut1PagE8+wcOwzdpJ7iRQlcezx/Pnmm7H8+NPPfnl5ycfhgDuYgJgjIrg7qoq7417G6lW/67O+xzXxquMAGWt7vLy8kKoAKAq4OxqExo2B/zVuAohgZqd/AMsSQXAFUeXi4oKkXhak9BVuGXXIeFk0Ka6CLxKaVyzjzqKIgAguAlIsnQZPVAWnacLMUBdEi6HZMgnAzMg5Y27gYHjnZjNrri6uLR6rlqoqqroxz7vvKotFv2pUqsLMDMeLhe4sNqIObo4vi0WkalG9i/qCo2WesITmpHExaJmTzToc6Rjbat0Iqs9917Gydu2FiLs4v96pE+KGDsgflYru7VwcviMORiUjqFs2xcV72o5X3Gg93mNoTN8tI1qKiggqitBzQgVcvSJvbIXE3UkpdaGNe8RUrrJbdpgZ2Q11VgKiG4tbM7Kk45hFAPM8d/+qvGmaWliiMalaZGaI+8IQsopjBGR5eBf3MTRfAnV86hjfMV4jVkaPFGXAvWaGb8oasRPlphjr4hHfpOM6p7r9+fmJj/f3FVbOz8+5+P6HbrOoTJ0XFUkAR8vM87y43oB+YQ2JLSQDcHV1xXefPiGWC8AE3EtB+uXX3zYzrCqUc27AbOEYU6dqOk1T8FCPjdvbW3LOTbh6mXNzc7Pigb1wN0xU2h1jHrNmJBgz4+3tjcfHxxaejPDw8MD7x+GLCozfukWj8Y4e6C0R7u/veTv8y+xwOBz448/7TUKKym/J0z3E7qWciOBa/s3HzN3dHarK73d3zPPc/m1l1p681NoMAVfBTJjo87+6vDGrLf2GCn/9/Q9ff/MtT0/PJWwOkmTVjY0UHoGaAHLOHI9HMqUk5w2uH7Mjhur6+rpz7/F43CxyI1G1fqKrHealSxryeGzftqza60WjNyNHxGuFiXHS+F5Ttlo0TVPn7tplbVdYVrIaJrZ4Ys/C0a0557Zx5Y1RRtfwhgLWYeKUOvv5HTcbO7CYfnvd00hgOedWVTd5YqwbY86Pm8VY13+jMRE/8WlmfY85hiTn3FFzpO+oWM550+I9b63au9IVO9FYcVAERRAHzMEcz1Z4YIOAohIjD3yuOW7AjOeG0TPI0j/XlFVFBqCN6I9g3lKs7pNSIufl8DPPcyuvng10fa6s7yMAbThDbIVs611EyoHLjFS5viqjAm777h7xsLVRxdF45ihzrM3pTmDxBI7bbnt/IrNYlvu71qJ1P+ntYBRJb5om0tnZGaraDqvt6HyyFRFQFVTLuSKS0lblVZ0aIVUZpUhWQ8poSonX11f+A300zYI9/iS2AAAAAElFTkSuQmCC') right center no-repeat,
        linear-gradient(#8b9094, #92979b);
}

.joint-inspector.joint-theme-dark option {
    color: #222;
}

.joint-inspector.joint-theme-dark output,
.joint-inspector.joint-theme-dark .units {
    font-weight: 700;
}

.joint-inspector.joint-theme-dark .group {
    height: auto;
    padding: 0;
    padding-bottom: 20px;
    margin-bottom: 1px;
}
.joint-inspector.joint-theme-dark .group.closed {
    height: auto;
    max-height: 31px;
    padding: 0;
}

.joint-inspector.joint-theme-dark .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #F5F5F5;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
    border-bottom: 1px solid #383c3f;
    border-top: 1px solid #383c3f;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC8AAAAdCAYAAAA6lTUKAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AMWDTgSBLydUQAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAAP4SURBVFjDjVhBtuQgCCyI25nTzWYWc/9L/FCzUBCJprvfy2tjEBELCpU/f/8RAHgbKAAAiAhEBCQBAAqJb95HEqoabR9jZvHuv6zL+0lGH4BFl7+7rrCJgF4CY7e3mVkfBILGMCoPpipALJPmif1dVaPf9WZH1AXn/yxT9QLAfd+4RAFVkICB0KpAVRevZW/ld19g9bzL5vcsV3egjjn9rusKPe6YVo3frbp62+Xytvp3V5y9mfvevF6/VRsAWeZuoYCAAKBgxSI61lg8kWV2+K6O2DmnxsDJ+9FH9/rY+dhWnUG5YFUAqCwwqNu8g8IJdju4nOD5aXFavVi9u/v21veG25ORvlMZKnVxdUdJ9myzw15u3/cNWMoCggXHi1wxJqe9LHOKi6yrt11Xh3bv72PbLj1tsek7IYBAHlywC8RvdurzT8JYkjE3STTPzdnoXcqDueF7DJ/IaEd6GSJvYyocRftjRogAShpIO3osSAdP8tgRVpbZpdI6z+wnui3f62sVa9lTwbAbaOS268i7eJq4Mu6U8YXgyDUKtwkgDSqiA9F4MOMpJVYoCPcZZZcOd0zbZQTdlv3PGd1ZFkDONtwGSP+3x4Qkcd/WAxkE+YlNLfSv/a5PXhkWAG6zYWZfbMtePZHGjjWR9ss9904077rOpFb6BeFcnR80eV2i7c+E1/wGURCC285pL3u46l7bdQFuuM/p2Uk7SmhovTjmUj/EYokVUpnhbBCI10VeG9kKLQEA63ERpDN0uz4aITrn796aKxYRGBm1jeuNbGNmy5bmjGBmgAz8OUbBMU9vmSEyVA1yVQVhXS5h2pnT+wWTiVEqTaaFuV0tSGK4TmS6X2Q0BUtG2hZh/FyIVSKrmQkgOiqkP+DrnA3qtur58JFOSKcyQsYhJg+N7KRL1PVxFegbXUyxFD5Ncm3Hgn6aygS2Oyys3+xBUmsVeCYpkriuKxHeDdVrYd9+5ujOIQ0gp7ufpFEZVZZHREsG+kRUM8PkTPOJnHJSrra1yGGWIONtSl+0zUBZYGXEkq2MOSd2NuSUk9EO3OdicF5PxMmuZ8Q1qcdukGg+Xw9QwSiRYsH0+Enhw1Fj/9g9iCONG7IiMos5zv7+z5CFuEHusW7l7cWiX8eM6xeOYyAINJUc09yUt4SbtsYxZ2Z6nHaA56kXM8eP+PR8neVrXMRhBFyCXBSzPDhmkU1xtbun+eaIdzrfvpUmj/o/JYX2c9tmMicczgupYqQZo86Yhp3OtSk7GR9zzd1Kcsjn2Z5hhBIwgwENqIE4awgpeKxyeMCJh2sRHo+MKJCdi7DSLw8ktt+/fh3hcjqLflMZ1tR7Oh19um2oNwZ+VWNm+A8qy7Bl/n+ONAAAAABJRU5ErkJggg==') repeat;
    padding: 0 5px 0 30px;
    border-top: 1px solid #383c3f;
}

.joint-inspector.joint-theme-dark .group > .group-label:before {
    position: absolute;
    left: 5px;
    top: 7px;
    color: #717276;
    font-family: inspector-icons-dark;
    font-style: normal;
    font-weight: 400;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: .2em;
    font-size: 16px;
    text-shadow: 0px 1px #35393c;
}

.joint-inspector.joint-theme-dark .group > .group-label:before {
    content: '\e80a';
}

.joint-inspector.joint-theme-dark .group.closed > .group-label:hover:before,
.joint-inspector.joint-theme-dark .group > .group-label:hover:before {
    color: #8b9094;
}

.joint-inspector.joint-theme-dark .group.closed > .group-label:before {
    content: '\e80b';
}

.joint-inspector.joint-theme-dark .toggle {
    width: 72px;
}
.joint-inspector.joint-theme-dark .toggle input {
    display: block;
    width: 100%;
    box-sizing: border-box;
    box-shadow: none;
    height: 12px;
}
.joint-inspector.joint-theme-dark .toggle span,
.joint-inspector.joint-theme-dark .toggle input:checked + span {
    background: #8b9094;
}
.joint-inspector.joint-theme-dark .toggle span {
    border-radius: 3px;
    box-shadow: none;
}
.joint-inspector.joint-theme-dark .toggle span:before {
    background: #f6f6f6;
    box-shadow: none;
}
.joint-inspector.joint-theme-dark .toggle span i:before {
    content: "off";
    position: absolute;
    right: -50%;
    top: 0;
    text-transform: uppercase;
    font-style: normal;
    font-weight: bold;
    color: #f5f5f5;
    font-family: Arial, sans-serif;
    font-size: 10px;
    line-height: 16px;
    margin-top: -1px;
    margin-right: -8px;
}
.joint-inspector.joint-theme-dark .toggle input:checked + span i:before {
    content: "on";
    right: 100%;
    color: #f5f5f5;
    margin-right: 12px;
}
.joint-inspector.joint-theme-dark .toggle span i {
    right: 50%;
    width: 50%;
    background: #414548;
    box-shadow: 0 0 3px #8b9094;
}

.joint-inspector.joint-theme-dark .btn-list-add,
.joint-inspector.joint-theme-dark .btn-list-del {
    background: transparent;
    color: #fff;
    border: 1px solid gray;
    box-shadow: 1px 1px 1px #000;
}
.joint-inspector.joint-theme-dark .btn-list-add:hover,
.joint-inspector.joint-theme-dark .btn-list-del:hover {
    box-shadow: inset 1px 1px 1px #000;
}

.joint-inspector.joint-theme-dark .joint-select-box {
    color: #000;
}

.joint-inspector.joint-theme-dark .joint-select-box.joint-color-palette.joint-theme-dark div.select-box-selection {
    color: #ddd;
    border: 1px solid #8b9094;
}

.joint-select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-dark .select-box-selection div.select-box-option-content:after {
    left: 5px;
    top: 5px;
}

.joint-inspector.joint-theme-dark .group .field {
    box-sizing: border-box;
    padding: 0 18px;
    margin-top: 12px;
}

.joint-inspector.joint-theme-dark input {
    width: 100%;
    height: auto;
    text-shadow: none;
    box-shadow: none;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0;
}

.joint-inspector.joint-theme-dark input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 20px;
    margin: 6px 0 0 0;
    background: transparent;
}

.joint-inspector.joint-theme-dark input[type="range"]:focus {
    outline: none;
}

.joint-inspector.joint-theme-dark input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-inspector.joint-theme-dark input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
    margin-top: -2px;
}

.joint-inspector.joint-theme-dark input[type="range"]::-ms-thumb {
    margin-top: 0;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
}

.joint-inspector.joint-theme-dark input[type="range"]::-moz-range-thumb {
    -moz-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
}

.joint-inspector.joint-theme-dark input[type="range"]::-ms-fill-lower,
.joint-inspector.joint-theme-dark input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #726bae, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}

.joint-inspector.joint-theme-dark input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #726bae, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}

.joint-inspector.joint-theme-dark input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #726bae, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}


.joint-inspector.joint-theme-dark input[type="text"],
.joint-inspector.joint-theme-dark input[type="number"],
.joint-inspector.joint-theme-dark textarea,
.joint-inspector.joint-theme-dark .content-editable,
.joint-inspector.joint-theme-dark select {
    width: 100%;
    height: auto;
    line-height: 16px;
    text-shadow: none;
    box-shadow: none;
    box-sizing: border-box;
    outline: none;
    padding: 6px 10px;
    overflow: auto;

    color: #24282b;
    background: #92979b;
    background: -webkit-linear-gradient(#8b9094, #92979b);
    background: -o-linear-gradient(#8b9094, #92979b);
    background: -moz-linear-gradient(#8b9094, #92979b);
    background: linear-gradient(#8b9094, #92979b);
    border: 1px solid #42474a;
    border-radius: 3px;
}

.joint-inspector.joint-theme-dark .content-editable {
    width: calc(100% - 12px); /* twice the padding from above */
    box-sizing: content-box;
    min-height: 1em;
}

.joint-inspector.joint-theme-dark input[type="text"],
.joint-inspector.joint-theme-dark input[type="number"],
.joint-inspector.joint-theme-dark select:not([multiple]) {
    height: 33px;
}
/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    /* change "6px padding" for visible text and same size as other browser */
    .joint-inspector.joint-theme-dark input[type="text"],
    .joint-inspector.joint-theme-dark input[type="number"] {
        padding: 0 0 0 10px;
    }
    /* "on/off" text in the center of the button  */
    .joint-inspector.joint-theme-dark .toggle span i:before {
        margin-top: 0;
    }
}

.joint-inspector.joint-theme-dark option {
    background: #fff;
    padding: 0 10px
}

.joint-inspector.joint-theme-dark input[type="color"] {
    width: 40px;
    height: 40px;
}

/*  Select Box  */
.joint-inspector .joint-select-box.joint-color-palette.joint-theme-dark .select-box-option-content {
    border: none;
}
.joint-inspector .joint-select-box.joint-theme-dark[data-type="select-button-group"] .select-box-selection,
.joint-inspector .joint-select-button-group.joint-theme-dark[data-type="select-button-group"] .select-button-group-button.selected,
.joint-inspector .joint-select-button-group.joint-theme-dark[data-type="select-button-group"] .select-button-group-button {
    color: #feffff;
    text-align: center;
    border-radius: 4px;
    border: 2px solid transparent;
}
.joint-inspector .joint-select-button-group.joint-theme-dark[data-type="select-button-group"] .select-button-group-button.selected {
    border: 2px solid #feffff;
}
.joint-inspector .joint-select-box.joint-theme-dark[data-type="select-button-group"] .select-box-selection,
.joint-inspector .joint-select-button-group.joint-theme-dark.disabled[data-type="select-button-group"] .select-button-group-button.selected,
.joint-inspector .joint-select-button-group.joint-theme-dark.disabled[data-type="select-button-group"] .select-button-group-button {
    color: #8b9094;
}
.joint-inspector .joint-select-button-group.joint-theme-dark.disabled[data-type="select-button-group"] .select-button-group-button.selected {
    border: 2px solid #8b9094;
}

/*  Select Box  */


/*  Lists  */
.joint-inspector.joint-theme-dark .list-item {
    background: #414548;
    padding: 16px;
    border: 1px solid rgb(36, 36, 36);
    box-shadow: inset 0 0 2px gray;
}
.joint-inspector.joint-theme-dark .list-item .field {
    padding: 0;
}
/*  Lists  */

.joint-inspector.joint-theme-default {
    color: black;
    background: white;
    border: 1px solid lightgrey;
}

.joint-inspector.joint-theme-default label {
    font-size: 12px;
    margin-bottom: 10px;
}

.joint-inspector.joint-theme-default output,
.joint-inspector.joint-theme-default .units {
    font-size: 12px;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-default .group > .group-label {
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
}
.joint-inspector.joint-theme-default .group {
    border-bottom: 1px solid lightgrey;
}
/* arrow */
.joint-inspector.joint-theme-default .group > .group-label:before {
    border-top: 5px solid black;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    border-bottom: 5px solid transparent;
}
.joint-inspector.joint-theme-default .group.closed > .group-label:before {
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-right: 5px solid transparent;
    border-left: 5px solid black;
}

/* toggle */
.joint-inspector.joint-theme-default .toggle {
    width: 60px;
}
.joint-inspector.joint-theme-default .toggle input:checked + span {
    background: white;
}
.joint-inspector.joint-theme-default .toggle span {
    background: lightgrey;
    border: 1px solid lightgrey;
    border-radius: 40px;
}
.joint-inspector.joint-theme-default .toggle input:checked + span i:before {
    content: "on";
    right: 115%;
    color: black;
}
.joint-inspector.joint-theme-default .toggle span i:before {
    content: "off";
    position: absolute;
    top: 50%;
    margin-top: -5px;
    right: -80%;
    text-transform: uppercase;
    color: black;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 10px;
    font-style: normal;
}
.joint-inspector.joint-theme-default .toggle span i {
    background: white;
    width: 50%;
    right: 50%;
}
.joint-inspector.joint-theme-default .toggle input:checked + span i {
    background: lightgrey;
}

.joint-inspector.joint-theme-default .btn-list-add,
.joint-inspector.joint-theme-default .btn-list-del {
    background: transparent;
    color: black;
    border: 1px solid lightgrey;
}

.joint-inspector.joint-theme-default .list-item {
    border: 1px solid lightgrey;
}

.joint-inspector.joint-theme-default input {
    width: 100%;
    height: 15px;
    text-shadow: none;
    box-shadow: none;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0;
}

.joint-inspector.joint-theme-default input[type="range"] {
    background: transparent;
    position: relative;
    height: 20px;
    border: none;
    outline: none;
    padding: 0;
}

.joint-inspector.joint-theme-default input[type="range"]::-ms-thumb {
    position: relative;
    width: 6px;
    height: 12px;
    top: 0;
    z-index: 2;
    border: 1px solid lightgrey;
    background: white;
}

.joint-inspector.joint-theme-default input[type="range"]::-ms-track {
    position: absolute;
    left: 0;
    top: 9px;
    content: ' ';
    width: 100%;
    height: 2px;
    background: lightgrey;
    border-color: transparent;
    border-radius: 3px;
    color: transparent;
}

.joint-inspector.joint-theme-default input[type="range"]::-ms-fill-lower {
    background: transparent;
    border-color: transparent;
}

.joint-inspector.joint-theme-default input[type="range"]:focus {
    outline: none;
}

.joint-inspector.joint-theme-default input[type="text"],
.joint-inspector.joint-theme-default input[type="number"],
.joint-inspector.joint-theme-default textarea,
.joint-inspector.joint-theme-default .content-editable,
.joint-inspector.joint-theme-default select,
.joint-inspector.joint-theme-default option {
    width: 100%;
    height: auto;
    line-height: 16px;
    background: transparent;
    border: 1px solid lightgrey;
    box-sizing: border-box;
    outline: none;
    padding: 5px;
}

.joint-inspector.joint-theme-default .content-editable {
    width: calc(100% - 10px); /* twice the padding from above */
    box-sizing: content-box;
    min-height: 1em;
}

.joint-inspector.joint-theme-default input[type="color"] {
    width: 40px;
    height: 40px;
}

.joint-inspector.joint-theme-default select:not([multiple]) {
    height: 28px
}

.joint-inspector.joint-theme-material {
    color: #55627b;
    background: #ecf0f8;
    font-family: lato-light, Arial, sans-serif;
}

.joint-inspector.joint-theme-material label {
    font-size: 12px;
    padding-right: 4px;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-material input,
.joint-inspector.joint-theme-material .content-editable,
.joint-inspector.joint-theme-material textarea {
    color: #55627b;
    border: none;
    outline: none;
    background: transparent;
}
.joint-inspector.joint-theme-material input[type="text"],
.joint-inspector.joint-theme-material input[type="number"],
.joint-inspector.joint-theme-material .content-editable,
.joint-inspector.joint-theme-material textarea {
    border-bottom: 2px solid #5fa9ee;
    padding: 4px 0;
    line-height: 16px;
}

.joint-inspector.joint-theme-material .content-editable {
    width: calc(100% - 8px); /* twice the padding from above */
    box-sizing: content-box;
    min-height: 1em;
}
.joint-inspector.joint-theme-material textarea {
    width: 100%;
    resize: vertical;
}

.joint-inspector.joint-theme-material select.select {
    font-size: 14px;
    font-family: lato-light, Arial, sans-serif;
    background: #ecf0f8;
    color: #55627b;
    -webkit-appearance: none;
    appearance: none;
    -webkit-padding-end: 20px;
    -webkit-padding-start: 2px;
    background-image: url('data:image/png;base64,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');
    background-position: right center;
    background-repeat: no-repeat;
    overflow: hidden;
    padding: 5px 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 200px;
    cursor: pointer;
}

.joint-inspector.joint-theme-material select.select::-ms-expand {
    visibility: hidden;
}

.joint-inspector.joint-theme-material output,
.joint-inspector.joint-theme-material .units {
    font-size: 12px;
    font-weight: 700;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-material .group {
    height: auto;
    padding: 0;
    padding-bottom: 20px;
    margin-bottom: 1px;
    max-height: 5000px;
    transition: max-height 0.25s cubic-bezier(0.5, 0, 1, 0) -.1s;
	transition-delay: 0s;
}
.joint-inspector.joint-theme-material .group.closed {
    height: auto;
    max-height: 31px;
    padding: 0;
    transition: max-height 0.25s cubic-bezier(0, 1, 0, 1) -.1s;
}

.joint-inspector.joint-theme-material .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #55627b;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
    background: #d0d8e8;
    padding: 0 5px 0 15px;
}

.joint-inspector.joint-theme-material .group > .group-label:hover {
    color: #5faaee;
}

.joint-inspector.joint-theme-material .toggle {
    height: 14px;
    width: 36px;
    border-radius: 14px;
}
.joint-inspector.joint-theme-material .toggle input:checked + span {
    background: #5fa9ee;
}
.joint-inspector.joint-theme-material .toggle span {
    background: rgba(0,0,0,.26);
    color: #f6f6f6;
    border-radius: 14px;
    box-shadow: none;
}
.joint-inspector.joint-theme-material .toggle span i {
    right: 50%;
    width: 50%;
    top: -2px;
    height: 130%;
    left: 0;
    border-radius: 50%;
    cursor: pointer;
    background: #fafafa;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12);
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: left;
}
.joint-inspector.joint-theme-material .toggle input:checked + span i {
    position: absolute;
    left: 20px;
    background: #3f51b5;
    box-shadow: 0 3px 4px 0 rgba(0,0,0,.14),0 3px 3px -2px rgba(0,0,0,.2),0 1px 8px 0 rgba(0,0,0,.12);
}
.joint-inspector.joint-theme-material .btn-list-add,
.joint-inspector.joint-theme-material .btn-list-del {
    background: #5fa9ee;
    color: #deebfb;
    font-weight: bold;
    font-size: 17px;
    border: none;
}
.joint-inspector.joint-theme-material .btn-list-add:hover,
.joint-inspector.joint-theme-material .btn-list-del:hover {
    background-color: #4C88BE;
}

.joint-inspector.joint-theme-material .select-box {
    color: #000;
}

.joint-inspector.joint-theme-material .select-box.color-palette.joint-theme-material div.select-box-selection {
    color: #ddd;
    border: 1px solid transparent;
}

.select-box[data-attribute$="/stroke"].color-palette.joint-theme-material .select-box-selection div.select-box-option-content:after {
    left: 5px;
    top: 5px;
}

.joint-inspector.joint-theme-material .group .field {
    box-sizing: border-box;
    padding: 0 18px;
    margin-top: 12px;
}

.joint-inspector.joint-theme-material input {
    width: 100%;
    height: auto;
    text-shadow: none;
    box-shadow: none;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0;
}

.joint-inspector.joint-theme-material input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
    height: 30px;
}

.joint-inspector.joint-theme-material input[type="range"]:focus {
    outline: none;
}

.joint-inspector.joint-theme-material input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-inspector.joint-theme-material input[type="range"]::-webkit-slider-thumb {
    margin-top: -5px;
    cursor: move;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-thumb {
    margin-top: 0;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-webkit-slider-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-ms-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-thumb {
    cursor: move;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-inspector.joint-theme-material input[type="range"]:active::-moz-range-thumb {

    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-lower,
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
}
.joint-inspector.joint-theme-material input[type="range"]::-ms-fill-lower {
    background: #3f51b5
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
    cursor: pointer;
}
.joint-inspector.joint-theme-material input[type="range"]::-moz-range-progress {
    height: 3px;
    background: #3f51b5;
}
.joint-inspector.joint-theme-material input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
    cursor: pointer;
}

.joint-inspector.joint-theme-material select {
    width: 100%;
    height: auto;
    line-height: 14px;
    text-shadow: none;
    box-shadow: none;
    border: 2px solid #5fa9ee;
    box-sizing: border-box;
    outline: none;
    padding: 6px;
    overflow: auto;
}

.joint-inspector.joint-theme-material select:not([multiple]) {
    height: 33px;
}

/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    /* change "6px padding" for visible text and same size as other browser */
    .joint-inspector.joint-theme-material input[type="text"],
    .joint-inspector.joint-theme-material input[type="number"] {
        padding: 0 0 4px 10px;
    }
}

.joint-inspector.joint-theme-material option {
    background: white;
    padding: 0 10px
}

.joint-inspector.joint-theme-material input[type="color"] {
    width: 40px;
    height: 40px;
}

/*  Lists  */
.joint-inspector.joint-theme-material .list-item {
    background: #d0d8e8;
    box-shadow: none;
    border: none;
    padding: 16px;
}
.joint-inspector.joint-theme-material .list-item .field {
    padding: 0;
}
/*  Lists  */

/* animation ripple toggle */
.joint-inspector.joint-theme-material input.toggle + span:after {
    content: '';
    position: absolute;
    z-index: 2;
    box-sizing: border-box;
    border-radius: 50%;
    background: #3f51b5;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    top: -12px;
    left: -12px;
    overflow: hidden;
    width: 40px;
    height: 40px;
}
.joint-inspector.joint-theme-material .toggle-field.is-in-action input.toggle + span:after {
    opacity: 0.3;
}
.joint-inspector.joint-theme-material input.toggle:checked + span:after {
    transform: translate(20px, 0);
}

/* input animation */
.joint-inspector.joint-theme-material .textarea-field > .input-wrapper,
.joint-inspector.joint-theme-material .number-field > .input-wrapper,
.joint-inspector.joint-theme-material .content-editable-field > .input-wrapper,
.joint-inspector.joint-theme-material .text-field > .input-wrapper {
    position: relative;
}

.joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .content-editable-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .number-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .text-field > .input-wrapper:after {
    background: #3f51b5;
    bottom: 0;
    content: '';
    height: 2px;
    left: 45%;
    position: absolute;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    z-index: -1;
    width: 10px;
    transition-property: width, left, z-index;
    display: block;
}

.joint-inspector.joint-theme-material .is-focused.textarea-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.content-editable-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.number-field > .input-wrapper:after,
.joint-inspector.joint-theme-material .is-focused.text-field > .input-wrapper:after {
    z-index: 1000;
    left:0;
    width: 100%;
}

@-moz-document url-prefix() {
    .joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after {
        bottom: 1px;
    }
}
@media (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {
    .joint-inspector.joint-theme-material .textarea-field > .input-wrapper:after {
        bottom: 3px;
    }
}

.joint-inspector.joint-theme-modern {
    color: #c6c7e2;
    background: #383b61;
}

.joint-inspector.joint-theme-modern label {
    font-size: 12px;
    padding-right: 4px;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-modern input,
.joint-inspector.joint-theme-modern .content-editable,
.joint-inspector.joint-theme-modern textarea {
    color: #ddd;
    border: 2px solid #444;
    background: transparent;
}

.joint-inspector.joint-theme-modern select.select {
    color: #ddd;
    -moz-appearance: none;
}

.joint-inspector.joint-theme-modern select.select::-ms-expand {
    visibility: hidden;
}

.joint-inspector.joint-theme-modern option {
    color: #222;
}

.joint-inspector.joint-theme-modern output,
.joint-inspector.joint-theme-modern .units {
    font-size: 12px;
    font-weight: 700;
    color: #e6e6e6;
    margin-bottom: 6px;
}

.joint-inspector.joint-theme-modern .group {
    height: auto;
    padding: 0;
    padding-bottom: 20px;
    margin-bottom: 1px;
}
.joint-inspector.joint-theme-modern .group.closed {
    height: auto;
    max-height: 31px;
    padding: 0;
}

.joint-inspector.joint-theme-modern .group > .group-label {
    position: relative;
    left: 0;
    width: 100%;
    height: 31px;
    line-height: 31px;
    color: #9093b1;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    box-sizing: border-box;
    background: #424568;
    padding: 0 5px 0 34px;
}

.joint-inspector.joint-theme-modern .group > .group-label:before {
    position: absolute;
    left: 5px;
    top: 6px;
    display: block;
    width: 19px;
    height: 19px;
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    border: none;
    content: ' ';
    margin: 0;
    padding: 0;
}

.joint-inspector.joint-theme-modern .group > .group-label:before {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-inspector.joint-theme-modern .group > .group-label:hover:before {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-inspector.joint-theme-modern .group.closed > .group-label:before {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23C6C7E2%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-inspector.joint-theme-modern .group.closed > .group-label:hover:before {
    background-image: url('data:image/svg+xml;charset=utf8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3C!DOCTYPE%20svg%20PUBLIC%20%22-%2F%2FW3C%2F%2FDTD%20SVG%201.1%2F%2FEN%22%20%22http%3A%2F%2Fwww.w3.org%2FGraphics%2FSVG%2F1.1%2FDTD%2Fsvg11.dtd%22%3E%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2218.75px%22%20height%3D%2218.75px%22%20viewBox%3D%220%200%2018.75%2018.75%22%20enable-background%3D%22new%200%200%2018.75%2018.75%22%20xml%3Aspace%3D%22preserve%22%3E%3Cg%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M9.375%2C0.5c-4.688%2C0-8.5%2C3.813-8.5%2C8.5c0%2C4.688%2C3.812%2C8.5%2C8.5%2C8.5c4.687%2C0%2C8.5-3.812%2C8.5-8.5%20C17.875%2C4.313%2C14.062%2C0.5%2C9.375%2C0.5L9.375%2C0.5z%20M9.375%2C16.386C5.303%2C16.386%2C1.99%2C13.072%2C1.99%2C9s3.312-7.385%2C7.385-7.385%20S16.76%2C4.928%2C16.76%2C9S13.447%2C16.386%2C9.375%2C16.386L9.375%2C16.386z%20M9.375%2C16.386%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M12.753%2C8.443H5.997c-0.308%2C0-0.558%2C0.25-0.558%2C0.557c0%2C0.309%2C0.25%2C0.559%2C0.558%2C0.559h6.756%20c0.308%2C0%2C0.558-0.25%2C0.558-0.559C13.311%2C8.693%2C13.061%2C8.443%2C12.753%2C8.443L12.753%2C8.443z%20M12.753%2C8.443%22%2F%3E%3Cpath%20fill%3D%22%23D8D8EC%22%20d%3D%22M8.817%2C5.623v6.756c0%2C0.307%2C0.25%2C0.557%2C0.558%2C0.557c0.309%2C0%2C0.558-0.25%2C0.558-0.557V5.623%20c0-0.309-0.25-0.559-0.558-0.559S8.817%2C5.314%2C8.817%2C5.623L8.817%2C5.623z%20M8.817%2C5.623%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%20');
}

.joint-inspector.joint-theme-modern .toggle {
    width: 72px;
}
.joint-inspector.joint-theme-modern .toggle input {
    display: block;
    width: 100%;
    box-sizing: border-box;
    box-shadow: none;
    height: 12px;
}
.joint-inspector.joint-theme-modern .toggle input:checked + span {
    background: #31d0c6;
}
.joint-inspector.joint-theme-modern .toggle span {
    background: #c6c7e2;
    border-radius: 40px;
    box-shadow: none;
}
.joint-inspector.joint-theme-modern .toggle span:before {
    background: #f6f6f6;
    box-shadow: none;
}
.joint-inspector.joint-theme-modern .toggle span i:before {
    content: "off";
    position: absolute;
    right: -50%;
    top: 0;
    text-transform: uppercase;
    font-style: normal;
    font-weight: bold;
    color: #6a6c8a;
    font-family: Arial, sans-serif;
    font-size: 10px;
    line-height: 16px;
    margin-top: -1px;
    margin-right: -8px;
}
.joint-inspector.joint-theme-modern .toggle input:checked + span i:before {
    content: "on";
    right: 100%;
    color: #f6f6f6;
    margin-right: 12px;
}
.joint-inspector.joint-theme-modern .toggle span i {
    right: 50%;
    width: 50%;
    background: #f6f6f6;
    box-shadow: 0 0 3px #111;
}

.joint-inspector.joint-theme-modern .btn-list-add,
.joint-inspector.joint-theme-modern .btn-list-del {
    background: transparent;
    color: #fff;
    border: 1px solid gray;
    box-shadow: 1px 1px 1px #000;
}
.joint-inspector.joint-theme-modern .btn-list-add:hover,
.joint-inspector.joint-theme-modern .btn-list-del:hover {
    box-shadow: inset 1px 1px 1px #000;
}

.joint-inspector.joint-theme-modern .joint-select-box {
    color: #000;
}

.joint-inspector.joint-theme-modern .joint-select-box.joint-color-palette.joint-theme-modern div.select-box-selection {
    color: #ddd;
    border: 1px solid #4E517A;
}

.joint-select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-modern .select-box-selection div.select-box-option-content:after {
    left: 5px;
    top: 5px;
}

.joint-inspector.joint-theme-modern .group .field {
    box-sizing: border-box;
    padding: 0 18px;
    margin-top: 12px;
}

.joint-inspector.joint-theme-modern input {
    width: 100%;
    height: auto;
    text-shadow: none;
    box-shadow: none;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding: 0;
}

.joint-inspector.joint-theme-modern input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 20px;
    margin: 0;
}

.joint-inspector.joint-theme-modern input[type="range"]:focus {
    outline: none;
}

.joint-inspector.joint-theme-modern input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-inspector.joint-theme-modern input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 8px;
    margin-top: -2px;
}

.joint-inspector.joint-theme-modern input[type="range"]::-ms-thumb {
    margin-top: 0;
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 8px;
}

.joint-inspector.joint-theme-modern input[type="range"]::-moz-range-thumb {
    -moz-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 8px;
}

.joint-inspector.joint-theme-modern input[type="range"]::-ms-fill-lower,
.joint-inspector.joint-theme-modern input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    border-radius: 3px;
}

.joint-inspector.joint-theme-modern input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    border-radius: 3px;
}

.joint-inspector.joint-theme-modern input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    border-radius: 3px;
}

.joint-inspector.joint-theme-modern input[type="text"],
.joint-inspector.joint-theme-modern input[type="number"],
.joint-inspector.joint-theme-modern textarea,
.joint-inspector.joint-theme-modern .content-editable,
.joint-inspector.joint-theme-modern select {
    width: 100%;
    height: auto;
    line-height: 16px;
    text-shadow: none;
    background: transparent;
    border: 2px solid #4E517A;
    box-shadow: none;
    box-sizing: border-box;
    outline: none;
    padding: 6px;
    overflow: auto;
}

.joint-inspector.joint-theme-modern .content-editable {
    width: calc(100% - 12px); /* twice the padding from above */
    box-sizing: content-box;
    min-height: 1em;
}

.joint-inspector.joint-theme-modern input[type="text"],
.joint-inspector.joint-theme-modern input[type="number"],
.joint-inspector.joint-theme-modern select:not([multiple]) {
    height: 33px;
}

/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    /* change "6px padding" for visible text and same size as other browser */
    .joint-inspector.joint-theme-modern input[type="text"],
    .joint-inspector.joint-theme-modern input[type="number"] {
        padding: 0 0 0 10px;
    }
    /* "on/off" text in the center of the button  */
    .joint-inspector.joint-theme-modern .toggle span i:before {
        margin-top: 0;
    }
}

.joint-inspector.joint-theme-modern option {
    background: #fff;
    padding: 0 10px
}

.joint-inspector.joint-theme-modern input[type="color"] {
    width: 40px;
    height: 40px;
}

/*  Select Box  */
.joint-inspector .joint-select-box.joint-theme-modern .select-box-selection {
    border-color: #4E517A;
    color: #fff;
}
.joint-inspector .joint-select-box.joint-color-palette.joint-theme-modern .select-box-option-content {
    border: none;
}
.joint-inspector .joint-select-button-group.joint-theme-modern.disabled .select-button-group-button {
    color: #4E517A;
}
.joint-inspector .joint-select-button-group.joint-theme-modern.disabled .select-button-group-button.selected {
    border-color: #4E517A;
}
/*  Select Box  */


/*  Lists  */
.joint-inspector.joint-theme-modern .list-item {
    background: #424568;
    box-shadow: none;
    border: none;
    padding: 16px;
}
.joint-inspector.joint-theme-modern .list-item .field {
    padding: 0;
}
/*  Lists  */

.joint-free-transform {
    position: absolute;
    pointer-events: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-drag: none;
    box-sizing: border-box;
}

.joint-free-transform > div {
    position: absolute;
    pointer-events: auto;
    box-sizing: content-box;
}

/* prevent chromium bug */
/* https://bugs.chromium.org/p/chromium/issues/detail?id=639227 */
.joint-free-transform .resize,
.joint-free-transform .rotate {
    touch-action: none;
}

.joint-free-transform .resize {
    border-radius: 6px;
    width: 8px;
    height: 8px;
}

.joint-free-transform .resize[data-position="top-left"] {
    top: -5px;
    left: -5px;
}
.joint-free-transform .resize[data-position="top-right"] {
    top: -5px;
    right: -5px;
}
.joint-free-transform .resize[data-position="bottom-left"] {
    bottom: -5px;
    left: -5px;
}
.joint-free-transform .resize[data-position="bottom-right"] {
    bottom: -5px;
    right: -5px;
}
.joint-free-transform .resize[data-position="top"] {
    top: -5px;
    left: 50%;
    margin-left: -5px;
}
.joint-free-transform .resize[data-position="bottom"] {
    bottom: -5px;
    left: 50%;
    margin-left: -5px;
}
.joint-free-transform .resize[data-position="left"] {
    left: -5px;
    top: 50%;
    margin-top: -5px;
}
.joint-free-transform .resize[data-position="right"] {
    right: -5px;
    top: 50%;
    margin-top: -5px;
}

.joint-free-transform.no-orthogonal-resize .resize[data-position="top"],
.joint-free-transform.no-orthogonal-resize .resize[data-position="bottom"],
.joint-free-transform.no-orthogonal-resize .resize[data-position="left"],
.joint-free-transform.no-orthogonal-resize .resize[data-position="right"] {
    display: none;
}

.joint-free-transform .resize.n { cursor: n-resize; }
.joint-free-transform .resize.s { cursor: s-resize; }
.joint-free-transform .resize.e { cursor: e-resize; }
.joint-free-transform .resize.w { cursor: w-resize; }
.joint-free-transform .resize.ne { cursor: ne-resize; }
.joint-free-transform .resize.nw { cursor: nw-resize; }
.joint-free-transform .resize.se { cursor: se-resize; }
.joint-free-transform .resize.sw { cursor: sw-resize; }

.joint-free-transform .rotate {
    border-radius: 6px;
    width: 10px;
    height: 10px;
    top: -20px;
    left: -20px;
    cursor: pointer;
}

.joint-free-transform.no-rotation .rotate {
    display: none;
}

.joint-free-transform.in-operation {
    border-style: hidden;
}

.joint-free-transform.in-operation > div{
    display: none;
}

.joint-free-transform  > div.in-operation {
    display: block;
}

.joint-free-transform.joint-theme-dark {
    border: 1px solid #8f9498;
}
.joint-free-transform.joint-theme-dark .resize,
.joint-free-transform.joint-theme-dark .rotate {
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, #acaca3, #8f9498);
    border-radius: 0;
}
.joint-free-transform.joint-theme-dark .resize[data-position="top-left"] {
    top: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="top-right"] {
    top: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="bottom-left"] {
    bottom: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="bottom-right"] {
    bottom: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="top"] {
    top: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="bottom"] {
    bottom: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="left"] {
    left: -3px;
    margin-top: -3px;
}
.joint-free-transform.joint-theme-dark .resize[data-position="right"] {
    right: -3px;
    margin-top: -3px;
}
.joint-free-transform.joint-theme-default {
    border: 1px dashed black;
}

.joint-free-transform.joint-theme-default > div {
    background-color: white;
    border: 1px solid black;
}

.joint-free-transform.joint-theme-default > div:hover {
    background-color: lightgrey;
}

.joint-free-transform.joint-theme-default > div.in-operation {
    background-color: grey;
}
.joint-free-transform.joint-theme-material {
    border: 1px solid #c6c7e2;
}
.joint-free-transform.joint-theme-material .resize,
.joint-free-transform.joint-theme-material .rotate {
    width: 6px;
    height: 6px;
    background: #717d98;
    border-radius: 0;
}
.joint-free-transform.joint-theme-material .resize[data-position="top-left"] {
    top: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="top-right"] {
    top: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="bottom-left"] {
    bottom: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="bottom-right"] {
    bottom: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="top"] {
    top: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="bottom"] {
    bottom: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="left"] {
    left: -3px;
    margin-top: -3px;
}
.joint-free-transform.joint-theme-material .resize[data-position="right"] {
    right: -3px;
    margin-top: -3px;
}
.joint-free-transform.joint-theme-modern {
    border: 1px solid #c6c7e2;
}
.joint-free-transform.joint-theme-modern .resize,
.joint-free-transform.joint-theme-modern .rotate {
    width: 6px;
    height: 6px;
    background: linear-gradient(#6a6c8a, #3c4260);
    border-radius: 0;
}
.joint-free-transform.joint-theme-modern .resize[data-position="top-left"] {
    top: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="top-right"] {
    top: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="bottom-left"] {
    bottom: -3px;
    left: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="bottom-right"] {
    bottom: -3px;
    right: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="top"] {
    top: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="bottom"] {
    bottom: -3px;
    margin-left: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="left"] {
    left: -3px;
    margin-top: -3px;
}
.joint-free-transform.joint-theme-modern .resize[data-position="right"] {
    right: -3px;
    margin-top: -3px;
}
.joint-tooltip {
   position: absolute;
   z-index: 10000;
   border-radius: 5px;
   pointer-events: none;
}

.joint-tooltip .tooltip-content{
   padding: 10px;
}

.joint-tooltip.left, .joint-tooltip.right {
    margin-top: -2px;
}

.joint-tooltip.top, .joint-tooltip.bottom {
    margin-left: -2px;
}

.joint-tooltip.small {
   padding: 5px;
   font-size: 10px;
}

.joint-tooltip .tooltip-arrow, .joint-tooltip .tooltip-arrow-mask {
   border: solid transparent;
   content: " ";
   height: 0;
   width: 0;
   position: absolute;
   pointer-events: none;
}

.joint-tooltip.left .tooltip-arrow-mask, .joint-tooltip.left .tooltip-arrow {
   margin-right: -1px;
   right: 100%;
}

.joint-tooltip.right .tooltip-arrow-mask, .joint-tooltip.right .tooltip-arrow {
   margin-left: -1px;
   left: 100%;
}

.joint-tooltip.top .tooltip-arrow-mask, .joint-tooltip.top .tooltip-arrow {
   bottom: 100%;
}

.joint-tooltip.bottom .tooltip-arrow-mask, .joint-tooltip.bottom .tooltip-arrow {
   top: 100%;
}

.joint-tooltip .tooltip-arrow-mask {
   border-width: 6px;
}

.joint-tooltip.left .tooltip-arrow-mask {
   margin-top: -6px;
}

.joint-tooltip.right .tooltip-arrow-mask {
   margin-top: -6px;
}

.joint-tooltip.top .tooltip-arrow-mask {
   margin-left: -6px;
}

.joint-tooltip.bottom .tooltip-arrow-mask {
   margin-left: -6px;
}

.joint-tooltip .tooltip-arrow {
   border-width: 8px;
}

.joint-tooltip.left .tooltip-arrow {
   margin-top: -8px;
}

.joint-tooltip.right .tooltip-arrow {
   margin-top: -8px;
}

.joint-tooltip.top .tooltip-arrow {
   margin-left: -8px;
}

.joint-tooltip.bottom .tooltip-arrow {
   margin-left: -8px;
}

/* Fading In */

@keyframes joint-tooltip-fadein {
   from { opacity: 0; }
   to   { opacity: 1; }
}
@-webkit-keyframes joint-tooltip-fadein {
   from { opacity: 0; }
   to   { opacity: 1; }
}
@-moz-keyframes joint-tooltip-fadein {
   from { opacity: 0; }
   to   { opacity: 1; }
}
@-ms-keyframes joint-tooltip-fadein {
   from { opacity: 0; }
   to   { opacity: 1; }
}

.joint-tooltip.animated {
   opacity: 0;
}
.joint-tooltip.animated.rendered {
   animation: joint-tooltip-fadein;
   -webkit-animation: joint-tooltip-fadein;
   -moz-animation: joint-tooltip-fadein;
   -ms-animation: joint-tooltip-fadein;
   animation-fill-mode: forwards;
   -webkit-animation-fill-mode: forwards;
   -moz-animation-fill-mode: forwards;
   -ms-animation-fill-mode: forwards;
}

.joint-tooltip.joint-theme-dark {
   color: #fff;
   font-size: 14px;
   background: #5e6366;
   border: none;
   border-radius: 10px;
   box-shadow: 0 1px 1px #aaa;
}

.joint-tooltip.joint-theme-dark.left .tooltip-arrow-mask {
   border-right-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.right .tooltip-arrow-mask {
   border-left-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.top .tooltip-arrow-mask {
   border-bottom-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.bottom .tooltip-arrow-mask {
   border-top-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.left .tooltip-arrow {
   border-right-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.right .tooltip-arrow {
   border-left-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.top .tooltip-arrow {
   border-bottom-color: #5e6366;
}

.joint-tooltip.joint-theme-dark.bottom .tooltip-arrow {
   border-top-color: #5e6366;
}

.joint-tooltip.joint-theme-default {
   background-color: lightgrey;
   color: black;
   font-size: 14px;
}

.joint-tooltip.joint-theme-default.left .tooltip-arrow-mask {
   border-right-color: lightgrey;
}

.joint-tooltip.joint-theme-default.right .tooltip-arrow-mask {
   border-left-color: lightgrey;
}

.joint-tooltip.joint-theme-default.top .tooltip-arrow-mask {
   border-bottom-color: lightgrey;
}

.joint-tooltip.joint-theme-default.bottom .tooltip-arrow-mask {
   border-top-color: lightgrey;
}

.joint-tooltip.joint-theme-default.left .tooltip-arrow {
   border-right-color: lightgrey;
}

.joint-tooltip.joint-theme-default.right .tooltip-arrow {
   border-left-color: lightgrey;
}

.joint-tooltip.joint-theme-default.top .tooltip-arrow {
   border-bottom-color: lightgrey;
}

.joint-tooltip.joint-theme-default.bottom .tooltip-arrow {
   border-top-color: lightgrey;
}

.joint-tooltip.joint-theme-material {
    color: #deebfb;
    font-size: 14px;
    background: #5fa9ee;
    border: none;
    border-radius: 3px;
    font-family: lato-light, Arial, sans-serif;
    -webkit-animation: pulse 200ms cubic-bezier(0,0,.2,1);
    animation: pulse 200ms cubic-bezier(0,0,.2,1);
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

@keyframes pulse {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

.joint-tooltip.joint-theme-material.left .tooltip-arrow-mask {
    border-right-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.right .tooltip-arrow-mask {
    border-left-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.top .tooltip-arrow-mask {
    border-bottom-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.bottom .tooltip-arrow-mask {
    border-top-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.left .tooltip-arrow {
    border-right-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.right .tooltip-arrow {
    border-left-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.top .tooltip-arrow {
    border-bottom-color: #5fa9ee;
}

.joint-tooltip.joint-theme-material.bottom .tooltip-arrow {
    border-top-color: #5fa9ee;
}

.joint-tooltip.joint-theme-modern.joint-theme-modern {
   color: #fff;
   font-size: 14px;
   background: #6a6b8a;
   border: none;
   border-radius: 10px;
   box-shadow: 0 1px 1px #aaa;
}

.joint-tooltip.joint-theme-modern.left .tooltip-arrow-mask {
   border-right-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.right .tooltip-arrow-mask {
   border-left-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.top .tooltip-arrow-mask {
   border-bottom-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.bottom .tooltip-arrow-mask {
   border-top-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.left .tooltip-arrow {
   border-right-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.right .tooltip-arrow {
   border-left-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.top .tooltip-arrow {
   border-bottom-color: #6a6b8a;
}

.joint-tooltip.joint-theme-modern.bottom .tooltip-arrow {
   border-top-color: #6a6b8a;
}

.joint-snaplines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.joint-snaplines > .snapline {
    position: absolute;
    pointer-events: none;
    opacity: 1;
}

.joint-snaplines > .snapline.horizontal {
    width: 100%;
}

.joint-snaplines > .snapline.vertical {
    height: 100%;
}

/* When the snaplines are inside the PaperScroller enlarge the snaplines so they appear as they would sit in the PaperScroller. */
.joint-paper-scroller .joint-snaplines > .snapline.horizontal {
    width: 700%;
    left: -300%;
}

.joint-paper-scroller .joint-snaplines > .snapline.vertical {
    height: 700%;
    top: -300%;
}

.joint-snaplines.joint-theme-dark .snapline.horizontal {
    border-bottom: 1px solid #feb663;
}

.joint-snaplines.joint-theme-dark .snapline.vertical {
    border-right: 1px solid #feb663;
}

.joint-snaplines.joint-theme-default .snapline.horizontal {
    border-bottom: 1px solid lightgrey;
}

.joint-snaplines.joint-theme-default .snapline.vertical {
    border-right: 1px solid lightgrey;
}

.joint-snaplines.joint-theme-material .snapline.horizontal {
    border-bottom: 1px solid #5faaee;
}

.joint-snaplines.joint-theme-material .snapline.vertical {
    border-right: 1px solid #5faaee;
}
.joint-snaplines.joint-theme-modern .snapline.horizontal {
    border-bottom: 1px solid #2ECC71;
}

.joint-snaplines.joint-theme-modern .snapline.vertical {
    border-right: 1px solid #2ECC71;
}

.joint-text-editor {
    margin: 0;
    padding: 0;
}

@-webkit-keyframes text-editor-caret-blink {
    0%   { opacity: 1 }
    100%  { opacity: 0 }
}
@-moz-keyframes text-editor-caret-blink {
    0%   { opacity: 1 }
    100%  { opacity: 0 }
}
@-ms-keyframes text-editor-caret-blink {
    0%   { opacity: 1 }
    100%  { opacity: 0 }
}
@keyframes text-editor-caret-blink {
    0%   { opacity: 1 }
    100%  { opacity: 0 }
}

.joint-text-editor .caret {
    position: absolute;
    height: 15px;
    margin-left: -1px;
    margin-top: 2px;
    width: 1px;
    -webkit-animation: text-editor-caret-blink 1s linear 0s infinite;
    -moz-animation: text-editor-caret-blink 1s linear 0s infinite;
    -ms-animation: text-editor-caret-blink 1s linear 0s infinite;
    animation: text-editor-caret-blink 1s linear 0s infinite;
    pointer-events: none;
    white-space: nowrap;
}
.joint-text-editor .caret.placeholder {
    background-color: transparent;
    -webkit-animation: none;
    animation: none;
    width: auto;
}

.joint-text-editor .caret.placeholder:before {
    content: '';
    display: block;
    height: 100%;
    width: 1px;
    -webkit-animation: text-editor-caret-blink 1s linear 0s infinite;
    -moz-animation: text-editor-caret-blink 1s linear 0s infinite;
    -ms-animation: text-editor-caret-blink 1s linear 0s infinite;
    animation: text-editor-caret-blink 1s linear 0s infinite;
    float: left;
}
.joint-text-editor .caret.placeholder:after {
    content: attr(data-placeholder-text);
    vertical-align: middle;
    display: inline-block;
}

.joint-text-editor .caret[text-anchor="middle"].placeholder:after {
    -ms-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}

.joint-text-editor .caret[text-anchor="end"].placeholder:after {
    -ms-transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
}

.joint-text-editor .char-selection-box {
    position: absolute;
    opacity: .8;
    padding: 0;
    margin: 0;
    margin-top: 2px;
    pointer-events: none;
}

.joint-text-editor .char-selection-box:hover {
    cursor: text;
}

.joint-text-editor .textarea-container {
    position: absolute;
    height: 0;
    overflow: hidden;
}

.joint-text-editor textarea {
    position: absolute;/* to get document.execCommand('copy') to work in Chrome */
    width: 1000px;
    height: 1em;
    outline: none;
}

.joint-text-editor.joint-theme-dark .caret {
    background-color: #595A5A;
}

.joint-text-editor.joint-theme-dark .caret.placeholder:before {
    background-color: #595A5A;
}
.joint-text-editor.joint-theme-dark .caret.placeholder:after {
    font-style: italic;
    color: #D3D3D3;
}

.joint-text-editor.joint-theme-dark .char-selection-box {
    background-color: #D3D3D3;
}

.joint-text-editor.joint-theme-default .caret {
    background-color: black;
}

.joint-text-editor.joint-theme-default .caret.placeholder:before {
    background-color: black;
}
.joint-text-editor.joint-theme-default .caret.placeholder:after {
    font-style: italic;
    color: lightgray;
}

.joint-text-editor.joint-theme-default .char-selection-box {
    background-color: lightgray;
}

.joint-text-editor.joint-theme-material .caret {
    background-color: black;
}

.joint-text-editor.joint-theme-material .caret.placeholder:before {
    background-color: black;
}
.joint-text-editor.joint-theme-material .caret.placeholder:after {
    font-style: italic;
    color: lightgray;
}

.joint-text-editor.joint-theme-material .char-selection-box {
    background-color: lightgray;
}

.joint-text-editor.joint-theme-modern .caret {
    background-color: black;
}

.joint-text-editor.joint-theme-modern .caret.placeholder:before {
    background-color: black;
}
.joint-text-editor.joint-theme-modern .caret.placeholder:after {
    font-style: italic;
    color: lightgray;
}

.joint-text-editor.joint-theme-modern .char-selection-box {
    background-color: #afd5ff;
}

.joint-dialog .bg {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: .5;
    z-index: 10000;
}

.joint-dialog .fg {
    width: 80%;
    margin: 0 auto;
    top: 100px;
    left: 0;
    right: 0;
    z-index: 10001;
    position: absolute;
}

.joint-dialog.inlined .bg {
    display: none;
}

.joint-dialog:not(.modal) .bg {
    display: none;
}
.joint-dialog:not(.modal) .fg {
    /* Make sure modal dialogs are always on top. */
    z-index: 9999;
}

.joint-dialog.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    margin: 0;
    z-index: auto;
}

.joint-dialog .titlebar {
    text-rendering: optimizeLegibility;
}

.joint-dialog.draggable .titlebar {
    cursor: move;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.joint-dialog .titlebar.empty {
    display: none;
}

.joint-dialog .btn-close {
    line-height: 1;
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
}

.joint-dialog .body {
    padding: 10px;
}

.joint-dialog .controls {
    text-align: center; /* to align .center buttons */
}

.joint-dialog .controls .control-button {
    float: right; /* buttons are right-aligned by default */
    margin-left: 5px;
    margin-right: 5px;
    line-height: 30px;
    height: 30px;
    cursor: pointer;
    outline: none;
    margin-top: 20px;
    margin-bottom: 10px;
}
.joint-dialog .controls .control-button:first-child:not(.left):not(.center) {
    /* requires right-aligned buttons to come first in the generated HTML */
    /* workaround because right-aligned buttons do not have a dedicated class */
    margin-right: 10px; /* extra space for the rightmost right-aligned button */
}
.joint-dialog .controls .control-button.left {
    float: left;
}
.joint-dialog .controls .control-button.left:not(.left ~ .left) {
    margin-left: 10px; /* extra space for the leftmost left-aligned button */
}
.joint-dialog .controls .control-button.center {
    float: none; /* remove default button float */
    display: inline-block; /* align to center */
}

.joint-dialog.modal {
    /* prevent the bootstrap CSS to override the modal CSS */
    display: block;
}

@font-face {
  font-family: 'dialog-icons-dark';
  src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-dialog.joint-theme-dark {
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
}

.joint-dialog.joint-theme-dark .bg {
    background-color: #111;
}

.joint-dialog.joint-theme-dark .fg {
    background-color: #f6f6f6;
    box-shadow: 2px 2px 3px #666;
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid #383c3f;
}

.joint-dialog.joint-theme-dark.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    z-index: auto;
    margin: 0;
}

.joint-dialog.joint-theme-dark .titlebar {
    background: url('data:image/png;base64,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') repeat;
    color: #F5F5F5;
    padding: 10px;
    padding-right: 25px;
    text-shadow: 1px 2px 1px #313538;
    border-bottom: 1px solid #383c3f;
}
.joint-dialog.joint-theme-dark[data-type="alert"] .titlebar {
    background: url('data:image/png;base64,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') repeat;
 }
.joint-dialog.joint-theme-dark[data-type="warning"] .titlebar {
    background: url('data:image/png;base64,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') repeat;
}
.joint-dialog.joint-theme-dark[data-type="info"] .titlebar {
    background: url('data:image/png;base64,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') repeat;
    border-bottom: 1px solid #06416E;
}
.joint-dialog.joint-theme-dark[data-type="success"] .titlebar {
    background: url('data:image/png;base64,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') repeat;
}
.joint-dialog.joint-theme-dark:not(.joint-lightbox) .btn-close {
    background-color: transparent;
    border: none;
    visibility: hidden;
    top: 10px;
}
.joint-dialog.joint-theme-dark:not(.joint-lightbox) .btn-close:before {
    position: absolute;
    color: #F5F5F5;
    font-family: dialog-icons-dark;
    font-style: normal;
    font-weight: 400;
    speak: none;
    display: inline-block;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    font-size: 16px;
    text-shadow: 0px 1px #35393c;
    content: '\e80a';
    visibility: visible;
}
.joint-dialog.joint-theme-dark:not(.joint-lightbox) .btn-close:hover:before {
    color: #866B6B;
}
.joint-dialog.joint-theme-dark .body {
    color: #3c4260;
    background-color: #f6f6f6;
    padding: 10px;
}

.joint-dialog.joint-theme-dark .controls .control-button {
    border: 1px solid #6A6C8B;
    color: #6A6C8B;
    background-color: transparent;
    border-radius: 8px;
    font-weight: bolder;
    min-width: 100px;
    padding: 0 15px;
    font-size: 10pt;
}

.joint-dialog.joint-theme-dark .controls .control-button:hover {
    color: #f6f6f6;
    background-color: #6A6C8B;
}

.joint-dialog.joint-theme-default {
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif !important;
}

.joint-dialog.joint-theme-default .bg {
    background-color: black;
}

.joint-dialog.joint-theme-default .fg {
    border-radius: 5px;
    background-color: white;
    border: 1px solid lightgrey;
}

.joint-dialog.joint-theme-default.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    margin: 0;
    z-index: auto;
}

.joint-dialog.joint-theme-default .titlebar {
    background-color: #7c68fc;
    padding: 10px;
    padding-right: 25px;
    color: white;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-bottom: 1px solid lightgrey;
}

.joint-dialog.joint-theme-default[data-type="alert"] .titlebar {
    background-color: #fe854f;
}
.joint-dialog.joint-theme-default[data-type="warning"] .titlebar {
    background-color: #feb663;
}
.joint-dialog.joint-theme-default[data-type="success"] .titlebar {
    background-color: #31d0c6;
}
.joint-dialog.joint-theme-default[data-type="neutral"] .titlebar {
    background-color: #efefef;
    color: #696c8a;
}

.joint-dialog.joint-theme-default .btn-close {
    background-color: transparent;
    border: 1px solid transparent;
    font-size: 16px;
    font-family: Arial;
    border-radius: 4px;
}

.joint-dialog.joint-theme-default .btn-close:hover {
    border: 1px solid black;
}

.joint-dialog.joint-theme-default .body {
    padding: 10px;
}

.joint-dialog.joint-theme-default .controls .control-button {
    border: 1px solid lightgrey;
    color: black;
    background-color: transparent;
    padding: 0 15px;
    font-size: 10pt;
    border-radius: 4px;
}

.joint-dialog.joint-theme-default .controls .control-button:hover {
    background-color: lightgrey;
}

.joint-dialog.joint-theme-material {
    font-family: lato-light, Arial, sans-serif;
}

.joint-dialog.joint-theme-material .fg {
    border: 2px solid #d0d8e8;
    background-color: #ecf0f8;
    border-radius: 3px;
    overflow: hidden;
}

.joint-dialog.joint-theme-material.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    z-index: auto;
    margin: 0;
}

.joint-dialog.joint-theme-material .titlebar {
    color: #6a6c8a;
    text-shadow: none;
    background-color: #d0d8e8;
    padding: 5px;
    padding-right: 25px;
}
.joint-dialog.joint-theme-material[data-type="alert"] .fg {
    border-color: #C00D0F;
}
.joint-dialog.joint-theme-material[data-type="alert"] .titlebar {
    color:#deebfb;
    background-color: #C00D0F;
}
.joint-dialog.joint-theme-material[data-type="warning"] .fg {
    border-color: #daac0f;
}
.joint-dialog.joint-theme-material[data-type="warning"] .titlebar {
    color: #deebfb;
    background-color: #daac0f;
}
.joint-dialog.joint-theme-material[data-type="success"] .fg {
    border-color: #5fa9ee;
}
.joint-dialog.joint-theme-material[data-type="success"] .titlebar {
    color: #deebfb;
    background-color: #5fa9ee;
}

.joint-dialog.joint-theme-material .btn-close {
    color: #6a6c8a;
    font-size: 16px;
    background-color: transparent;
    border: none;
}
.joint-dialog.joint-theme-material[data-type="alert"] .btn-close,
.joint-dialog.joint-theme-material[data-type="warning"] .btn-close,
.joint-dialog.joint-theme-material[data-type="success"] .btn-close {
    color: #f6f6f6;
}

.joint-dialog.joint-theme-material .body {
    color: #55627b;
    padding: 10px;
}

.joint-dialog.joint-theme-material .controls .control-button {
    border: none;
    color: #fefefe;
    background-color: #5faaee;
    border-radius: 8px;
    padding: 0 15px;
    font-size: 10pt;
    font-weight: bold;
}

.joint-dialog.joint-theme-material .controls .control-button:hover {
    background-color: #4C88BE;
}

.joint-dialog.joint-theme-modern {
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
}

.joint-dialog.joint-theme-modern .bg {
    background-color: #111;
}

.joint-dialog.joint-theme-modern .fg {
    background-color: #f6f6f6;
    box-shadow: 0 0 3px #888;
    border-radius: 10px;
    overflow: hidden;
}

.joint-dialog.joint-theme-modern.inlined .fg {
    position: relative;
    top: auto;
    left: auto;
    z-index: auto;
    margin: 0;
}

.joint-dialog.joint-theme-modern .titlebar {
    color: #6a6c8a;
    text-shadow: none;
    background-color: #efefef;
    padding: 10px;
    padding-right: 25px;
}
.joint-dialog.joint-theme-modern[data-type="alert"] .titlebar {
    color: #f6f6f6;
    background-color: #fe854f;
}
.joint-dialog.joint-theme-modern[data-type="warning"] .titlebar {
    color: #f6f6f6;
    background-color: #feb663;
}
.joint-dialog.joint-theme-modern[data-type="success"] .titlebar {
    color: #f6f6f6;
    background-color: #31d0c6;
}

.joint-dialog.joint-theme-modern .btn-close {
    color: #6a6c8a;
    font-size: 16px;
    font-family: Arial;
    background-color: transparent;
    border: none;
}
.joint-dialog.joint-theme-modern[data-type="alert"] .btn-close,
.joint-dialog.joint-theme-modern[data-type="warning"] .btn-close,
.joint-dialog.joint-theme-modern[data-type="success"] .btn-close {
    color: #f6f6f6;
}

.joint-dialog.joint-theme-modern .body {
    color: #3c4260;
    padding: 10px;
}

.joint-dialog.joint-theme-modern .controls .control-button {
    border: 1px solid #6A6C8B;
    color: #6A6C8B;
    background-color: transparent;
    border-radius: 15px;
    padding: 0 15px;
    font-size: 10pt;
    font-family: 'Helvetica Neue';
}

.joint-dialog.joint-theme-modern .controls .control-button:hover {
    color: #f6f6f6;
    background-color: #6A6C8B;
}

.joint-flash-message .fg {
    top: 0;
    max-width: 30%;
    min-width: 300px;
    right: 20px;
    left: auto;
}
.joint-flash-message .titlebar {
    border-radius: 0;
    border-bottom: 0;
}

.joint-flash-message.joint-theme-default .fg {
    border-radius: 5px;
}
.joint-flash-message.joint-theme-default .titlebar {
    padding: 5px;
    padding-right: 25px;
}
.joint-flash-message.joint-theme-default .body {
    color: white;
}
.joint-flash-message.joint-theme-default .fg {
    background-color: #7c68fc;
}
.joint-flash-message.joint-theme-default[data-type="alert"] .fg {
    background-color: #fe854f;
}
.joint-flash-message.joint-theme-default[data-type="warning"] .fg {
    background-color: #feb663;
}
.joint-flash-message.joint-theme-default[data-type="success"] .fg {
    background-color: #31d0c6;
}
.joint-flash-message.joint-theme-default[data-type="neutral"] .fg {
    background-color: #efefef;
}
.joint-flash-message.joint-theme-default[data-type="neutral"] .body {
    color: #696c8a;
    text-shadow: none;
}


.joint-dialog.joint-lightbox {
    position: fixed;
    z-index: 10000;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    text-align: center; /* to align .fg to center */
}

.joint-dialog.joint-lightbox .bg {
    opacity: .87;
}

.joint-dialog.joint-lightbox .btn-close {
    background-color: transparent;
    border: 1px solid transparent;
    text-shadow: none;
}

.joint-dialog.joint-lightbox .fg {
    position: relative;
    display: inline-block; /* align to center */
    overflow: visible;
    background-color: transparent;
    top: 0;
    width: 80%;
    height: 80%;
    min-width: 10%;
    max-height: none;
    border: none;
    box-shadow: none;
    text-align: center; /* to align .body to center */
}

.joint-dialog.joint-lightbox .body {
    display: inline-block; /* align to center */
    background-color: transparent;
    padding: 0;
    width: 100%;
    height: 100%;
}

.joint-dialog.joint-lightbox img {
    display: inline-block;
    max-width: 100%;
    max-height: 100%;
}

.joint-dialog.joint-lightbox .titlebar {
    position: absolute; /* to position it after .body */
    background-color: transparent;
    background: none;
    top: 100%;
    padding: 10px 0px;
    border-bottom: none;
    text-align: left;
}

.joint-dialog.joint-lightbox .controls {
    position: relative; /* to position it after .titlebar */
}

.joint-dialog.joint-lightbox.joint-theme-dark .btn-close {
    color: #d1d2d4;
    top: -35px;
    right: -4px;
    font-size: 24px;
}

.joint-dialog.joint-lightbox.joint-theme-dark .btn-close:hover {
    color: #d1d2d4;
    border: 1px solid #937b7b;
}

.joint-dialog.joint-lightbox.joint-theme-dark .titlebar {
    color: #d1d2d4;
}

.joint-dialog.joint-lightbox.joint-theme-dark .control-button {
    color: #d1d2d4;
    border: 1px solid #937b7b;
}

.joint-dialog.joint-lightbox.joint-theme-default .btn-close {
    color: #fff;
    top: -35px;
    right: -2px;
    font-size: 24px;
}

.joint-dialog.joint-lightbox.joint-theme-default .btn-close:hover {
    border: 1px solid #fff;
}

.joint-dialog.joint-lightbox.joint-theme-default .titlebar {
    color: #fff;
}

.joint-dialog.joint-lightbox.joint-theme-default .control-button {
    color: #fff;
}

.joint-dialog.joint-lightbox.joint-theme-material {
    font-family: lato-light, Arial, sans-serif;
}

.joint-dialog.joint-lightbox.joint-theme-material .bg {
    background: #000;
}

.joint-dialog.joint-lightbox.joint-theme-material .btn-close {
    color: #fff;
    top: -35px;
    right: -2px;
    font-size: 24px;
}

.joint-dialog.joint-lightbox.joint-theme-material .btn-close:hover {
    border: 1px solid #fff;
}

.joint-dialog.joint-lightbox.joint-theme-material .titlebar {
    color: #fff;
}

.joint-dialog.joint-lightbox.joint-theme-material .control-button {
    color: #fff;
}

.joint-dialog.joint-lightbox.joint-theme-modern .btn-close {
    color: #fff;
    top: -35px;
    right: -2px;
    font-size: 24px;
}

.joint-dialog.joint-lightbox.joint-theme-modern .btn-close:hover {
    border: 1px solid #fff;
}

.joint-dialog.joint-lightbox.joint-theme-modern .titlebar {
    color: #fff;
}

.joint-dialog.joint-lightbox.joint-theme-modern .control-button {
    color: #fff;
}

.joint-context-toolbar {
    position: absolute;
    z-index: 1000;
}

.joint-context-toolbar .tools {
    margin: 0;
}

.joint-context-toolbar .tool {
    min-height: 30px;
    min-width: 35px;
    cursor: pointer;
    outline: none;
    vertical-align: middle;
    margin: 0;
    padding: 5px;
}

.joint-context-toolbar.joint-vertical .tool {
    display: block;
    width: 100%;
}

.joint-context-toolbar .tool>img {
    pointer-events: none;
}
.joint-context-toolbar.joint-theme-dark .tools {
    border: 1px solid #0f1110;
    padding: 5px;
    border-radius: 3px;
    background: #5e6366;
}
.joint-context-toolbar.joint-theme-dark .tool {
    border: 1px solid #0f1110;
    color: #d1d2d4;
    box-shadow: -1px -1px 0 0 hsl(0, 0%, 40%) inset;
    border-right: none;
    background: rgba(104,108,112,1);
    background:
        -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(120,124,128,1)), color-stop(1%, rgba(119,123,127,1)), color-stop(100%, rgba(104,108,112,1))); /*  safari4+,chrome */
    background:
        -webkit-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%); /* safari5.1+,chrome10+ */
    background:
        -o-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%);  /* opera 11.10+ */
    background:
        -ms-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%);  /* ie10+ */
    background:
        linear-gradient(180deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%); /* w3c */
}
.joint-context-toolbar.joint-theme-dark.joint-vertical .tool {
    border-right: 1px solid #0f1110;
    border-bottom: none;
}
.joint-context-toolbar.joint-theme-dark .tool:last-child {
    border-right: 1px solid #0f1110;
}
.joint-context-toolbar.joint-theme-dark.joint-vertical .tool:last-child {
    border-bottom: 1px solid #0f1110;
}
.joint-context-toolbar.joint-theme-dark .tool:hover,
.joint-context-toolbar.joint-theme-dark .tool:active {
    background: #92979b;
    background: -webkit-linear-gradient(#3b3f40, #454a4d);
    background: -o-linear-gradient(#3b3f40, #454a4d);
    background: -moz-linear-gradient(#3b3f40, #454a4d);
    background: linear-gradient(#3b3f40, #454a4d);
    box-shadow: -1px -1px 0 0 hsl(0, 0%, 35%) inset;
}

.joint-context-toolbar.joint-theme-default .tools {
    border: 1px solid lightgrey;
}
.joint-context-toolbar.joint-theme-default .tool {
    background: white;
    border: none;
}
.joint-context-toolbar.joint-theme-default .tool:not(:last-child) {
    border-right: 1px solid lightgrey;
}
.joint-context-toolbar.joint-theme-default.joint-vertical .tool:not(:last-child) {
    border-right: none;
    border-bottom: 1px solid lightgrey;
}
.joint-context-toolbar.joint-theme-default.joint-vertical .tool {
    border-bottom: none;
}

.joint-context-toolbar.joint-theme-default .tool:hover {
    background-color: lightgrey;
}
.joint-context-toolbar.joint-theme-default .tool:active {
    background-color: grey;
}

.joint-context-toolbar.joint-theme-material .tools {
    border: 2px solid #d0d8e8;
    border-radius: 3px;
}
.joint-context-toolbar.joint-theme-material .tool {
    background: #f6f6f6;
    border: none;
    border-right: 1px solid #d0d8e8;
}
.joint-context-toolbar.joint-theme-material.joint-vertical .tool {
    border-right: none;
    border-bottom: 1px solid #d0d8e8;
}
.joint-context-toolbar.joint-theme-material .tool:last-child {
    border-right: none;
}
.joint-context-toolbar.joint-theme-material.joint-vertical .tool:last-child {
    border-bottom: none;
    border-right: none;
}
.joint-context-toolbar.joint-theme-material .tool:hover,
.joint-context-toolbar.joint-theme-material .tool:active {
    background-color: #d0d8e8;
}

.joint-context-toolbar.joint-theme-modern .tools {
    border: 2px solid #31d0c6;
    border-radius: 10px;
}
.joint-context-toolbar.joint-theme-modern .tool {
    background: #f6f6f6;
    border: none;
    border-right: 1px solid #c6c7e2;
}
.joint-context-toolbar.joint-theme-modern .tool:last-child {
    border-right: none;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}
.joint-context-toolbar.joint-theme-modern .tool:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}
.joint-context-toolbar.joint-theme-modern.joint-vertical .tool {
    border-right: none;
    border-bottom: 1px solid #c6c7e2;
}
.joint-context-toolbar.joint-theme-modern.joint-vertical .tool:last-child {
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    border-top-right-radius: 0;
    border-bottom: none;
}
.joint-context-toolbar.joint-theme-modern.joint-vertical .tool:first-child {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 0;
}

.joint-context-toolbar.joint-theme-modern .tool:hover,
.joint-context-toolbar.joint-theme-modern .tool:active {
    background-color: #c6c7e2;
}

.joint-popup {
   position: absolute;
   z-index: 1000;
}

.joint-popup .popup-arrow,
.joint-popup .popup-arrow-mask {
   border: solid transparent;
   position: absolute;
}

.joint-popup .popup-arrow-mask {
   border-width: var(--arrow-mask-width);
   /* fix for edge rendering glitches https://bugs.chromium.org/p/chromium/issues/detail?id=600120 */
   transform: scale(1.05);
}

.joint-popup .popup-arrow {
   border-width: var(--arrow-width);
}

.joint-popup.top .popup-arrow-mask,
.joint-popup.top-left .popup-arrow-mask,
.joint-popup.top-right .popup-arrow-mask  {
   border-bottom-color: var(--arrow-mask-color);
}

.joint-popup.bottom .popup-arrow-mask,
.joint-popup.bottom-left .popup-arrow-mask,
.joint-popup.bottom-right .popup-arrow-mask {
   border-top-color: var(--arrow-mask-color);
}

.joint-popup.left .popup-arrow-mask {
   border-right-color: var(--arrow-mask-color);
}

.joint-popup.right .popup-arrow-mask {
   border-left-color: var(--arrow-mask-color);
}

.joint-popup.top .popup-arrow,
.joint-popup.top-left .popup-arrow,
.joint-popup.top-right .popup-arrow {
   border-bottom-color: var(--arrow-color);
}

.joint-popup.bottom .popup-arrow,
.joint-popup.bottom-left .popup-arrow,
.joint-popup.bottom-right .popup-arrow {
   border-top-color: var(--arrow-color);
}

.joint-popup.left .popup-arrow {
   border-right-color: var(--arrow-color);
}

.joint-popup.right .popup-arrow {
   border-left-color: var(--arrow-color);
}

.joint-popup.top .popup-arrow-mask,
.joint-popup.top .popup-arrow {
   bottom: 100%;
   left: 50%;
}

.joint-popup.top-left .popup-arrow-mask,
.joint-popup.top-left .popup-arrow {
   bottom: 100%;
   left: 15px;
}

.joint-popup.top-right .popup-arrow-mask,
.joint-popup.top-right .popup-arrow {
   bottom: 100%;
   left: calc(100% - 15px);
}

.joint-popup.bottom .popup-arrow-mask,
.joint-popup.bottom .popup-arrow {
   top: 100%;
   left: 50%;
}

.joint-popup.bottom-left .popup-arrow-mask,
.joint-popup.bottom-left .popup-arrow {
   top: 100%;
   left: 15px;
}

.joint-popup.bottom-right .popup-arrow-mask,
.joint-popup.bottom-right .popup-arrow {
   top: 100%;
   left: calc(100% - 15px);
}

.joint-popup.bottom .popup-arrow-mask {
   margin-left: calc(-1 * var(--arrow-mask-width));
}

.joint-popup.bottom .popup-arrow {
   margin-left: calc(-1 * var(--arrow-width));
}

.joint-popup.bottom .popup-arrow-mask,
.joint-popup.bottom-right .popup-arrow-mask,
.joint-popup.bottom-left .popup-arrow-mask,
.joint-popup.top .popup-arrow-mask,
.joint-popup.top-right .popup-arrow-mask,
.joint-popup.top-left .popup-arrow-mask {
   margin-left: calc(-1 * var(--arrow-mask-width));
}

.joint-popup.bottom .popup-arrow,
.joint-popup.bottom-right .popup-arrow,
.joint-popup.bottom-left .popup-arrow,
.joint-popup.top .popup-arrow,
.joint-popup.top-right .popup-arrow,
.joint-popup.top-left .popup-arrow {
   margin-left: calc(-1 * var(--arrow-width));
}

.joint-popup.left .popup-arrow-mask,
.joint-popup.left .popup-arrow {
   right: 100%;
   top: 50%;
}

.joint-popup.right .popup-arrow-mask,
.joint-popup.right .popup-arrow {
   left: 100%;
   top: 50%;
}

.joint-popup.left .popup-arrow,
.joint-popup.right .popup-arrow {
   margin-top: calc(-1 * var(--arrow-width));
}

.joint-popup.left .popup-arrow-mask,
.joint-popup.right .popup-arrow-mask {
   margin-top: calc(-1 * var(--arrow-mask-width));
}

.joint-popup.joint-theme-dark {
   background-color: #8b9094;
   border: 2px solid #5e6366;
   border-radius: 10px;
   padding: 10px;
   --arrow-mask-width: 6px;
   --arrow-width: 8px;
   --arrow-mask-color: #8b9094;
   --arrow-color: #5e6366;
}

.joint-popup.joint-theme-default {
   border: 1px solid lightgrey;
   background-color: white;
   padding: 10px;
   --arrow-mask-width: 7px;
   --arrow-width: 8px;
   --arrow-mask-color: white;
   --arrow-color: lightgrey;
}

.joint-popup.joint-theme-material {
    background-color: #ecf0f8;
    border: 2px solid #d0d8e8;
    border-radius: 10px;
    padding: 10px;
    font-family: lato-light, Arial, sans-serif;
    --arrow-mask-width: 6px;
    --arrow-width: 8px;
    --arrow-mask-color: #ecf0f8;
    --arrow-color: #d0d8e8;
}

.joint-popup.joint-theme-modern {
   background-color: #fff;
   border: 2px solid #31d0c6;
   border-radius: 10px;
   padding: 10px;
   --arrow-mask-width: 6px;
   --arrow-width: 8px;
   --arrow-mask-color: #fff;
   --arrow-color: #31d0c6;
}

.joint-select-box {
    position: relative;
    display: inline-block;
    cursor: pointer;
    box-sizing: border-box;
}
.select-box-selection {
    padding: 8px 12px;
    padding-right: 40px;
}
.select-box-selection:empty {
    height: 1em;
}
.select-box-selection:after,
.select-box-option.selected:after {
    content: '';
    display: block;
    position: absolute;
    right: 10px;
    top: 0;
    bottom: 0;
    margin: auto;
}
.joint-select-box-options {
    position: absolute;
    z-index: 10001;
    box-sizing: border-box;
}
.joint-select-box.opened .joint-select-box-options {
    display: block;
}
.select-box-option {
    cursor: pointer;
    padding: 8px 12px;
    padding-right: 40px;
    position: relative;
    box-sizing: border-box;
}
.select-box-option-icon {
    max-height: 1em;
    vertical-align: bottom;
    margin-right: 10px;
}

/* Availibility */

.joint-select-box.disabled {
    cursor: default;
}

.joint-select-box.disabled .select-box-selection:after {
    display: none;
}
.joint-select-box.joint-theme-dark {
    font-size: 14px;
    line-height: 1em;
}

.joint-select-box.joint-theme-dark:not(.joint-color-palette) .select-box-selection {
    padding-right: 35px;
    color: #24282b;
    background: #92979b;
    background: -webkit-linear-gradient(#8b9094, #92979b);
    background: -o-linear-gradient(#8b9094, #92979b);
    background: -moz-linear-gradient(#8b9094, #92979b);
    background: linear-gradient(#8b9094, #92979b);
    border: 1px solid #42474a;
    border-radius: 3px;

    margin: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.joint-select-box.joint-theme-dark:not(.joint-color-palette) .select-box-placeholder {
    color: #d6d6d6;
}
.joint-select-box-options.joint-theme-dark {
    font-size: 14px;
    border: 2px solid #383c3f;
    border-radius: 2px;
    background-color: #f6f6f6;
}
.joint-select-box.joint-theme-dark .select-box-option.hover {
    background-color: #8b9094;
}
.joint-select-box.joint-theme-dark.disabled:not(.joint-color-palette) .select-box-selection {
    background: linear-gradient(#8b9094, #92979b);
}
.joint-select-box.joint-theme-dark.disabled .select-box-option-content {
    color: #d6d6d6;
}
.joint-select-box.joint-theme-dark .select-box-selection:after {
    width: 33px;
    height: calc(100% - 2px);
    border:  1px solid #42474a;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    right: 0;
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat;
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat,
        -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(120,124,128,1)), color-stop(1%, rgba(119,123,127,1)), color-stop(100%, rgba(104,108,112,1))); /*  safari4+,chrome */
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat,
        -webkit-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%); /* safari5.1+,chrome10+ */
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat,
        -o-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%);  /* opera 11.10+ */
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat,
        -ms-linear-gradient(270deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%);  /* ie10+ */
    background:
        url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAKCAYAAAA6jzeaAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AUMDA4najXgawAAAMZJREFUOMvtkjFOw0AQRd+MLCsugC6cyKegQeJckVLkUjZbZFdA8AHsLMI7Q4FEn0WKKHjdFPP19GfgDyA1Sw+PT35elp9ZVXF3uq7jsN9dnNnUSPR9z/12i1hBVTEBd2GaJg773cV5WiMxjiOllO8qRVAHM2MYhqpzVEnM80xKCRHBzCgIMUaWc76exGbTyTEmmfMHq0POmWNMtG0rV5NY10/MjBACqspzCLi7mFlVE1WP6Q7gvLy+cXN7x+n0TtM0/PNbvgDYoFa9lKBZgwAAAABJRU5ErkJggg==') right center no-repeat,
        linear-gradient(180deg, rgba(120,124,128,1) 0%, rgba(119,123,127,1) 1%, rgba(104,108,112,1) 100%); /* w3c */
}

.joint-select-box.joint-theme-default {
    font-size: 14px;
    line-height: 1em;
    background: white;
    color: black;
}
.joint-select-box.joint-theme-default .select-box-selection {
    border: 1px solid lightgrey;
}
.joint-select-box.joint-theme-default .select-box-placeholder {
    color: lightgrey;
}
.joint-select-box.joint-theme-default .select-box-selection:after,
.joint-select-box.joint-theme-default .select-box-option.selected:after {
    width: 10px;
    height: 6px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJ4AAABdCAYAAABU+d9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABONJREFUeNrs3eFV4kAQB/BgA9qBdIAdSAemA/GLfjyugqOD4z76Ca4D6QA6gA68Cs6rgNvBCbdyMSSwye7M/Oe9PH3qg2Tn52Y3TDa97XabNYmnp6cr9+Xm4Mdvz8/P6wxhIpwByv+V/zOX/2WT1+jVgcfYRrwNKv504bYX2tyOvCFFaqBR/nPO/23Fn67cNq+T/6Pw3JtO3Jex2y4b7Osft01pA0Dx4CYMrmn+Jy7308bwuDudH+nh6uzAyO3AC9IoDt2Y0V2e8TIrzv9rLXjuTXNGdxnoOBa8A+j90gfX59zfBnpJ6nyGh3OA/+C5N6ZuddbCMf2icQImIclPGpYBO5xP8X2A1+IbV+pHqEbn5/6mOO1eHAwkX1p844xfe8kHibCDrsj9fqx/4f2CBpPXHRwn8NlDV8SAh3Lv8Li3G3d4vMCXBrpRh+iKmPg9Xt7xmwNfGuhmEfJ+TVdNfHgxAvjioosVe3jDiDsBfLbQUdz0Hh8faXz3O5F2eXDT7Tl4qEa3n9Wm1NPMilkPQi86f3KRAR/QWYcHfMrRFfDWwKcW3SRFdBS7z2rdDm4Tbj9MOE5DR212n+jubYpT7SrhNkTPpwsdxbqAt0y8LYFPD7qdtwKehAphwjcFLfHodt729Xhup2mSMRCw0z/dmA+9n1x0C5e/3L+cIqU3uedGRshDt3d2WIH8mnVTk4eezya6lcvZkL45vIA8FtTmpns+qqEUho5iUnxTdrMPTTTuBB2MuZ6PC3eXQsbkRfxwedp3bGUfmVESN+j5gC5gbPzerrTHE3xw6ns+weiGh/dUV60kIPEg1d44LrUzoHlDWT4q107R9B8GdGmdgeos2gN8QBd82NOruz6ewKm7eHyax9q9JgszAh/QhZrg9U5YERT42kfX5d39Ua4qNIYHfEBXEl+rFmEMBg/4gM6LkyrET4bHDUXKvwjDl5etUAl03aE7Gx43GJ3XZ4IaK7k1+qyhCwIP+IAuGjzgM4Mu2GLqweABnwl0wdoqKDwP39Rqg6KNIsHDfzPOCtHgAR/QRYMHfOLRbXgi0co/YqvwLF8uUICu1U95Wofn4aNEDqzhA7qI8DgBEst8zsIHdAnAs4YP6BKCZwUfL4j4TdDxfXpTjhp42vEJLBeLcltoFHgePkrSnRZ8QCcAnuBkleIDOmHwhCbtwzogQCcUnuTkaemxzcITio8ed38NdMLhMT46BUi6/gV0GuABn350ycIDvmBB1TbjFB9Qkyw84AuCLqm76cTA8/BJKhMHOg3wGJ/Emj6gkw4P+HShEwUP+I5GsstziIcHfJXoRC3FJg4e4+tn7w/+G8CczMUnRcJjfBJr+oBOOjzg2z3cOpe6xrNoeIbxiX+YjHh4Hj4a890CHeDFADjPZJVVmUSnDp5yfKqe1aYOnlJ8jVdVBzzgOzceUixrArxqfHRDznegA7wY+GhcNAM6wAO+6ki2ahjw9OITU9YEeM3xpVrNbAadOXiML8WyKlPoTMJLEJ85dGbhJYRPVNUw4OnAJ/7R9YB3Hr5+1n01s2l0gPcPX5c1febRAV73+ERXDQOeTHyqypoArx188yzs2sxAB3i1AVJly+TMGa+Jz10Br50ZL+E7pa6v82dHAJ5OgLnbRkfGfxs+Tc8BDvDaGAPShec+b6+8rYGtfvwVYABN8cuiTxhvmwAAAABJRU5ErkJggg==');
    background-size: 10px 6px;
}
.joint-select-box-options.joint-theme-default {
    font-size: 14px;
    border: 1px solid lightgrey;
    border-radius: 2px;
    background-color: white;
}
.joint-select-box.joint-theme-default .select-box-option.hover {
    background-color: lightgrey;
}
.joint-select-box.joint-theme-default.disabled .select-box-selection {
    border-color: lightgrey;
}
.joint-select-box.joint-theme-default.disabled .select-box-option-content {
    color: lightgrey;
}
@font-face {
  font-family: 'select-box-material';
  src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-select-box.joint-theme-material {
    font-size: 14px;
    line-height: 1em;
    font-family: lato-light, Arial, sans-serif;
    background: #ecf0f8;
    color: #55627b;
}

.joint-select-box.joint-theme-material .select-box-selection {
    border: 2px solid #5fa9ee;
}

.joint-select-box.joint-theme-material .select-box-placeholder {
    color: #d6d6d6;
}
.joint-select-box.joint-theme-material .select-box-selection:before {
    height: 100%;
    content: ' ';
    position: absolute;
    width: 32px;
    top: 0;
    background: #5fa9ee;
    right: 2px;
}

.joint-select-box.joint-theme-material .select-box-selection:after {
    height: 15px;
    content: '\e800';
    font-family: "select-box-material";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    position: absolute;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    background: #5fa9ee;
    right: 2px;
    color: #FFFFFF;
    border: 9px solid #5fa9ee;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection:before,
.joint-select-box.joint-theme-material .select-box-option.selected {
    background: #d0d8e8;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection {
    border: 2px solid #d0d8e8;
}

.joint-select-box.joint-theme-material.disabled .select-box-selection:after {
    border: 9px solid #d0d8e8;
    background: #d0d8e8;
}

.joint-select-box-options.joint-theme-material {
    font-size: 14px;
    border: 2px solid #5fa9ee;
    border-radius: 2px;
    background-color: #ecf0f8;
}

.joint-select-box.joint-theme-material .select-box-option.hover {
    background-color: #d0d8e8;
    transition: background-color 1.6s cubic-bezier(0, 0, 0.2, 1);
}

.joint-select-box.joint-theme-material.disabled .select-box-option-content {
    color: #d6d6d6;
}

.joint-select-box.joint-select-box-options.joint-theme-material.rendered{
    -webkit-animation: select-box-pulse 200ms cubic-bezier(0,0,.2,1);
    animation: select-box-pulse 200ms cubic-bezier(0,0,.2,1);
}
@-webkit-keyframes select-box-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

@keyframes select-box-pulse {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}
.joint-select-box.joint-theme-modern {
    font-size: 14px;
    line-height: 1em;
}

.joint-select-box.joint-theme-modern .select-box-selection {
    border: 2px solid #e6e6e6;
}

.joint-select-box.joint-theme-modern .select-box-placeholder {
    color: #d6d6d6;
}

.joint-select-box.joint-theme-modern .select-box-selection:after,
.joint-select-box.joint-theme-modern .select-box-option.selected:after {
    width: 10px;
    height: 6px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJ4AAABdCAYAAABU+d9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABONJREFUeNrs3eFV4kAQB/BgA9qBdIAdSAemA/GLfjyugqOD4z76Ca4D6QA6gA68Cs6rgNvBCbdyMSSwye7M/Oe9PH3qg2Tn52Y3TDa97XabNYmnp6cr9+Xm4Mdvz8/P6wxhIpwByv+V/zOX/2WT1+jVgcfYRrwNKv504bYX2tyOvCFFaqBR/nPO/23Fn67cNq+T/6Pw3JtO3Jex2y4b7Osft01pA0Dx4CYMrmn+Jy7308bwuDudH+nh6uzAyO3AC9IoDt2Y0V2e8TIrzv9rLXjuTXNGdxnoOBa8A+j90gfX59zfBnpJ6nyGh3OA/+C5N6ZuddbCMf2icQImIclPGpYBO5xP8X2A1+IbV+pHqEbn5/6mOO1eHAwkX1p844xfe8kHibCDrsj9fqx/4f2CBpPXHRwn8NlDV8SAh3Lv8Li3G3d4vMCXBrpRh+iKmPg9Xt7xmwNfGuhmEfJ+TVdNfHgxAvjioosVe3jDiDsBfLbQUdz0Hh8faXz3O5F2eXDT7Tl4qEa3n9Wm1NPMilkPQi86f3KRAR/QWYcHfMrRFfDWwKcW3SRFdBS7z2rdDm4Tbj9MOE5DR212n+jubYpT7SrhNkTPpwsdxbqAt0y8LYFPD7qdtwKehAphwjcFLfHodt729Xhup2mSMRCw0z/dmA+9n1x0C5e/3L+cIqU3uedGRshDt3d2WIH8mnVTk4eezya6lcvZkL45vIA8FtTmpns+qqEUho5iUnxTdrMPTTTuBB2MuZ6PC3eXQsbkRfxwedp3bGUfmVESN+j5gC5gbPzerrTHE3xw6ns+weiGh/dUV60kIPEg1d44LrUzoHlDWT4q107R9B8GdGmdgeos2gN8QBd82NOruz6ewKm7eHyax9q9JgszAh/QhZrg9U5YERT42kfX5d39Ua4qNIYHfEBXEl+rFmEMBg/4gM6LkyrET4bHDUXKvwjDl5etUAl03aE7Gx43GJ3XZ4IaK7k1+qyhCwIP+IAuGjzgM4Mu2GLqweABnwl0wdoqKDwP39Rqg6KNIsHDfzPOCtHgAR/QRYMHfOLRbXgi0co/YqvwLF8uUICu1U95Wofn4aNEDqzhA7qI8DgBEst8zsIHdAnAs4YP6BKCZwUfL4j4TdDxfXpTjhp42vEJLBeLcltoFHgePkrSnRZ8QCcAnuBkleIDOmHwhCbtwzogQCcUnuTkaemxzcITio8ed38NdMLhMT46BUi6/gV0GuABn350ycIDvmBB1TbjFB9Qkyw84AuCLqm76cTA8/BJKhMHOg3wGJ/Emj6gkw4P+HShEwUP+I5GsstziIcHfJXoRC3FJg4e4+tn7w/+G8CczMUnRcJjfBJr+oBOOjzg2z3cOpe6xrNoeIbxiX+YjHh4Hj4a890CHeDFADjPZJVVmUSnDp5yfKqe1aYOnlJ8jVdVBzzgOzceUixrArxqfHRDznegA7wY+GhcNAM6wAO+6ki2ahjw9OITU9YEeM3xpVrNbAadOXiML8WyKlPoTMJLEJ85dGbhJYRPVNUw4OnAJ/7R9YB3Hr5+1n01s2l0gPcPX5c1febRAV73+ERXDQOeTHyqypoArx188yzs2sxAB3i1AVJly+TMGa+Jz10Br50ZL+E7pa6v82dHAJ5OgLnbRkfGfxs+Tc8BDvDaGAPShec+b6+8rYGtfvwVYABN8cuiTxhvmwAAAABJRU5ErkJggg==');
    background-size: 10px 6px;
}

.joint-select-box-options.joint-theme-modern {
    font-size: 14px;
    border: 2px solid #31d0c6;
    border-radius: 2px;
    background-color: #f6f6f6;
}

.joint-select-box.joint-theme-modern .select-box-option.hover {
    background-color: #c6c7e2;
}

.joint-select-box.joint-theme-modern.disabled .select-box-option-content {
    color: #d6d6d6;
}

.joint-color-palette .select-box-option-content {
    width: 25px;
    display: inline-block;
    margin: 5px;
    padding: 0;
    height: 25px;
    overflow: hidden;
}
.joint-color-palette.joint-select-box-options {
    width: 160px;
    margin-top: -7px;
}
.joint-color-palette .select-box-selection {
    padding: 4px 24px 0 4px;
}
.joint-color-palette .select-box-selection .select-box-option-content {
    float: none;
    margin: 0;
    width: 30px;
    height: 20px;
}
.joint-color-palette .select-box-option.selected:after {
    right: 3px;
}
.joint-color-palette .select-box-option-content .select-box-option-icon {
    width: 21px;
    height: 21px;
    max-height: none;
}
.joint-color-palette .select-box-selection .select-box-option-icon {
  margin-top: -2px;
  margin-left: -2px;
}

/*  Arrow  */
.joint-select-box.joint-color-palette .select-box-options-arrow {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  border: 8px solid transparent;
  pointer-events: none;
  margin-left: -2px;
  margin-top: -16px;
}
.joint-select-box.joint-color-palette .select-box-options-arrow:after {
  content: ' ';
  position: absolute;
  left: -6px;
  top: -4px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  pointer-events: none;
}
/*  Arrow  */

.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option-content {
    border: none;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection,
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection .select-box-option-content {
	width: 30px;
	height: 30px;
    border: none;
    border-radius: 6px;
    padding: 0;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-selection:after,
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option.selected:after {
    display: none;
}

/*  Icons  */
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-option-icon {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
}
/*  Icons  */


/*  Options  */
.joint-select-box-options.joint-color-palette.joint-theme-dark {
    width: 144px;
    border: 2px solid #383c3f;
    background: #92979b;
}

/*  Arrow  */
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-options-arrow {
    border-bottom-color: #383c3f;
}
.joint-select-box.joint-color-palette.joint-theme-dark .select-box-options-arrow:after {
    border-bottom-color: #92979b;
}
/*  Arrow  */

.joint-select-box-options.joint-color-palette.joint-theme-dark .select-box-option {
    width: 25px;
    height: 25px;
    border: none;
    border-radius: 5px;
    padding: 0;
}

.joint-select-box-options.joint-color-palette.joint-theme-dark .select-box-option.hover {
    border: 2px solid #b2ac9e;
}
/*  Options  */


/*  When color palette is used for a stroke attribute  */
.joint-select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-dark .select-box-selection .select-box-option-content:after {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 22px;
    height: 22px;
    background: #5e6366;
    border-radius: 4px;
    content: ' ';
}
/*  When color palette is used for a stroke attribute  */

.joint-select-box.joint-color-palette.joint-theme-default .select-box-option-content {
    border: 1px solid lightgrey;
}
.joint-select-box-options.joint-color-palette.joint-theme-default {
    border: 1px solid lightgrey;
}
.joint-select-box.joint-color-palette.joint-theme-default .select-box-option.selected:after {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA7CAYAAADB0CKOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABPBJREFUeNrcWztM40AQXSwqQMpVtCTUoKODjghqCFdCFQmokxoKnILeoYUi10B5CdR8roMuCGo+JVSkgJbbZ22inIlnxvYm8d1IlpVkvfbb+byZWWfk8/NTpV1WV1fz+lTUB85TgZ9b+rjSh3d6enrV6/qRNIPU4LL6VNPHovCSBhZDg337J0BqgHNGQ5mIl0KzeQ202f7C+c8AKnPNlZkjnZrUD/dNn55iAgxqNAvTTaMmaxYAtjXqpU6TJopecuNyuZx6eXlRHx8fkmlzqQFpzLTZgyI6srS0pLa3t9X4+Lj/+e7uTmlzVDc3N9TUlTSZa5kCODMzo8rlcgcgZHZ2Vu3u7qr5+Xlq3ryTEi0iEu5RY9bX10N/g3YJWUyLJj3qx5WVFV9rYTI5Oen7aZg4KdBimcpoxsbG1MbGBjtPtxmnCqQJNi7pqAE/7CXv7+/q/v4+tZokORHBZmFhgZ3k/PycTAqcIWoRnFigzBRa5OT19VUdHx9TQ+rOEM20Ro0pFAp+QOHk8PCQSwrqw9IkyYmIlBRltAXJAJMI3OpkYfAgJZy4tbXFzoNgU61WJYs5lMCTiBPbgnQO/khItd0pcAasRSuc+PDwoE5OTrgyy41FIQgYpiUxNE6EHB0dsWba3QIZjdNA0t/j9Gyq9zqce1CcCDOliF/Lbz2mxvZ4zKrXIzSQnk0D6Spksdb06RdlpgcHByxlINhsbm5ylJHTz/FEZjxGe08RACqj5Ut9rRuyYN6AOLESBPgFpFnxywTthz09RxCQa4sTLy4uSGvSAF0ydzX8VbMQREtmsdpzlsjBpZJoUgEnFiUJumepgeQHma7GMMmJ09PT7GSgC4YTf4bFgw5I44ciH6SK0y7JmH7Nd6rQlXAiwAk4kczkR7vTHwoYaAMhHjyGGyMIMHljhkvdJJzoeR43xA1uC4SZa57iL/jD8vJy56GgBTSQJDlmL0HjSVonCjiRXQXH+E6GykKIZMFvE0YRcCLTeOpwIqxFkoCzIPWRpbTI8RceWOinvsAPJZyIQljAiU0pyEQCEwYNQEOSoGVSQpYTz87OuAzLkz4jQL5RJiMR0IDEBKWcGDUBZ0FSKn98fPRXVSIITJR/RuFE3JeQhrAg+GKut1SmIdUoglQv/4zCiY1GIxEnUiBr1I0FXNURUEvQP6WcKEjA3V4JuBQkqX6QPuo4iUBr3bQj5cTr62tJU8pTMcQxhShWp8IFA7QdJAJQqCyicKIg2BTjMsBfRbMO72y+CR+VmF6bCiRNKZgpQxloSpXjggzy5Jpxbiv+KQEI62AAtrjeUCSQxmyLtvzTVp0YhRNFGY/hoKot/+SaUgwn/o7KieK0ztj/LXXhzs6OmD/Dgg2zUdNKEmykuSvpn+Cz/f392DeGbzOc6MXhxEggJf6JWo+p2kOjroATXVt+7zA+w/onQErz27aZSjdqBgJS6p8wW6l/CjZqyKZUX0Da9E/hRo1VLYpB2vJP23WibU2K/TMMKFK3qBs1tiTyu3U6v4W/LHKVCKqPiYkJ/zO6bowf+t0RW5RhA6St91GDTSlX9UlivSUpfWVTKNioyao+SqxunQnxFQv3b5nI3VdJ9L6r2aYrJQCYl/ZOB67JQKLwg+LQsEiqj7lBAEysyUAwKhsunSKGNhTxJ5VUgwwARhCZMwcE5A6NNftB9BL5I8AAdamSv1rCQJIAAAAASUVORK5CYII=');
    background-size: 15px 15px;
    width: 15px;
    height: 15px;
}

.joint-select-box.joint-color-palette.joint-theme-default .select-box-option.hover {
    border: 1px solid grey;
}

/*  Arrow  */
.joint-select-box.joint-color-palette.joint-theme-default .select-box-options-arrow {
    border-bottom-color: black;
}
.joint-select-box.joint-color-palette.joint-theme-default .select-box-options-arrow:after {
    border-bottom-color: white;
}
/*  Arrow  */

.joint-select-box.joint-color-palette.joint-theme-material {
    background: transparent;
}
.joint-select-box.joint-color-palette.joint-theme-material .select-box-option-content {
    border: none;
}
.joint-select-box.joint-color-palette.joint-theme-material .select-box-selection,
.joint-select-box.joint-color-palette.joint-theme-material .select-box-selection .select-box-option-content {
	width: 30px;
	height: 30px;
    border: none;
    border-radius: 6px;
    padding: 0;
}
.joint-select-box.joint-color-palette.joint-theme-material .select-box-selection:after,
.joint-select-box.joint-color-palette.joint-theme-material .select-box-option.selected:after {
    display: none;
}
.joint-select-box.joint-color-palette.joint-select-box-options.joint-theme-material.rendered{
    -webkit-animation: collor-pallete-animation 200ms cubic-bezier(0,0,.2,1);
    animation: collor-pallete-animation 200ms cubic-bezier(0,0,.2,1);
}
.joint-select-box.joint-color-palette.joint-theme-material .select-box-selection:before {
    display: none;
}
@-webkit-keyframes collor-pallete-animation {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

@keyframes collor-pallete-animation {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 0; }
  1% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0; }
  50% {
    -webkit-transform: scale(0.99);
            transform: scale(0.99); }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;}
}

/*  Icons  */
.joint-select-box.joint-color-palette.joint-theme-material .select-box-option-icon {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
}
/*  Icons  */


/*  Options  */
.joint-select-box-options.joint-color-palette.joint-theme-material {
    width: 144px;
    border: 2px solid #d0d8e8;
    background: #ecf0f8;
}

/*  Arrow  */
.joint-select-box.joint-color-palette.joint-theme-material .select-box-options-arrow {
    border-bottom-color: #d0d8e8;
}
.joint-select-box.joint-color-palette.joint-theme-material .select-box-options-arrow:after {
    border-bottom-color: #ecf0f8;
}
/*  Arrow  */

.joint-select-box-options.joint-color-palette.joint-theme-material .select-box-option {
    width: 25px;
    height: 25px;
    border: none;
    border-radius: 5px;
    padding: 0;
}
.joint-select-box-options.joint-color-palette.joint-theme-material .select-box-option {
    border: 2px solid transparent;
}
.joint-select-box-options.joint-color-palette.joint-theme-material .select-box-option:hover {
    border: 2px solid #d0d8e8;
}
/*  Options  */


/*  When color palette is used for a stroke attribute  */
.select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-material .select-box-selection .select-box-option-content:after {
    position: absolute;
    left: 4px;
    top: 4px;
    width: 22px;
    height: 22px;
    background: #5fa9ee;
    border-radius: 4px;
    content: ' ';
}
/*  When color palette is used for a stroke attribute  */

.joint-select-box.joint-color-palette.joint-theme-modern .select-box-option-content {
    border: none;
}
.joint-select-box.joint-color-palette.joint-theme-modern .select-box-selection,
.joint-select-box.joint-color-palette.joint-theme-modern .select-box-selection .select-box-option-content {
	width: 30px;
	height: 30px;
    border: none;
    border-radius: 6px;
    padding: 0;
}
.joint-select-box.joint-color-palette.joint-theme-modern .select-box-selection:after,
.joint-select-box.joint-color-palette.joint-theme-modern .select-box-option.selected:after {
    display: none;
}

    /*  Icons  */
    .joint-select-box.joint-color-palette.joint-theme-modern .select-box-option-icon {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
        margin: 0 auto;
    }
    /*  Icons  */


    /*  Options  */
    .joint-select-box-options.joint-color-palette.joint-theme-modern {
        width: 144px;
        border: 2px solid #e6e6e6;
    }

        /*  Arrow  */
        .joint-select-box.joint-color-palette.joint-theme-modern .select-box-options-arrow {
            border-bottom-color: #e6e6e6;
        }
        .joint-select-box.joint-color-palette.joint-theme-modern .select-box-options-arrow:after {
            border-bottom-color: #f6f6f6;
        }
        /*  Arrow  */

    .joint-select-box-options.joint-color-palette.joint-theme-modern .select-box-option {
        width: 25px;
        height: 25px;
        border: none;
        border-radius: 5px;
        padding: 0;
    }
    .joint-select-box-options.joint-color-palette.joint-theme-modern .select-box-option {
        border: 2px solid transparent;
    }
    .joint-select-box-options.joint-color-palette.joint-theme-modern .select-box-option.hover {
        border: 2px solid #31d0c6;
    }
    /*  Options  */


    /*  When color palette is used for a stroke attribute  */
    .joint-select-box[data-attribute$="/stroke"].joint-color-palette.joint-theme-modern .select-box-selection .select-box-option-content:after {
        position: absolute;
        left: 4px;
        top: 4px;
        width: 22px;
        height: 22px;
        background: #383b61;
        border-radius: 4px;
        content: ' ';
    }
    /*  When color palette is used for a stroke attribute  */

.select-button-group-button {
    display: inline-block;
    min-width: 30px;
    min-height: 30px;
    line-height: 22px;
    cursor: pointer;
    box-sizing: border-box;
    padding: 2px;
    margin: 0 1px;
    vertical-align: middle;
    position: relative;
}
.select-button-group-button-icon {
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
}

/* Availibility */

.joint-select-button-group.disabled .select-button-group-button {
    cursor: default;
}

.joint-select-button-group.joint-theme-dark .select-button-group-button {
    text-align: center;
    color: #feffff;
    border: 2px solid transparent;
    text-shadow: 1px 2px 1px #313538;
}
.joint-select-button-group.joint-theme-dark.disabled .select-button-group-button {
    text-shadow: none;
}
.joint-select-button-group.joint-theme-dark .select-button-group-button.selected {
    color: #feffff;
    border: 2px solid #feffff;
    border-radius: 4px;
}
.joint-select-button-group.joint-theme-dark.disabled .select-button-group-button {
    color: #8b9094;
}
 .joint-select-button-group.joint-theme-dark.disabled .select-button-group-button.selected {
    border-color: #8b9094;
}
.joint-select-button-group.joint-theme-default .select-button-group-button {
    text-align: center;
    border: 1px solid transparent;
}
.joint-select-button-group.joint-theme-default .select-button-group-button.selected {
    color: black;
    border: 1px solid lightgrey;
    border-radius: 4px;
}
.joint-select-button-group.joint-theme-default.disabled .select-button-group-button {
    color: lightgrey;
}
 .joint-select-button-group.joint-theme-default.disabled .select-button-group-button.selected {
    border-color: lightgrey;
}

.joint-select-button-group.joint-theme-material .select-button-group-button {
    text-align: center;
    border: 2px solid transparent;
    font-family: lato-light, Arial, sans-serif;
    border-radius: 4px;
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: border-color;
}
.joint-select-button-group.joint-theme-material .select-button-group-button.selected {
    color: #55627b;
    border: 2px solid #55627b;
}
.joint-select-button-group.joint-theme-material.disabled .select-button-group-button {
    color: #d0d8e8;
}
.joint-select-button-group.joint-theme-material.disabled .select-button-group-button.selected {
    border-color: #d0d8e8;
}
/* animation */
.joint-select-button-group.joint-theme-material:not(.disabled) .select-button-group-button:after {
    content: '';
    background: #3f51b5;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -20px 0 0 -20px;
    overflow: hidden;
    width: 40px;
    height: 40px;
}
.joint-select-button-group.joint-theme-material .select-button-group-button.is-in-action:after {
    opacity: 0.3;
}
.joint-select-button-group.joint-theme-modern .select-button-group-button {
    text-align: center;
    border: 2px solid transparent;
}
.joint-select-button-group.joint-theme-modern .select-button-group-button.selected {
    color: #31d0c6;
    border: 2px solid #31d0c6;
    border-radius: 4px;
}
.joint-select-button-group.joint-theme-modern.disabled .select-button-group-button {
    color: #c6c7e2;
}
 .joint-select-button-group.joint-theme-modern.disabled .select-button-group-button.selected {
    border-color: #c6c7e2;
}

.joint-navigator {
    overflow: hidden;
    position: relative;
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    box-sizing: border-box;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}
.joint-navigator > .joint-paper {
    display: inline-block;
    cursor: pointer;
}
.joint-navigator > .joint-paper > svg {
    shape-rendering: optimizeSpeed;
    pointer-events: none;
}
.joint-navigator .current-view {
    position: absolute;
    cursor: move;
    margin: -2px 0 0 -2px;
}
.joint-navigator .current-view-control {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 10px;
    height: 10px;
    cursor: nwse-resize;
    margin: 0 -7px -7px 0;
}
.joint-navigator .joint-paper .joint-element * {
    vector-effect: initial;
}

.joint-navigator.navigator-no-content .current-view,
.joint-navigator.navigator-no-content .joint-paper {
    display: none;
}

@font-face {
  font-family: 'navigator-icons-dark';
  src: url('data:application/octet-stream;base64,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') format('woff');
}

.joint-navigator.joint-theme-dark {
    background-color: #414548;
}
.joint-navigator.joint-theme-dark > .joint-paper {
    box-shadow: 0 0 2px lightgray;
    background-color: #18191b;
}
.joint-navigator.joint-theme-dark.navigator-use-content-bbox {
    background-color: #18191b;
}
.joint-navigator.joint-theme-dark.navigator-use-content-bbox > .joint-paper {
    box-shadow: none
}
.joint-navigator.joint-theme-dark .current-view {
    border: 2px solid #BF8441;
}
.joint-navigator.joint-theme-dark .current-view-control {
    font-family: "navigator-icons-dark";
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
    speak: none;
    color: #BF8441;
    display: inline-block;
    text-decoration: inherit;
}
.joint-navigator.joint-theme-dark .current-view-control:before {
    content: '\e801';
    position: absolute;
    top: 4px;
    left: 1px;
    /* transform: rotate(90deg); */
    /* background-color: #18191b; */
    /* border-radius: 50%; */
    /* border: 2px solid #BF8441; */
    width: 20px;
    height: 20px;
}

.joint-navigator.joint-theme-default {
    background-color: white;
    border: 1px solid lightgrey;
}
.joint-navigator.joint-theme-default > .joint-paper {
    box-shadow: 0 0 2px black;
}
.joint-navigator.joint-theme-default.navigator-use-content-bbox > .joint-paper {
    box-shadow: none;
}
.joint-navigator.joint-theme-default .current-view {
    border: 2px solid grey;
}
.joint-navigator.joint-theme-default .current-view-control {
    border-radius: 50%;
    border: 2px solid grey;
    background-color: white;
}

.joint-navigator.joint-theme-material {
    background-color: #fff;
}
.joint-navigator.joint-theme-material > .paper {
    box-shadow: 0 0 2px lightgray;
}
.joint-navigator.joint-theme-material.navigator-use-content-bbox > .paper {
    box-shadow: none;
}
.joint-navigator.joint-theme-material .current-view {
    border: 2px solid #5faaee;
}
.joint-navigator.joint-theme-material .current-view-control {
    border-radius: 50%;
    border: 2px solid #5faaee;
    background-color: #ecf0f8;
}

.joint-navigator.joint-theme-modern {
    background-color: #fff;
}
.joint-navigator.joint-theme-modern > .joint-paper {
    box-shadow: 0 0 2px lightgray;
}
.joint-navigator.joint-theme-modern.navigator-use-content-bbox > .joint-paper {
    box-shadow: none;
}
.joint-navigator.joint-theme-modern .current-view {
    border: 2px solid #31d0c6;
}
.joint-navigator.joint-theme-modern .current-view-control {
    border-radius: 50%;
    border: 2px solid #31d0c6;
    background-color: #fff;
}

.joint-tree-layout {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.joint-tree-layout .hidden {
    display: none;
}

.tree-layout-box {
    position: absolute;
    pointer-events: none;
    opacity: 1;
}

.joint-tree-layout .tree-layout-box.translate {
    z-index: 100;
    transform: translate(-50%,-50%);
}

/* Prevent throwing exception in FF when trying to measure an element view inside the zero-sized paper. */
.tree-layout-box > .joint-paper {
    min-width: 1px;
    min-height: 1px;
    width: 100%;
    height: 100%;
}

.tree-layout-box > .joint-paper > svg {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
}


.tree-layout-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: move;
}

.tree-layout-mask.dropping-not-allowed {
    cursor: not-allowed;
}

.tree-layout-preview-group .tree-layout-preview {
    fill: none;
}

/* HTML Elements for dragging and highlighting the original element */

.joint-tree-layout.joint-theme-dark .tree-layout-box {
    background-color: rgba(0, 0, 255, 0.1);
    border: 2px solid #feb663;
    border-radius: 5px;
    margin: -2px 0 0 -2px;
}

.joint-tree-layout.joint-theme-dark .tree-layout-box.active {
    border-style: dashed;
    background-color: transparent;
}

/* HTML element under the cursor */

.joint-tree-layout.joint-theme-dark .tree-layout-box.translate {
    background-color: #000;
}

.joint-tree-layout.joint-theme-dark .tree-layout-box.translate.no-drop {
    border-color: #a73c3c;
    background-color: #a73c3c;
}

/* SVG preview on the main paper */

.tree-layout-preview-group.joint-theme-dark .tree-layout-preview {
    stroke: #feb663;
    stroke-width: 2;
}

.tree-layout-preview-group.joint-theme-dark .tree-layout-preview.child,
.tree-layout-preview-group.joint-theme-dark .tree-layout-preview.parent {
    fill: #cc0000;
    fill-opacity: 0.3;
}

/* HTML Elements for dragging and highlighting the original element */

.joint-tree-layout.joint-theme-default .tree-layout-box {
    background-color: rgba(255,255,255,0.5);
    border: 2px solid grey;
    border-radius: 4px;
    margin: -2px 0 0 -2px;
}

/* HTML element under the cursor */

.joint-tree-layout.joint-theme-default .tree-layout-box.translate {
    border-style: dotted;
}

.joint-tree-layout.joint-theme-default .tree-layout-box.translate.no-drop {
    border-color: red;
}

/* SVG preview on the main paper */

.tree-layout-preview-group.joint-theme-default .tree-layout-preview {
    stroke: grey;
    stroke-width: 2;
}

.tree-layout-preview-group.joint-theme-default .tree-layout-preview.child,
.tree-layout-preview-group.joint-theme-default .tree-layout-preview.parent {
    fill: white;
    fill-opacity: 0.5;
}

/* HTML Elements for dragging and highlighting the original element */

.joint-tree-layout.joint-theme-material .tree-layout-box {
    background-color: rgba(208, 230, 249, 0.3);
    border: 2px solid #737c97;
    border-radius: 5px;
    margin: -2px 0 0 -2px;
    padding: 0;
}

.joint-tree-layout.joint-theme-material .tree-layout-box.active {
    border-style: solid;
}

/* HTML element under the cursor */

.joint-tree-layout.joint-theme-material .tree-layout-box > .joint-paper {
    opacity: 0.5;
}

.joint-tree-layout.joint-theme-material .tree-layout-box.translate {
    border-style: dotted;
    background-color: #d0d8e8;
}

.joint-tree-layout.joint-theme-material .tree-layout-box.translate.no-drop {
    border-color: #d71920;
}

/* SVG preview on the main paper */

.tree-layout-preview-group.joint-theme-material .tree-layout-preview {
    stroke: #737c97;
    stroke-width: 2;
}

.tree-layout-preview-group.joint-theme-material .tree-layout-preview.child,
.tree-layout-preview-group.joint-theme-material .tree-layout-preview.parent {
    fill: #d0d8e8;
    fill-opacity: 0.3;
}

/* HTML Elements for dragging and highlighting the original element */

.joint-tree-layout.joint-theme-modern .tree-layout-box {
    background-color: rgba(208, 230, 249, 0.3);
    border: 2px solid #31d0c6;
    border-radius: 5px;
    margin: -2px 0 0 -2px;
    padding: 0;
}

/* HTML element under the cursor */

.joint-tree-layout.joint-theme-modern .tree-layout-box.translate {
    border-style: solid;
}

.joint-tree-layout.joint-theme-modern .tree-layout-box.translate.no-drop {
    border-color: #d71920;
    background-color: rgba(255, 160, 164, 0.2);
}

.joint-tree-layout.joint-theme-modern .tree-layout-box.translate > .joint-paper {
    opacity: 0.4;
}
/* SVG preview on the main paper */

.tree-layout-preview-group.joint-theme-modern .tree-layout-preview {
    stroke: #31d0c6;
    stroke-width: 2;
}

.tree-layout-preview-group.joint-theme-modern .tree-layout-preview.child,
.tree-layout-preview-group.joint-theme-modern .tree-layout-preview.parent {
    fill: #31d0c6;
    fill-opacity: 0.3;
}

.joint-path-drawer {
	cursor: crosshair;
}
.joint-path-drawer .start-point {
	fill: #ffffff;
	stroke: #000000;
	stroke-width: 2px;
}
.joint-path-drawer .start-point:hover {
	fill: #000000;
}
.joint-path-drawer .control-path {
    pointer-events: none;
    fill: none;
    stroke: #000000;
    stroke-width: 1px;
    stroke-linecap: round;
}

.joint-path-editor {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.joint-path-editor .control-point,
.joint-path-editor .anchor-point {
    cursor: pointer;
    pointer-events: auto;
    border: 1px solid lightgrey;
    stroke-width: 1px;
}
.joint-path-editor .control-point {
    fill: royalblue;
    stroke: royalblue;
}
.joint-path-editor .control-point.locked {
    fill: seagreen;
    stroke: seagreen;
}
.joint-path-editor .anchor-point {
    fill: crimson;
    stroke: crimson;
}
.joint-path-editor .direction-path {
    stroke: #000000;
    stroke-width: 1px;
}
.joint-path-editor .segment-path {
    cursor: move;
    pointer-events: auto;
    fill: none;
    stroke: #000000;
    stroke-width: 10px;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0;
    stroke-opacity: 0;
}
.joint-path-editor .segment-path:hover {
    opacity: .6;
    stroke-opacity: .6;
}

.joint-radio-group {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
}

.joint-radio-group label {
    display: flex;
    cursor: pointer;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    margin-bottom: 0.375em;
}

.joint-radio-group label:after {
    content: none;
}

.joint-radio-group input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.joint-radio-group input:checked + span:before {
    box-shadow: inset 0 0 0 0.4375em var(--checked-color);
}

.joint-radio-group input:hover:not(:checked) + span:before {
    box-shadow: inset 0 0 0 0.2em var(--checked-color);
}

.joint-radio-group span {
    display: flex;
    align-items: center;
}

.joint-radio-group span:before {
    display: flex;
    flex-shrink: 0;
    content: "";
    background-color: #fff;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    margin-right: 0.375em;
    transition: 0.25s ease;
    box-shadow: inset 0 0 0 0.125em var(--checked-color);
}

.joint-radio-group.joint-theme-dark {
    --checked-color: #8b9094;
}

.joint-radio-group.joint-theme-default {
    --checked-color: black;
}

.joint-radio-group.joint-theme-material {
    --checked-color: #5fa9ee;
}



.joint-radio-group.joint-theme-modern {
    --checked-color: #31d0c6;
}

div.joint-toolbar,
div.joint-toolbar .joint-toolbar-group,
div.joint-toolbar .joint-widget {
    display: flex;
    align-items: center;
    flex-shrink: 1000;
}

div.joint-toolbar button.joint-widget {
    flex-shrink: 1;
}

.joint-toolbar .joint-toolbar-group.right.group-first {
    margin-left: auto;
}

.joint-toolbar .joint-widget + .joint-widget,
.joint-toolbar-group + .joint-toolbar .joint-widget,
.joint-toolbar .joint-widget + .joint-toolbar-group,
.joint-toolbar-group + .joint-toolbar-group {
    margin-left: 2px;
}

/* chrome */
@media screen and (-webkit-min-device-pixel-ratio:0)
{
    div.joint-toolbar textarea {
        margin-top: 4px;
    }
    div.joint-toolbar button.joint-widget {
        text-align: center;
    }
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar .joint-toolbar-group,
    .joint-toolbar .joint-widget {
        vertical-align: middle;
    }

    .joint-toolbar {
        display: inline-block;
        vertical-align: top;
    }
    .joint-toolbar .joint-toolbar-group.right {
        float: right;
    }
    div.joint-toolbar button.joint-widget {
        display:table-cell;
    }
}
.joint-toolbar.joint-theme-dark {
    padding: 8px;
    box-sizing: border-box;
    color: #d3d3d5;
    background: #5e6366;
}

.joint-toolbar.joint-theme-dark label,
.joint-toolbar.joint-theme-dark .units,
.joint-toolbar.joint-theme-dark output {
    font-size: .8em;
}

.joint-toolbar.joint-theme-dark .joint-widget {
    margin-left: 6px;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar.joint-theme-dark .joint-widget[data-type="selectButtonGroup"] {
        padding-top: 1px;
    }

    .joint-toolbar.joint-theme-dark .joint-widget[data-type="toggle"] span:first-child {
        line-height: 32px;
    }

    .joint-toolbar.joint-theme-dark label {
        line-height: 33px;
    }
}

.joint-toolbar.joint-theme-default {
    background: white;
    padding: 0 10px;
    border: 1px solid lightgray;
    box-sizing: border-box;
    font-size: 12px;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar.joint-theme-default .joint-widget[data-type="toggle"] span:first-child {
        line-height: 32px;
    }
    .joint-toolbar.joint-theme-default label {
        line-height: 36px;
    }
}
.joint-toolbar.joint-theme-material {
    background: #717d98;
    box-sizing: border-box;
    color: #ffffff;
    font-family: lato-light;
}
.joint-toolbar.joint-theme-material label,
.joint-toolbar.joint-theme-material .units,
.joint-toolbar.joint-theme-material output {
    font-size: .8em;
}
.joint-toolbar.joint-theme-material .joint-toolbar-group {
    margin-left: 0px;
}

.joint-toolbar.joint-theme-material .joint-select-button-group .select-button-group-button.selected {
    color: white;
    border: 2px solid white;
}
.joint-toolbar.joint-theme-material .joint-select-box .select-box-selection:after {
    background: #828da6;
    border: 9px solid #828da6;
}
.joint-toolbar.joint-theme-material .joint-select-box .select-box-selection:before {
    background: #828da6;
}
.joint-toolbar.joint-theme-material .joint-select-box.joint-theme-material .select-box-selection {
    border: 2px solid #828da6;
}
.joint-toolbar.joint-theme-material .joint-widget textarea,
.joint-toolbar.joint-theme-material .joint-widget input {
    color: #ffffff;
 }
/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar.joint-theme-material .joint-widget[data-type="toggle"] span:first-child {
        line-height: 49px;
    }
    .joint-toolbar.joint-theme-material label {
        line-height: 49px;
    }
}
.joint-toolbar.joint-theme-modern {
    background: #f6f6f6;
    padding: 10px;
    box-sizing: border-box;
}
.joint-toolbar.joint-theme-modern label,
.joint-toolbar.joint-theme-modern .units,
.joint-toolbar.joint-theme-modern output {
    font-size: .8em;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-toolbar.joint-theme-modern .joint-widget[data-type="toggle"] span:first-child {
        line-height: 32px;
    }
    .joint-toolbar.joint-theme-modern label {
        line-height: 36px;
    }
}
.joint-widget[data-type="zoomSlider"] output,
.joint-widget[data-type="range"] output {
    min-width: 1.6em;
    display: inline-block;
    text-align: right;
}

.joint-widget[data-type="separator"] {
    border-right: 1px solid #b3b3b3;
}

.joint-widget[data-type="separator"]:after {
    content: "\00a0";
}

.joint-widget input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-widget input[type="range"]::-ms-fill-lower {
    background: transparent;
    border-color: transparent;
}

/* Toggle */

.joint-widget .toggle {
   position: relative;
   width: 97px;
   height: 14px;
}
.joint-widget .toggle input {
   top: 0;
   right: 0;
   bottom: 0;
   left: 0;
   -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
   filter: alpha(opacity=0);
   -moz-opacity: 0;
   opacity: 0;
   z-index: 100;
   position: absolute;
   width: 100%;
   height: 100%;
   cursor: pointer;
   box-sizing: border-box;
   padding: 0;
   box-shadow: none;
   -webkit-appearance: none;
}
.joint-widget .toggle span {
   display: block;
   width: 100%;
   height: 100%;
   border-radius: 40px;
   position: relative;
}
.joint-widget .toggle input:checked + span i {
   right: 0;
}
.joint-widget .toggle span i {
   display: block;
   height: 100%;
   width: 60%;
   border-radius: inherit;
   position: absolute;
   z-index: 2;
   right: 40%;
   top: 0;
}

/* Color Picker */
.joint-widget input[type='color'] {
    padding: 0;
    width: 150%;
    height: 150%;
    margin: -25%;
}

.joint-widget[data-type="colorPicker"] {
    box-sizing: border-box;
    overflow: hidden;
    width: 30px;
    height: 30px;
    border-radius: 4px;
}

.joint-widget[data-type="colorPicker"] {
    border: 1px solid lightgrey;
}

.joint-widget[data-type="colorPicker"].disabled {
    opacity: 0.5;
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-widget {
        float: left;
    }
}

@font-face {
    font-family: 'toolbar-icons-dark';
    src: url('data:application/octet-stream;base64,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') format('woff');
}

/* range */
.joint-widget.joint-theme-dark input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 20px;
    width: 60px;
    padding: 0;
    background: transparent;
}
.joint-widget.joint-theme-dark button:focus,
.joint-widget.joint-theme-dark input[type="range"]:focus {
    outline: none;
}
.joint-widget.joint-theme-dark input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}
.joint-widget.joint-theme-dark input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
    margin-top: -2px;
}
.joint-widget.joint-theme-dark input[type="range"]::-ms-thumb {
    margin-top: 0;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
}
.joint-widget.joint-theme-dark input[type="range"]::-moz-range-thumb {
    -moz-appearance: none;
    appearance: none;
    width: 8px;
    height: 8px;
    background: #8a9199;
    border-radius: 8px;
}
.joint-widget.joint-theme-dark input[type="range"]::-ms-fill-lower,
.joint-widget.joint-theme-dark input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #726bae, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}
.joint-widget.joint-theme-dark input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #726bae, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}
.joint-widget.joint-theme-dark input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #8f88da, #3cbebc);
    background: -o-linear-gradient(right, #726bae, #3cbebc);
    background: -moz-linear-gradient(right, #726bae, #3cbebc);
    background: linear-gradient(to right, #726bae, #3cbebc);
}

/* toggle - slider - disabled*/
.joint-widget.joint-theme-dark input[type="range"][disabled]::-ms-fill-lower,
.joint-widget.joint-theme-dark input[type="range"][disabled]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #b7b4cf, #b6e2e2);
    background: -o-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: -moz-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: linear-gradient(to right, #b7b4cf, #b6e2e2);
}
.joint-widget.joint-theme-dark input[type="range"][disabled]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #b7b4cf, #b6e2e2);
    background: -o-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: -moz-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: linear-gradient(to right, #b7b4cf, #b6e2e2);
}
.joint-widget.joint-theme-dark input[type="range"][disabled]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #7c69fd;
    background: -webkit-linear-gradient(left, #b7b4cf, #b6e2e2);
    background: -o-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: -moz-linear-gradient(right, #b7b4cf, #b6e2e2);
    background: linear-gradient(to right, #b7b4cf, #b6e2e2);
}

/* label */
label.joint-widget.joint-theme-dark {
    text-shadow: 1px 2px 1px #313538;
}

/* button */
button.joint-widget.joint-theme-dark {
    border: 1px solid #0f1110;
    width: 40px;
    height: 32px;
    color: #d1d2d4;
    border-radius: 3px;
    line-height: 12px;
    box-shadow: -1px -1px 0 0 hsl(0, 0%, 40%) inset;
    background-color: #52575b;
}

button.joint-widget.joint-theme-dark:disabled {
    background-color: #92979b;
    box-shadow: none;
}

button.joint-widget.joint-theme-dark:not([disabled]):hover {
    background: #92979b;
    background: -webkit-linear-gradient(#3b3f40, #454a4d);
    background: -o-linear-gradient(#3b3f40, #454a4d);
    background: -moz-linear-gradient(#3b3f40, #454a4d);
    background: linear-gradient(#3b3f40, #454a4d);
    box-shadow: -1px -1px 0 0 hsl(0, 0%, 35%) inset;
    color: #d3d3d5 !important;
}
button.joint-widget.joint-theme-dark[data-type="button"]:not(:empty) {
    width: auto;
    color: #b5b6ba;
}

.joint-widget.joint-theme-dark[data-type="undo"]:after,
.joint-widget.joint-theme-dark[data-type="redo"]:after,
.joint-widget.joint-theme-dark[data-type="zoomToFit"]:after,
.joint-widget.joint-theme-dark[data-type="zoomIn"]:after,
.joint-widget.joint-theme-dark[data-type="zoomOut"]:after,
.joint-widget.joint-theme-dark[data-type="fullscreen"]:after {
    font-family: "toolbar-icons-dark";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    margin: auto;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    font-size: 22px;
}

.joint-widget.joint-theme-dark[data-type="undo"]:after { content: '\e800'; }
.joint-widget.joint-theme-dark[data-type="redo"]:after { content: '\e801'; }
.joint-widget.joint-theme-dark[data-type="zoomToFit"]:after { content: '\e80a'; }
.joint-widget.joint-theme-dark[data-type="zoomIn"]:after { content: '\e806'; }
.joint-widget.joint-theme-dark[data-type="zoomOut"]:after { content: '\e807'; }
.joint-widget.joint-theme-dark[data-type="fullscreen"]:after { content: '\e809'; }

/* checkbox*/
.joint-widget.joint-theme-dark[data-type="checkbox"] input {
    position: relative;
    display: none;
}
.joint-widget.joint-theme-dark[data-type="checkbox"] span:first-child {
    display: inline-block;
}
.joint-widget.joint-theme-dark[data-type="checkbox"].disabled input + span {
    border: 1px solid #92979b;
    background-color: #92979b;
}
.joint-widget.joint-theme-dark[data-type="checkbox"] input + span {
    position: relative;
    left: 0;
    display: inline-block;
    vertical-align: top;
    width: 20px;
    min-width: 20px;
    height: 20px;
    border: 1px solid black;
    border-radius: 3px;
    background: #18191b;
}
.joint-widget.joint-theme-dark[data-type="checkbox"] input:checked + span:after {
    position: relative;
    left: 4px;
    top: 5px;
    display: block;
    width: 11px;
    height: 11px;
    content: ' ';
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAGXRFWHRDb21tZW50AENyZWF0ZWQgd2l0aCBHSU1QV4EOFwAAAa5JREFUGNMlwUGLUlEYBuD3O9doRqai6KbneKELCkGCm2YKI0imBtomuGo3+guMUNoK7RSi1SwKZjGEjrNpG0iEiItZ+QNuhM0USSg4d8qbnrdFzyPaeNestVMRuSSwK5I3AIRKxUKS8H3/WaFQ2Ot0Orsxa+2ZiIDkOURZCH4BOD89/bbMZDK3j44O97TWk37/s6NEROG/dQCG5OLH95Ol1nqtVqt90lpjOp264/E4HiO5UkopiD0DZSEiSwDI5XK3yuWyCwCNRgNRFO0rEXlCrAyA6yQJwPE8L9FsNj+KCIbD4aTdPrwHqLlyXdcOBoNusVh8JSJCMlapVD5ks1k3iiLU6/WnJI8JrCF//8E7S/L34g+3H+28vLO1uRuGIUmy1Wr91FqrRNLEtfE2kEqlHo5GI5JkEATs9Xq01jIIAvq+fzeZNI42Xlwb7yISSbOez+dfz2YzkqS1liRZKpWea526qo23kUiaK9p4MaWUioIvX19Uq9UWAIgIut3upN8ftAFcsNZedhznLwBHtPG2ABwDiO883n6TTqfDg4P3b+fz+QlEVgAiAEsAN/8BAiTM2/zlWnUAAAAASUVORK5CYII=') no-repeat;
}
/* toggle */
.joint-widget.joint-theme-dark .toggle {
  width: 72px;
}
.joint-widget.joint-theme-dark .toggle input {
  display: block;
  width: 100%;
  box-sizing: border-box;
  box-shadow: none;
  height: 12px;
}

.joint-widget.joint-theme-dark .toggle span,
.joint-widget.joint-theme-dark .toggle input:checked + span {
  background: #8b9094;
}
.joint-widget.joint-theme-dark.disabled .toggle span,
.joint-widget.joint-theme-dark.disabled .toggle input:checked + span {
  background: #d6d6d6;
}
.joint-widget.joint-theme-dark .toggle span {
  border-radius: 3px;
  box-shadow: none;
}
.joint-widget.joint-theme-dark .toggle span:before {
  background: #f6f6f6;
  box-shadow: none;
}
.joint-widget.joint-theme-dark .toggle span i:before {
  content: "off";
  position: absolute;
  right: -50%;
  top: 0;
  text-transform: uppercase;
  font-style: normal;
  font-weight: bold;
  color: #f5f5f5;
  font-family: Arial, sans-serif;
  font-size: 10px;
  line-height: 16px;
  margin-top: -1px;
  margin-right: -8px;
}
.joint-widget.joint-theme-dark .toggle input:checked + span i:before {
  content: "on";
  right: 100%;
  color: #f5f5f5;
  margin-right: 12px;
}
.joint-widget.joint-theme-dark .toggle span i {
  right: 50%;
  width: 50%;
  background: #414548;
  box-shadow: 0 0 3px #8b9094;
}
.joint-widget.joint-theme-dark.disabled .toggle span i {
    background-color: #92979b;
}
.joint-widget.joint-theme-dark .toggle input:checked + span i {
  right: 0;
}

/*inputs*/
.joint-widget.joint-theme-dark input[type="text"],
.joint-widget.joint-theme-dark input[type="number"],
.joint-widget.joint-theme-dark textarea {
  width: 100%;
  height: auto;
  line-height: 14px;
  text-shadow: none;
  box-shadow: none;
  box-sizing: border-box;
  outline: none;
  padding: 6px 10px;
  overflow: auto;

  color: #24282b;
  background: #92979b;
  background: -webkit-linear-gradient(#8b9094, #92979b);
  background: -o-linear-gradient(#8b9094, #92979b);
  background: -moz-linear-gradient(#8b9094, #92979b);
  background: linear-gradient(#8b9094, #92979b);
  border: 1px solid #42474a;
  border-radius: 3px;
}

.joint-widget.joint-theme-dark input[type="text"],
.joint-widget.joint-theme-dark input[type="number"] {
  height: 33px;
}

.joint-widget.joint-theme-dark input[type="text"]:disabled,
.joint-widget.joint-theme-dark input[type="number"]:disabled,
.joint-widget.joint-theme-dark textarea:disabled {
    color: #d6d6d6;
}

/* separator */
.joint-widget.joint-theme-dark[data-type="separator"] {
    box-shadow: 1px 0px 0px #161A1D;
    margin-right: 4px;
    line-height: 33px;
}

/*label space*/
.joint-widget.joint-theme-dark[data-type="inputText"] label,
.joint-widget.joint-theme-dark[data-type="inputNumber"] label,
.joint-widget.joint-theme-dark[data-type="inputTextArea"] label,
.joint-widget.joint-theme-dark[data-type="checkbox"] > span:first-child,
.joint-widget.joint-theme-dark[data-type="toggle"] > span {
    padding-right: 6px;
}

/* color picker */
.joint-widget.joint-theme-dark[data-type="colorPicker"] {
    border: 1px solid #0f1110;
    box-shadow: -1px -1px 0 0 hsl(0deg 0% 40%) inset;
}

/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
  /* change "6px padding" for visible text and same size as other browser */
  .joint-widget.joint-theme-dark input[type="text"],
  .joint-widget.joint-theme-dark input[type="number"] {
      padding: 0 0 0 10px;
  }
  /* "on/off" text in the center of the button  */
  .joint-widget.joint-theme-dark .toggle span i:before {
    margin-top: -1px;
  }
}

/* IE 8,9,10*/
@media screen\0 {
    .joint-widget.joint-theme-dark[data-type="zoomSlider"],
    .joint-widget.joint-theme-dark[data-type="range"],
    .joint-widget.joint-theme-dark[data-type="checkbox"] input + span {
      margin-top: 6px;
      margin-bottom: 6px;
    }
    /* select button group*/
    .joint-widget.joint-theme-dark[data-type="selectButtonGroup"] {
        padding-top: 2px;
        padding-bottom: 2px;
    }
}

.joint-widget.joint-theme-default input[type="range"] {
    margin: 0 0 0 6px;
    position: relative;
    border: 0;
    padding: 0;
    width: 80px;
}

.joint-widget.joint-theme-default input[type="range"]::-ms-thumb {
    position: relative;
    width: 6px;
    height: 12px;
    top: 0;
    z-index: 2;
    border: 1px solid lightgrey;
    background: white;
}

.joint-widget.joint-theme-default input[type="range"]::-ms-track {
    position: absolute;
    left: 0;
    top: 9px;
    content: ' ';
    width: 100%;
    height: 2px;
    background-color: #d3d3d3;
    border-radius: 3px;
}

button.joint-widget.joint-theme-default:not([disabled]):hover {
    background: #d3d3d3;
}

button.joint-widget.joint-theme-default:disabled {
    color: #9e9e9e;
}

button.joint-widget.joint-theme-default {
    outline: none;
    cursor: pointer;
    color: black;
    padding: 5px;
    background: white;
    border: 1px solid #d3d3d3;
    min-width: 30px;
    min-height: 30px;
    font-size: 12px;
    border-radius: 4px;
}

.joint-widget.joint-theme-default[data-type="undo"]:after {
    content: 'undo';
}

.joint-widget.joint-theme-default[data-type="redo"]:after {
    content: 'redo';
}

.joint-widget.joint-theme-default[data-type="zoomToFit"]:after {
    content: 'fit';
}

.joint-widget.joint-theme-default[data-type="zoomIn"]:after {
    content: '+';
}

.joint-widget.joint-theme-default[data-type="zoomOut"]:after {
    content: '-';
}
.joint-widget.joint-theme-default[data-type="fullscreen"]:after {
    content: 'fullscreen';
}
.joint-widget.joint-theme-default[data-type="toggle"] > input {
    margin-bottom: 0;
}

.joint-widget.joint-theme-default[data-type="toggle"] span:first-child {
    float: left;
}

.joint-widget.joint-theme-default[data-type="separator"] {
    line-height: 38px;
    margin-right: 4px;
    border-color: lightgray;
}

/*toggle*/
.joint-widget.joint-theme-default .toggle {
    width: 60px;
}
.joint-widget.joint-theme-default .toggle input:checked + span {
    background: white;
}
.joint-widget.joint-theme-default .toggle span {
    background: lightgrey;
    border: 1px solid lightgrey;
    border-radius: 40px;
}
.joint-widget.joint-theme-default.disabled .toggle input:checked + span i:before {
    color: #d3d3d3;
}
.joint-widget.joint-theme-default .toggle input:checked + span i:before {
    content: "on";
    right: 115%;
    color: black;
}
.joint-widget.joint-theme-default.disabled .toggle span i:before {
    color: #9e9e9e;
}
.joint-widget.joint-theme-default .toggle span i:before {
    content: "off";
    position: absolute;
    top: 50%;
    margin-top: -5px;
    right: -80%;
    text-transform: uppercase;
    color: black;
    font-family: Helvetica, Arial, sans-serif;
    font-size: 10px;
    font-style: normal;
}
.joint-widget.joint-theme-default .toggle span i {
    background: white;
    width: 50%;
    right: 50%;
}
.joint-widget.joint-theme-default .toggle input:checked + span i {
    background: lightgrey;
}

/*inputs*/
.joint-widget.joint-theme-default input[type="text"],
.joint-widget.joint-theme-default input[type="number"],
.joint-widget.joint-theme-default textarea {
    width: 100%;
    height: auto;
    line-height: 14px;
    border: 1px solid lightgrey;
    box-sizing: border-box;
    outline: none;
    padding: 5px;
    font-size: 12px;
}

.joint-widget.joint-theme-default .joint-select-button-group {
    font-size: 16px;
}

/*label space*/
.joint-widget.joint-theme-default[data-type="inputText"] label,
.joint-widget.joint-theme-default[data-type="inputNumber"] label,
.joint-widget.joint-theme-default[data-type="inputTextArea"] label,
.joint-widget.joint-theme-default[data-type="checkbox"] > span,
.joint-widget.joint-theme-default[data-type="toggle"] > span {
    padding-right: 2px;
}

/* IE 8,9,10*/
@media screen\0 {
    /* toggle and select button group*/
    .joint-widget.joint-theme-default[data-type="toggle"],
    .joint-widget.joint-theme-default[data-type="selectButtonGroup"] {
        padding-top: 3px;
        padding-bottom: 3px;
    }
    /* range*/
    .joint-widget.joint-theme-default[data-type="zoomSlider"],
    .joint-widget.joint-theme-default[data-type="range"] {
        margin-top: 8px;
        margin-bottom: 8px;
    }
    .joint-widget.joint-theme-default input[type="range"] + output {
        padding-top: 10px;
        padding-bottom: 10px;
    }
}

/* range */
.joint-widget.joint-theme-material input[type="range"] {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
    width: 80px;
    padding: 5px;
    background: transparent;
}
.joint-widget.joint-theme-material .units {
    padding-right: 4px;
}

.joint-widget.joint-theme-material input[type="range"]:focus {
    outline: none;
}

.joint-widget.joint-theme-material input[type="range"]::-ms-track {
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.joint-widget.joint-theme-material input[type="range"]:disabled::-webkit-slider-thumb {
    background-color: #808bc6;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-ms-thumb {
    background-color: #808bc6;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-moz-range-thumb {
    background-color: #808bc6;
}
.joint-widget.joint-theme-material input[type="range"]::-webkit-slider-thumb {
    margin-top: -5px;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-widget.joint-theme-material input[type="range"]::-ms-thumb {
    margin-top: 0;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-widget.joint-theme-material input[type="range"]:active::-webkit-slider-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-widget.joint-theme-material input[type="range"]:active::-ms-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-widget.joint-theme-material input[type="range"]::-moz-range-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    box-sizing: border-box;
    border-radius: 50%;
    background: rgb(63,81,181);
    border: none;
    transition: transform 0.18s cubic-bezier(0.4, 0, 0.2, 1), border 0.18s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.18s cubic-bezier(0.4, 0, 0.2, 1), background 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.18s cubic-bezier(0.4, 0, 0.2, 1);
}
.joint-widget.joint-theme-material input[type="range"]:active::-moz-range-thumb {
    background-image: none;
    background: rgb(63,81,181);
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
}
.joint-widget.joint-theme-material input[type="range"]::-ms-fill-lower,
.joint-widget.joint-theme-material input[type="range"]::-ms-fill-upper {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
}
.joint-widget.joint-theme-material input[type="range"]::-ms-fill-lower {
    background: #3f51b5
}
.joint-widget.joint-theme-material input[type="range"]::-moz-range-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
}
.joint-widget.joint-theme-material input[type="range"]::-moz-range-progress {
    height: 3px;
    background: #3f51b5;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-webkit-slider-runnable-track {
    background-color: #d0d8e8;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-moz-range-progress {
    background-color: #d0d8e8;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-ms-fill-lower {
    background-color: #d0d8e8;
}
.joint-widget.joint-theme-material input[type="range"]:disabled::-ms-fill-upper {
    background-color: #d0d8e8;
}
.joint-widget.joint-theme-material input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 3px;
    background: #5fa9ee;
}

/* button */
button.joint-widget.joint-theme-material {
    position: relative;
    height: 49px;
    min-width: 49px;
    color: white;
    font-size: 15px;
    background: transparent;
    outline: none;
    cursor: pointer;
    box-sizing: border-box;
    padding: 4px 11px;
    margin: 0;
    border-width: 0 2px 0 2px;
    border-style: solid;
    -webkit-border-image:
        -webkit-gradient(linear, 0 100%, 0 0, from(#5e6b88), to(#717d98)) 1 100%;
    -webkit-border-image:
        -webkit-linear-gradient(bottom, #5e6b88, #717d98) 1 100%;
    -moz-border-image:
        -moz-linear-gradient(bottom, #5e6b88, #717d98) 1 100%;
    -o-border-image:
        -o-linear-gradient(bottom, #5e6b88, #717d98) 1 100%;
    border-image:
        linear-gradient(to top, #5e6b88, #717d98) 1 100%;
}

.joint-toolbar.joint-theme-material .joint-toolbar-group + .joint-toolbar-group button.joint-widget.joint-theme-material,
button.joint-widget.joint-theme-material + button.joint-widget.joint-theme-material {
    border-width: 0 2px 0 0;
    margin-left: 0;
}

button.joint-widget.joint-theme-material:hover {
    box-shadow: inset 0 -4px 0 #5fa9ee;
}

button.joint-widget.joint-theme-material:disabled:hover {
    box-shadow: none;
}

.joint-widget.joint-theme-material[data-type="toggle"] span:first-child,
.joint-widget.joint-theme-material[data-type="checkbox"] span:first-child {
    display: inline-block;
    padding-left: 4px;
    padding-right: 4px;
}

/* checkbox*/
.joint-widget.joint-theme-material[data-type="checkbox"] input {
    position: relative;
    display: none;
}

.joint-widget.joint-theme-material[data-type="checkbox"].disabled input + span {
    border: 2px solid #d0d8e8;
}
.joint-widget.joint-theme-material[data-type="checkbox"] input + span {
    display: inline-block;
    box-sizing: border-box;
    width: 16px;
    min-width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
    border: 2px solid #5fa9ee;
    border-radius: 2px;
    z-index: 2;
    position: relative;
}

.joint-widget.joint-theme-material[data-type="checkbox"] input + span:after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8ZGVmcz4KICAgIDxjbGlwUGF0aCBpZD0iY2xpcCI+CiAgICAgIDxwYXRoCiAgICAgICAgIGQ9Ik0gMCwwIDAsMSAxLDEgMSwwIDAsMCB6IE0gMC44NTM0Mzc1LDAuMTY3MTg3NSAwLjk1OTY4NzUsMC4yNzMxMjUgMC40MjkzNzUsMC44MDM0Mzc1IDAuMzIzMTI1LDAuOTA5Njg3NSAwLjIxNzE4NzUsMC44MDM0Mzc1IDAuMDQwMzEyNSwwLjYyNjg3NSAwLjE0NjU2MjUsMC41MjA2MjUgMC4zMjMxMjUsMC42OTc1IDAuODUzNDM3NSwwLjE2NzE4NzUgeiIKICAgICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZSIgLz4KICAgIDwvY2xpcFBhdGg+CiAgICA8bWFzayBpZD0ibWFzayIgbWFza1VuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgbWFza0NvbnRlbnRVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giPgogICAgICA8cGF0aAogICAgICAgICBkPSJNIDAsMCAwLDEgMSwxIDEsMCAwLDAgeiBNIDAuODUzNDM3NSwwLjE2NzE4NzUgMC45NTk2ODc1LDAuMjczMTI1IDAuNDI5Mzc1LDAuODAzNDM3NSAwLjMyMzEyNSwwLjkwOTY4NzUgMC4yMTcxODc1LDAuODAzNDM3NSAwLjA0MDMxMjUsMC42MjY4NzUgMC4xNDY1NjI1LDAuNTIwNjI1IDAuMzIzMTI1LDAuNjk3NSAwLjg1MzQzNzUsMC4xNjcxODc1IHoiCiAgICAgICAgIHN0eWxlPSJmaWxsOiNmZmZmZmY7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmUiIC8+CiAgICA8L21hc2s+CiAgPC9kZWZzPgogIDxyZWN0CiAgICAgd2lkdGg9IjEiCiAgICAgaGVpZ2h0PSIxIgogICAgIHg9IjAiCiAgICAgeT0iMCIKICAgICBjbGlwLXBhdGg9InVybCgjY2xpcCkiCiAgICAgc3R5bGU9ImZpbGw6IzAwMDAwMDtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZSIgLz4KPC9zdmc+Cg==");
    mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8ZGVmcz4KICAgIDxjbGlwUGF0aCBpZD0iY2xpcCI+CiAgICAgIDxwYXRoCiAgICAgICAgIGQ9Ik0gMCwwIDAsMSAxLDEgMSwwIDAsMCB6IE0gMC44NTM0Mzc1LDAuMTY3MTg3NSAwLjk1OTY4NzUsMC4yNzMxMjUgMC40MjkzNzUsMC44MDM0Mzc1IDAuMzIzMTI1LDAuOTA5Njg3NSAwLjIxNzE4NzUsMC44MDM0Mzc1IDAuMDQwMzEyNSwwLjYyNjg3NSAwLjE0NjU2MjUsMC41MjA2MjUgMC4zMjMxMjUsMC42OTc1IDAuODUzNDM3NSwwLjE2NzE4NzUgeiIKICAgICAgICAgc3R5bGU9ImZpbGw6I2ZmZmZmZjtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZSIgLz4KICAgIDwvY2xpcFBhdGg+CiAgICA8bWFzayBpZD0ibWFzayIgbWFza1VuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgbWFza0NvbnRlbnRVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giPgogICAgICA8cGF0aAogICAgICAgICBkPSJNIDAsMCAwLDEgMSwxIDEsMCAwLDAgeiBNIDAuODUzNDM3NSwwLjE2NzE4NzUgMC45NTk2ODc1LDAuMjczMTI1IDAuNDI5Mzc1LDAuODAzNDM3NSAwLjMyMzEyNSwwLjkwOTY4NzUgMC4yMTcxODc1LDAuODAzNDM3NSAwLjA0MDMxMjUsMC42MjY4NzUgMC4xNDY1NjI1LDAuNTIwNjI1IDAuMzIzMTI1LDAuNjk3NSAwLjg1MzQzNzUsMC4xNjcxODc1IHoiCiAgICAgICAgIHN0eWxlPSJmaWxsOiNmZmZmZmY7ZmlsbC1vcGFjaXR5OjE7c3Ryb2tlOm5vbmUiIC8+CiAgICA8L21hc2s+CiAgPC9kZWZzPgogIDxyZWN0CiAgICAgd2lkdGg9IjEiCiAgICAgaGVpZ2h0PSIxIgogICAgIHg9IjAiCiAgICAgeT0iMCIKICAgICBjbGlwLXBhdGg9InVybCgjY2xpcCkiCiAgICAgc3R5bGU9ImZpbGw6IzAwMDAwMDtmaWxsLW9wYWNpdHk6MTtzdHJva2U6bm9uZSIgLz4KPC9zdmc+Cg==");
    background: 0 0;
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: background;
}
.joint-widget.joint-theme-material[data-type="checkbox"] input:checked + span:after {
    background: #3f51b5 url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K");
}
.joint-widget.joint-theme-material[data-type="checkbox"] input:disabled:checked + span:after {
    background-color: #808bc6;
}
.joint-widget.joint-theme-material[data-type="checkbox"] input:checked + span {
    border: 2px solid #3f51b5;
    background: white;
}
.joint-widget.joint-theme-material[data-type="checkbox"] input:disabled:checked + span {
    border: 2px solid #808bc6;
}
/* toggle */
.joint-widget.joint-theme-material .toggle {
    height: 14px;
    width: 36px;
    border-radius: 14px;
}
.joint-widget.joint-theme-material .toggle input:checked + span {
    background: #5fa9ee;
}
.joint-widget.joint-theme-material.disabled .toggle input:checked + span {
    background-color: #d0d8e8;
}
.joint-widget.joint-theme-material.disabled .toggle span {
    background-color: #d6d6d6;

}
.joint-widget.joint-theme-material .toggle span {
    background: rgba(0,0,0,.26);
    color: #f6f6f6;
    border-radius: 14px;
    box-shadow: none;
}
.joint-widget.joint-theme-material .toggle span i:before {
    content: '';
}
.joint-widget.joint-theme-material .toggle span i {
    right: 50%;
    width: 50%;
    top: -2px;
    height: 130%;
    left: 0;
    border-radius: 50%;
    cursor: pointer;
    background: #fafafa;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12);
    transition-duration: .28s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-property: left;
}
.joint-widget.joint-theme-material.disabled .toggle input:checked + span i{
    background-color: #808bc6;
}
.joint-widget.joint-theme-material .toggle input:checked + span i {
    right: 0;
    position: absolute;
    left: 20px;
    background: #3f51b5;
    box-shadow: 0 3px 4px 0 rgba(0,0,0,.14),0 3px 3px -2px rgba(0,0,0,.2),0 1px 8px 0 rgba(0,0,0,.12);
}
.joint-widget.joint-theme-material[data-type="checkbox"] span,
.joint-widget.joint-theme-material[data-type="toggle"] span,
.joint-widget.joint-theme-material[data-type="toggle"] div {
    pointer-events: none;
}

.joint-widget.joint-theme-material[data-type="undo"]:after,
.joint-widget.joint-theme-material[data-type="redo"]:after,
.joint-widget.joint-theme-material[data-type="zoomToFit"]:after,
.joint-widget.joint-theme-material[data-type="zoomIn"]:after,
.joint-widget.joint-theme-material[data-type="zoomOut"]:after,
.joint-widget.joint-theme-material[data-type="fullscreen"]:after {
    display: block;
    width: 33px;
    height: 33px;
    content: ' ';
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-image: url('data:image/png;base64,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');
}

.joint-widget.joint-theme-material[data-type="zoomIn"]:after { background-position: -47px -54px; }
.joint-widget.joint-theme-material[data-type="zoomOut"]:after { background-position: -91px -5px; }
.joint-widget.joint-theme-material[data-type="zoomToFit"]:after { background-position: -5px -54px; }
.joint-widget.joint-theme-material[data-type="undo"]:after { background-position: -6px -8px; }
.joint-widget.joint-theme-material[data-type="redo"]:after { background-position: -44px -8px; }
.joint-widget.joint-theme-material[data-type="fullscreen"]:after { background-position: -88px -51px; }
.joint-widget.joint-theme-material[data-type="zoomIn"]:disabled:after { background-position: -173px -54px; }
.joint-widget.joint-theme-material[data-type="zoomOut"]:disabled:after { background-position: -217px -5px; }
.joint-widget.joint-theme-material[data-type="zoomToFit"]:disabled:after { background-position: -131px -54px; }
.joint-widget.joint-theme-material[data-type="undo"]:disabled:after { background-position: -132px -8px; }
.joint-widget.joint-theme-material[data-type="redo"]:disabled:after { background-position: -170px -8px; }
.joint-widget.joint-theme-material[data-type="fullscreen"]:disabled:after { background-position: -214px -51px; }

/* separator */
.joint-widget.joint-theme-material[data-type="separator"] {
    background:
        -webkit-gradient(linear, 0 100%, 0 0, from(#5e6b88), to(#717d98));
    background:
        -webkit-linear-gradient(bottom, #5e6b88, #717d98);
    background:
        -moz-linear-gradient(bottom, #5e6b88, #717d98);
    background:
        -o-linear-gradient(bottom, #5e6b88, #717d98);
    background:
        linear-gradient(to top, #5e6b88, #717d98);
    height: 49px;
    width: 2px;
    border: none;
}

/* label */
label.joint-widget.joint-theme-material {
    padding-left: 4px;
    padding-right: 4px;
}

/* animation click on button */
button.joint-widget.joint-theme-material:active {
   background: #5e6d9d;
}

/* animation ripple checkbox, toggle */
.joint-widget.joint-theme-material input.toggle + span:after,
.joint-widget.joint-theme-material[data-type="checkbox"] input + span:before {
    content: '';
    position: absolute;
    z-index: 2;
    box-sizing: border-box;
    border-radius: 50%;
    background: #3f51b5;
    transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
    transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
    border-radius: 50%;
    opacity: 0;
    pointer-events: none;
    top: -14px;
    right: -14px;
    overflow: hidden;
    width: 40px;
    height: 40px;
}
.joint-widget.joint-theme-material.is-in-action input.toggle + span:after,
.joint-widget.joint-theme-material[data-type="checkbox"].is-in-action input + span:before {
    opacity: 0.3;
}
.joint-widget.joint-theme-material input.toggle + span:after {
    top: -12px;
    left: -12px;
}
.joint-widget.joint-theme-material input.toggle:checked + span:after {
    transform: translate(20px, 0);
}
/* inputs*/
.joint-widget.joint-theme-material[data-type="inputNumber"] div,
.joint-widget.joint-theme-material[data-type="textarea"] div,
.joint-widget.joint-theme-material[data-type="inputText"] div {
    position: relative;
}

.joint-widget.joint-theme-material textarea,
.joint-widget.joint-theme-material input {
    color: #55627b;
    border: none;
    outline: none;
    background: transparent;
    line-height: 20px;
    height: 20px;
}

.joint-widget.joint-theme-material textarea,
.joint-widget.joint-theme-material input[type="number"],
.joint-widget.joint-theme-material input[type="text"] {
    border-bottom: 2px solid #5fa9ee;
}

.joint-widget.joint-theme-material[data-type="inputNumber"] > .input-wrapper:after,
.joint-widget.joint-theme-material[data-type="textarea"] > .input-wrapper:after,
.joint-widget.joint-theme-material[data-type="inputText"] > .input-wrapper:after {
    background: #3f51b5;
    bottom: 0;
    content: '';
    height: 2px;
    left: 45%;
    position: absolute;
    transition-duration: .2s;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    z-index: -1;
    width: 10px;
    transition-property: width, left, z-index;
    display: block;
}

.joint-widget.joint-theme-material[data-type="textarea"] > .input-wrapper:after {
    bottom: 1px;
}

.joint-widget.joint-theme-material input[type="text"]:disabled,
.joint-widget.joint-theme-material input[type="number"]:disabled,
.joint-widget.joint-theme-material textarea:disabled {
    color: #d6d6d6;
    border-bottom: 2px solid #d0d8e8;
}

.joint-widget.joint-theme-material.is-focused[data-type="inputNumber"] > .input-wrapper:after,
.joint-widget.joint-theme-material.is-focused[data-type="textarea"] > .input-wrapper:after,
.joint-widget.joint-theme-material.is-focused[data-type="inputText"] > .input-wrapper:after {
    z-index: 1000;
    left:0;
    width: 100%;
}

/* color picker */
.joint-widget.joint-theme-material[data-type="colorPicker"] {
    width: 49px;
    height: 49px;
    border-radius: 0;
    border-bottom: none;
    border-top: none;
    border-left: 2px solid #5e6b88;
    border-right: 2px solid #5e6b88;
    position: relative;
}

.joint-widget.joint-theme-material input[type="color"] {
    height: 150%;
}

.joint-widget.joint-theme-material[data-type="colorPicker"]::before,
.joint-widget.joint-theme-material[data-type="colorPicker"]::after {
    content: '';
    position: absolute;
    height: 100%;
    top: 0;
}

.joint-widget.joint-theme-material[data-type="colorPicker"]::before {
    left: 0;
    width: 2px;
    background: linear-gradient(to left, transparent 0%, #5e6b88 100%);
}

.joint-widget.joint-theme-material[data-type="colorPicker"]::after {
    right: 0;
    width: 2px;
    background: linear-gradient(to right, transparent 0%, #5e6b88 100%);
}

@media (-webkit-min-device-pixel-ratio: 0) and (min-resolution: 0.001dpcm) {
    .joint-widget.joint-theme-material input[type="text"],
    .joint-widget.joint-theme-material input[type="number"] {
        padding: 0 0 0 10px;
    }
    .joint-widget.joint-theme-material[data-type="textarea"] > .input-wrapper:after {
        bottom: 3px;
    }
}

/* IE 8,9,10*/
@media screen\0 {

    /* select button group*/
    .joint-widget.joint-theme-material[data-type="selectButtonGroup"] {
        padding-top: 9px;
        padding-bottom: 9px;
    }
    /* range*/
    .joint-widget.joint-theme-material[data-type="zoomSlider"],
    .joint-widget.joint-theme-material[data-type="range"] {
        margin-top: 14px;
        margin-bottom: 14px;
        margin-right: 4px;
    }
    /* selectBox*/
    .joint-widget.joint-theme-material[data-type="selectBox"] {
        margin-top: 7px;
        margin-bottom: 7px;
    }
    /* checkbox*/
    .joint-widget.joint-theme-material[data-type="checkbox"] {
        padding-top: 2px;
        padding-bottom: 2px;
    }

    /* button */
    button.joint-widget.joint-theme-material {
        border-bottom: none;
        border-top: none;
        border-left: 2px solid #5e6b88;
        border-right: 2px solid #5e6b88;
    }
}

/* range */
.joint-widget.joint-theme-modern input[type="range"] {
    vertical-align: middle;
    width: 80px;
    color: #ddd;
    background: transparent;
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 20px;
    border: none;
    outline: none;
    padding: 0;
}

.joint-widget.joint-theme-modern input[type="range"]:focus {
    outline: none;
}

.joint-widget.joint-theme-modern input[type="range"]::-ms-thumb {
    position: relative;
    width: 8px;
    height: 8px;
    top: 0;
    z-index: 2;
    background: #fff;
    box-shadow: 1px 1px 1px #888;
    border-radius: 8px;
}

.joint-widget.joint-theme-modern input[type="range"]::-moz-range-thumb {
    position: relative;
    width: 8px;
    height: 8px;
    top: 0;
    z-index: 2;
    background: #fff;
    box-shadow: 1px 1px 1px #888;
    border-radius: 8px;
    appearance: none;
}

.joint-widget.joint-theme-modern input[type="range"]::-webkit-slider-thumb {
    position: relative;
    width: 8px;
    height: 8px;
    top: 0;
    z-index: 2;
    background: #fff;
    box-shadow: 1px 1px 1px #888;
    border-radius: 8px;
    -webkit-appearance: none;
    appearance: none;
}

.joint-widget.joint-theme-modern input[type="range"]:after {
    position: absolute;
    left: 0;
    top: 9px;
    content: ' ';
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #fb6eb6, #c658fd);
    border-radius: 3px;
}

.joint-widget.joint-theme-modern input[type="range"]:disabled:after {
    background: linear-gradient(to right, #fbe0ee, #daacf1);
}

.joint-widget.joint-theme-modern input[type="range"]::-moz-range-track {
    position: absolute;
    left: 0;
    top: 9px;
    content: ' ';
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #fb6eb6, #c658fd);
    border-radius: 3px;
}

.joint-widget.joint-theme-modern input[type="range"]::-ms-track {
    position: absolute;
    left: 0;
    top: 9px;
    content: ' ';
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #fb6eb6, #c658fd);
    border-radius: 3px;
}

.joint-widget.joint-theme-modern input[type="range"][disabled]::-moz-range-track {
    background: linear-gradient(to right, #fbe0ee, #daacf1);
}
.joint-widget.joint-theme-modern input[type="range"][disabled]::-ms-track {
    background: linear-gradient(to right, #fbe0ee, #daacf1);
}


/* button */
button.joint-widget.joint-theme-modern {
    position: relative;
    height: 32px;
    min-width: 32px;
    color: #6b6c8a;
    font-size: 11px;
    background: transparent;
    border: 1px solid #6b6c8a;
    border-radius: 18px;
    outline: none;
    cursor: pointer;
    box-sizing: border-box;
    padding: 4px 11px;
    margin: 0;
}

button.joint-widget.joint-theme-modern:not([disabled]):hover {
    color: #fff;
    background: #6a6b8a;
}

/* checkbox*/
.joint-widget.joint-theme-modern[data-type="checkbox"] span:first-child {
    display: inline-block;
}

.joint-widget.joint-theme-modern[data-type="checkbox"] input {
    position: relative;
    display: none;
}

.joint-widget.joint-theme-modern[data-type="checkbox"].disabled input + span {
    background-color: #e2e2ee;
    border-color: #c6c7e2;
}
.joint-widget.joint-theme-modern[data-type="checkbox"] input + span {
    position: relative;
    left: 0;
    display: inline-block;
    vertical-align: top;
    width: 21px;
    height: 22px;
    background-color: #fff;
    border: 1px solid #6b6c8a;
    border-radius: 4px;
}

.joint-widget.joint-theme-modern[data-type="checkbox"] input:checked + span:after {
    position: absolute;
    left: 4px;
    top: 3px;
    display: block;
    width: 18px;
    height: 16px;
    content: ' ';
    background: transparent url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAQCAMAAAAs2N9uAAABSlBMVEUrrqorsqwrtq4surEtvbQvw7svx70wzMIxvboyzscztrozxsM0nq00srk1n681obA1qbY1rbc2o7I2pbU2qLc3q7o3vcQ3xsg4rrw5sL85tME5uMM6tsQ6v8o7ucc7vMk7vcs8v8w8wc7///81n688wc4xzsQ1obA6tsQ2o7IvqKsxpK0twbcx0MY5tMEsras5tME2pbUwysA5sL87vcsssa05sL82qLc5uMM4rrw3q7o1n68sta8uxLo2ysk4vMUsurEwzMI3tsA1obA5sL86wss5sL80vb8tvbQ1n68vx702o7I6tsQ3wcY1obA1o7I2pbUxzsQzwsAyvLs1prQ5tME2qLctwbcvw7syu7s1qbY3q7o5sL8wysA4rrwzycQuxLovw7svx70wysAwzMIxzsQyw8AyyME1rbc1sbk2tLw2t785uMM6ucYRnxwPAAAAYHRSTlMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwYGCQwPDxUYGBsbHiQkMDMzNjZCRUhOUVRgeHh4h5OTmaiurrS9vcPJ0tLb3uTk5Ofq9vb29vb5+fwT6nnAAAAAw0lEQVR4AV3LVXPCQADE8au7N22aprlLru7u7lLcHZLg8P1fWTIBAr+nnf/MkskenJPpbnc/62TGaevNvcfJvMPOn2cXx4WOE2/wiCAttl2F4xd9s0hCy22idN8/JyCJtpt0+XlgShSRPt/PVuAyWfkeHMdAOg7EXiXpMFSN7I9KgCRfR7Nf2/+13MPQqgxIivKk6/564Xd4SWlCopS6DKOYOp2gFiTG2HnGNF9G1pgFSYWPvG9zWQVA0mDj8WBMs3HeAD86KVYGq1XTAAAAAElFTkSuQmCC') 0 0 no-repeat;
}

/*toggle*/
.joint-widget.joint-theme-modern .toggle {
  width: 72px;
}
.joint-widget.joint-theme-modern .toggle input {
  display: block;
  width: 100%;
  box-sizing: border-box;
  box-shadow: none;
  height: 12px;
}
.joint-widget.joint-theme-modern .toggle input:checked + span {
  background: #31d0c6;
}
.joint-widget.joint-theme-modern.disabled .toggle input:checked + span {
  background-color: #9cddd9;
}
.joint-widget.joint-theme-modern .toggle span {
  background: #c6c7e2;
  border-radius: 40px;
  box-shadow: none;
}
.joint-widget.joint-theme-modern.disabled .toggle span {
  background: #e2e2ee;
}
.joint-widget.joint-theme-modern .toggle span:before {
  background: #f6f6f6;
  box-shadow: none;
}
.joint-widget.joint-theme-modern .toggle span i:before {
  content: "off";
  position: absolute;
  right: -50%;
  top: 0;
  text-transform: uppercase;
  font-style: normal;
  font-weight: bold;
  color: #6a6c8a;
  font-family: Arial, sans-serif;
  font-size: 10px;
  line-height: 16px;
  margin-top: -1px;
  margin-right: -8px;
}
.joint-widget.joint-theme-modern.disabled .toggle span i:before {
    color: #a0a1b1;
}

.joint-widget.joint-theme-modern .toggle input:checked + span i:before {
  content: "on";
  right: 100%;
  color: #f6f6f6;
  margin-right: 12px;
}
.joint-widget.joint-theme-modern .toggle span i {
  right: 50%;
  width: 50%;
  background: #f6f6f6;
  box-shadow: 0 0 3px #111;
}
.joint-widget.joint-theme-modern .toggle input:disabled + span i {
  box-shadow: 0 0 3px #8c8c8c;
}

/*inputs*/
.joint-widget.joint-theme-modern input[type="text"],
.joint-widget.joint-theme-modern input[type="number"],
.joint-widget.joint-theme-modern textarea {
    width: 100%;
    height: auto;
    line-height: 14px;
    text-shadow: none;
    background: transparent;
    border: 2px solid #e6e6e6;
    box-shadow: none;
    box-sizing: border-box;
    outline: none;
    padding: 6px;
    overflow: auto;
}

.joint-widget.joint-theme-modern input[type="text"]:disabled,
.joint-widget.joint-theme-modern input[type="number"]:disabled,
.joint-widget.joint-theme-modern textarea:disabled {
    color: #d6d6d6;
}

.joint-widget.joint-theme-modern input[type="text"],
.joint-widget.joint-theme-modern input[type="number"] {
    height: 33px;
}

.joint-widget.joint-theme-modern[data-type="undo"]:hover,
.joint-widget.joint-theme-modern[data-type="redo"]:hover,
.joint-widget.joint-theme-modern[data-type="zoomToFit"]:hover,
.joint-widget.joint-theme-modern[data-type="zoomIn"]:hover,
.joint-widget.joint-theme-modern[data-type="zoomOut"]:hover,
.joint-widget.joint-theme-modern[data-type="fullscreen"]:hover {
    background: transparent;
}

.joint-widget.joint-theme-modern[data-type="undo"]:after,
.joint-widget.joint-theme-modern[data-type="redo"]:after,
.joint-widget.joint-theme-modern[data-type="zoomToFit"]:after,
.joint-widget.joint-theme-modern[data-type="zoomIn"]:after,
.joint-widget.joint-theme-modern[data-type="zoomOut"]:after,
.joint-widget.joint-theme-modern[data-type="fullscreen"]:after {
    display: block;
    width: 31px;
    height: 31px;
    content: ' ';
    background-color: transparent;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-image: url('data:image/png;base64,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');
}

.joint-widget.joint-theme-modern[data-type="zoomIn"]:after { background-position: -31px -31px }
.joint-widget.joint-theme-modern[data-type="zoomIn"]:disabled:after { background-position: 0 -31px }
.joint-widget.joint-theme-modern[data-type="zoomIn"]:not([disabled]):hover:after { background-position: -62px -31px }

.joint-widget.joint-theme-modern[data-type="zoomOut"]:after { background-position: -31px 0 }
.joint-widget.joint-theme-modern[data-type="zoomOut"]:disabled:after { background-position: 0 0 }
.joint-widget.joint-theme-modern[data-type="zoomOut"]:not([disabled]):hover:after { background-position: -62px 0 }

.joint-widget.joint-theme-modern[data-type="zoomToFit"]:after { background-position: -31px -217px }
.joint-widget.joint-theme-modern[data-type="zoomToFit"]:disabled:after { background-position: 0 -217px }
.joint-widget.joint-theme-modern[data-type="zoomToFit"]:not([disabled]):hover:after { background-position: -62px -217px }

.joint-widget.joint-theme-modern[data-type="undo"]:after { background-position: -31px -155px }
.joint-widget.joint-theme-modern[data-type="undo"]:disabled:after { background-position: 0 -155px }
.joint-widget.joint-theme-modern[data-type="undo"]:not([disabled]):hover:after { background-position: -62px -155px }

.joint-widget.joint-theme-modern[data-type="redo"]:after { background-position: -31px -186px }
.joint-widget.joint-theme-modern[data-type="redo"]:disabled:after { background-position: 0 -186px }
.joint-widget.joint-theme-modern[data-type="redo"]:not([disabled]):hover:after { background-position: -62px -186px }

.joint-widget.joint-theme-modern[data-type="fullscreen"]:after { background-position: -31px -93px }
.joint-widget.joint-theme-modern[data-type="fullscreen"]:disabled:after { background-position: 0 -93px }
.joint-widget.joint-theme-modern[data-type="fullscreen"]:not([disabled]):hover:after { background-position: -62px -93px }

.joint-widget.joint-theme-modern[data-type="undo"],
.joint-widget.joint-theme-modern[data-type="redo"],
.joint-widget.joint-theme-modern[data-type="zoomToFit"],
.joint-widget.joint-theme-modern[data-type="zoomIn"],
.joint-widget.joint-theme-modern[data-type="zoomOut"],
.joint-widget.joint-theme-modern[data-type="fullscreen"] {
    position: relative;
    top: -1px;
    border: none;
    padding: 0;
}
/* separator */
.joint-widget.joint-theme-modern[data-type="separator"] {
    line-height: 36px;
    margin-right: 3px;
}

/*label space*/
.joint-widget.joint-theme-modern[data-type="inputText"] label,
.joint-widget.joint-theme-modern[data-type="inputNumber"] label,
.joint-widget.joint-theme-modern[data-type="inputTextArea"] label,
.joint-widget.joint-theme-modern[data-type="checkbox"] > span,
.joint-widget.joint-theme-modern[data-type="toggle"] > span {
    padding-right: 2px;
}

/* color picker */
.joint-widget.joint-theme-modern[data-type="colorPicker"] {
    border-radius: 50%;
    border: 1px solid #6b6c8a;
    width: 31px;
    height: 31px;
}

/* Chrome only */
@media all and (-webkit-min-device-pixel-ratio:0) and (min-resolution: .001dpcm) {
    /* change "6px padding" for visible text and same size as other browser */
    .joint-widget.joint-theme-modern input[type="text"],
    .joint-widget.joint-theme-modern input[type="number"] {
        padding: 0 0 0 10px;
    }
    /* "on/off" text in the center of the button  */
    .joint-widget.joint-theme-modern .toggle span i:before {
        margin-top: 0;
    }
}
/* IE 8,9,10*/
@media screen\0 {

    /* select button group*/
    .joint-widget.joint-theme-modern[data-type="selectButtonGroup"] {
        padding-top: 3px;
        padding-bottom: 3px;
    }
    /* range*/
    .joint-widget.joint-theme-modern[data-type="zoomSlider"],
    .joint-widget.joint-theme-modern[data-type="range"] {
        margin-top: 6px;
        margin-bottom: 6px;
    }
    /* checkbox*/
    .joint-widget.joint-theme-modern[data-type="checkbox"] {
        padding-top: 2px;
        padding-bottom: 2px;
    }
}

.printarea {
    position: relative;
}

.printarea.print-ready {
    display: none;
}

.printarea.preview {
    overflow: hidden !important;
    background: #fff !important;
}

@media print {

    html,
    html > body.joint-print {
        position: relative !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    html > body.joint-print > * {
        display: none !important;
    }

    html > body.joint-print > .printarea {
        display: block !important;
    }

    .printarea {
        page-break-after: always;
        left: 0 !important;
        top: 0 !important;
        overflow: hidden !important;
        background: #fff !important;
        margin: 0mm !important;
        padding: 0mm !important;
    }

    .printarea.print-ready {
        display: none;
    }
}

