Copyright 2013 client IO
http://client.io

JointJS+ General License Statement and Limited Warranty
======================================================

Definitions
-----------

 "JointJS+" shall mean a programmer's toolkit
 for building interactive diagramming applications as originally
 created by client IO.

 “JointJS+” and “Rappid” remain equivalents, “Rappid” being
 the older version of the name. Onwards, we shall refer to “JointJS+”.

 "Software" shall mean JointJS+, as originally created by
 client IO.

 "Licensee" is you, only if you agree to be bound by the terms
 and conditions set forth in this Agreement.

 "Source code" means the JavaScript files and other related files in the
 Software package.


This license statement and limited warranty constitutes a legal agreement
("License Agreement") between You (either as an individual or a single entity)
and client IO for the software product ("Software") identified above,
including any software, media, and accompanying on-line or printed
documentation. By installing, copying, or otherwise using the
Software, you are agreeing to be bound by the terms of this Agreement. If you
do not agree to the terms of this Agreement, you are not authorized to use
this Software.


BY INSTALLING, COPYING, OR OTHERWISE USING THE SOFTWARE, YOU AGREE TO BE BOUND
BY ALL OF THE TERMS AND CONDITIONS OF THE LICENSE AGREEMENT.

Upon your acceptance of the terms and conditions of the License Agreement,
client IO grants to you the right to use the Software in the manner provided
below. This license is perpetual, no additional payment is required to
maintain it, with the exception of you breaking any part of this license, in
which case you lose all rights under the license.

You may transfer the rights granted to you under this agreement in and to the
Software and documentation on a permanent basis provided you retain no copies,
the recipient agrees to the terms of the License Agreement and the total number
of developers licensed to use the software does not exceed the number of
licensed developers at the time of license purchase. client IO must be informed
in writing of the transfer.

Except as provided in the License Agreement, you may not transfer,
rent, lease, lend, copy, modify, translate, sublicense, time-share or
electronically transmit or receive the Software, media or documentation. When
transferring licenses, the whole of the licensed product must be transferred.

In addition to the other terms contained herein, We grant to You a revocable, non-exclusive,
non-transferable and non-sublicensable license to install and use the Software (the "Trial License")
strictly for Your internal evaluation and review purposes and not for production purposes.
This Trial License applies only if You have registered with Us for a Trial License of the Software and
shall be effective for thirty (30) consecutive days following the date of registration ("the Trial Period").
You may only register for a Trial License once in any eighteen month period. You agree not to use a
Trial License for any purpose other than determining whether to purchase a license to the Software.
You are explicitly not permitted to distribute the Software to any user outside the Organization on whose
behalf you have undertaken this license. Your rights to use the Trial License will immediately terminate
upon the earlier of (i) the expiration of the Trial Period, or (ii) such time that You purchase a license
to the Software. We reserve the right to terminate Your Trial License at any time in Our absolute and
sole discretion.

YOU ACKNOWLEDGE THAT TRIAL SOFTWARE MIGHT PLACE WATERMARKS ON OUTPUT, CONTAIN LIMITED FUNCTIONALITY,
FUNCTION FOR A LIMITED PERIOD OF TIME, OR LIMIT THE FUNCTIONALITY OR TIME OF FUNCTIONING OF ANY OUTPUT.
ACCESS TO AND/OR USE OF ANY FILES OR OUTPUT CREATED WITH SUCH SOFTWARE IS ENTIRELY AT YOUR OWN RISK.
WE ARE LICENSING THE SOFTWARE ON AN "AS IS" BASIS AT YOUR OWN RISK AND WE DISCLAIM ANY WARRANTY OR
LIABILITY TO YOU OF ANY KIND.


Developer License Terms
-----------------------

To develop products using the Software, you require a number of development
licenses equal to the number of engineers developing the application area
that specifically uses the Software. This is the maximum number
of such developers over any 1 month sliding window. A development license is
not required in order to build an application that uses the software.

A developer License may be installed on any number of computers
at any time. client IO grants to you non exclusive license to install
and use the Software for the sole purposes of
designing, developing and testing application programs which you create.

In addition to the right to install the complete software on the specified
number of computers, the Source code may also be placed on storage devices
for the purposes of version control, automated building and archiving.
The entire downloaded Software package may also be archived for backup purposes.


Additional Redistribution Terms for Software
---------------------------------------------

client IO hereby grants to you the worldwide, non exclusive, perpetual right
under all intellectual property rights in and to the Software to
incorporate the Software in whole or in part in your offerings and to
redistribute the Software as incorporated to your customers and distributors
and resellers.

JointJS+ must be part of a larger system. You may not expose the JointJS+ API for
developer use in your product.


Updates
-------

Free updates are provided for all versions of the Software for the period of twelve (12) months
from the time of the license purchase. Updates are performed by downloading the updated Software package
through a link provided in the purchase process or sent to an email address provided in the purchase process.

Marketing
---------

client IO is permitted to reference you as a user of the Software in customer
lists on any of the web sites owned by client IO, in presentations to clients and at trade events.

Limited Warranty
----------------

client IO warrants that as provided the Software does not contain any malicious
code inserted to affect the functioning of the Software or any computer that
the Software runs on. Any developer using JointJS+ must have a broad
understanding of security issue relating to web application and must take all
reasonable steps at a system level to mitigate those risks.

client IO warrants that the Software does not infringe any patent, copyright or
design secret of any third party. client IO is not aware of any facts upon which
such a claim for infringement could be based.


TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, CLIENT IO AND ITS
SUPPLIERS DISCLAIM ALL OTHER WARRANTIES AND CONDITIONS, EITHER EXPRESSED OR
IMPLIED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE, AND NON-INFRINGEMENT, WITH REGARD TO
THE SOFTWARE, AND THE PROVISION OF OR FAILURE TO PROVIDE SUPPORT SERVICES. THIS
LIMITED WARRANTY GIVES YOU SPECIFIC LEGAL RIGHTS. YOU MAY HAVE OTHERS, WHICH
VARY FROM STATE/JURISDICTION TO STATE/JURISDICTION.

LIMITATION OF LIABILITY TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, IN
NO EVENT SHALL CLIENT IO OR ITS SUPPLIERS BE LIABLE FOR ANY SPECIAL,
INCIDENTAL, INDIRECT, OR CONSEQUENTIAL DAMAGES WHATSOEVER (INCLUDING, WITHOUT
LIMITATION, DAMAGES FOR LOSS OF BUSINESS PROFITS, BUSINESS INTERRUPTION, LOSS
OF BUSINESS INFORMATION, OR ANY OTHER PECUNIARY LOSS) ARISING OUT OF THE USE OF
OR INABILITY TO USE THE SOFTWARE PRODUCT OR THE PROVISION OF OR FAILURE TO
PROVIDE SUPPORT SERVICES, EVEN IF CLIENT IO HAS BEEN ADVISED OF THE
POSSIBILITY OF SUCH DAMAGES. BECAUSE SOME STATES AND JURISDICTIONS DO NOT
ALLOW THE EXCLUSION OR LIMITATION OF LIABILITY, THE ABOVE LIMITATION MAY NOT
APPLY TO YOU.


General Provisions
-------------------

This License Agreement may only be modified in writing signed by you and
client IO. If any provision of this License Agreement is found void or
unenforceable, the remainder will remain valid and enforceable according to its
terms. If any remedy provided is determined to have failed for its essential
purpose, all limitations of liability and exclusions of damages set forth in
the Limited Warranty shall remain in effect.

Governing Law and Jurisdiction
-------------------------------

This Agreement shall be subject to and governed by the Law of The Netherlands.
Any dispute arising out of or in connection with this Agreement shall
be exclusively dealt with by the courts of The Netherlands. This License
Agreement gives you specific legal rights; you may have others which vary from
state to state and from country to country. client IO reserves all rights not
specifically granted in this License Agreement.

Version 3.0 - 25.02.2022
