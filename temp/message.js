// PluginForms/Message.tsx
import React, { useEffect, useRef, useState } from 'react';
import { FilePlus2, FileVideo2, Globe, ImagePlus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import ReactQuill from 'react-quill';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '@/components/ui/accordion';
import DropdownButton from '@/components/dropdownButton';
import { FileUpload, FileType } from '@/components/file-upload';
import { platformOptions, PlatformType, MessageLanguage } from '@/modules/editor/utils/constants';
import { languageOptions } from '@/lib/constant';
import type { ModuleData } from '../types';
import { EditorInstance, RichTextEditor } from '@/modules/rich-text-editor';

interface MessageProps {
  details: ModuleData;
  updateDetails: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setModuleDetails: React.Dispatch<React.SetStateAction<ModuleData>>;
  error: any[];
  isEdit: boolean;
}
interface EditorRef {
  instance: EditorInstance;
}
const MESSAGE_NODE_ATTACHMENT_CONFIG = [
  { type: FileType.Image, key: 'images', labelKey: 'common.image', icon: <ImagePlus size={18} /> },
  { type: FileType.File, key: 'files', labelKey: 'common.file', icon: <FilePlus2 size={18} /> },
  { type: FileType.Video, key: 'videos', labelKey: 'common.video', icon: <FileVideo2 size={18} /> },
];

export default function Message({ details, setModuleDetails, isEdit }: MessageProps) {
  const { t } = useTranslation();
  const process = details.process || {};
  const [messageState, setMessageState] = useState({
    content: '',
  });
  const editorRef = useRef<EditorRef | null>(null);
  const [messageText, setMessageText] = useState<string>(process.messageText || '');
  const [platform, setPlatform] = useState(details.settings?.platform || PlatformType.Web);
  const [language, setLanguage] = useState(details.settings?.language || MessageLanguage.English);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [uploadedVideos, setUploadedVideos] = useState<string[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);

  useEffect(() => {
    setModuleDetails(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        platform,
        language,
      },
      process: {
        ...prev.process,
        messageText,
      },
    }));
  }, [platform, language, messageText]);

  const handleFileSelect = (file: File, type: FileType) => {
    const url = URL.createObjectURL(file);
    if (type === FileType.Image) {
      setUploadedImages(prev => [...prev, url]);
    } else if (type === FileType.Video) {
      setUploadedVideos(prev => [...prev, url]);
    } else if (type === FileType.File) {
      setUploadedFiles(prev => [...prev, url]);
    }
  };

  const handleFileRemove = (type: FileType, index: number) => {
    if (type === FileType.Image) {
      setUploadedImages(prev => prev.filter((_, i) => i !== index));
    } else if (type === FileType.Video) {
      setUploadedVideos(prev => prev.filter((_, i) => i !== index));
    } else if (type === FileType.File) {
      setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    }
  };

  return (
    <>
      {/* Language and Platform */}
      <div className="flex items-center px-4 pt-4 space-x-2">
        <DropdownButton
          value={language}
          onChange={val => setLanguage(val)}
          options={languageOptions}
          className="min-w-24"
        />
        <DropdownButton
          value={platform}
          onChange={val => setPlatform(val)}
          options={platformOptions}
          icon={<Globe className="w-5 h-5 text-tertiary-600" />}
          className="min-w-24"
        />
      </div>

      {/* Message Text */}
      <div className="p-4">
        {/* <ReactQuill
          theme="snow"
          value={messageText}
          onChange={setMessageText}
          placeholder={t('common.writeMessage')}
          className="bg-transparent !rounded-2xl"
        /> */}
        <RichTextEditor
          content={messageText}
          onChange={setMessageText}
          placeholder="Write Messages"
          // onEditorState={state => (editorRef.current = state)}
          className="w-full"
          isToolbar={true}
          // selectedFiles={selectedFiles}
          // onFileRemove={index => setSelectedFiles(prev => prev.filter((_, i) => i !== index))}
        />
      </div>

      {/* File Uploads */}
      <div className="px-4 space-y-4">
        <Accordion type="multiple">
          {MESSAGE_NODE_ATTACHMENT_CONFIG.map(item => (
            <AccordionItem key={item.key} value={item.key}>
              <AccordionTrigger>
                <div className="flex items-center space-x-2">
                  {item.icon}
                  <span className="text-sm font-medium text-tertiary-600">{t(item.labelKey)}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <FileUpload
                  type={item.type}
                  onFileSelect={file => handleFileSelect(file, item.key as any)}
                  onFileRemove={url => handleFileRemove(item.key as any, url)}
                  showUrlOption
                />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </>
  );
}
