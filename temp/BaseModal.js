import React, {
    useRef,
    useState,
    useEffect,
    ReactNode,
    ChangeEvent,
    FormEvent,
    KeyboardEvent,
  } from 'react';
  import CloseIcon from '@mui/icons-material/Close';
  import copyIcon from '../assets/common/pluginIcons/copy.svg';
  import editIcon from '../assets/common/pluginIcons/edit.svg';
  import { getFormSchema } from './utils/schema';
  import { useValidation } from './utils/useValidation';
  import { getModuleIcon, getModuleText } from '../utils/config';
  import { useTranslation } from 'react-i18next';
  import { ModuleData } from '../types';
  import { Sheet, SheetContent } from '@/components/ui/sheet';
  
  import './style.css';
  
  interface Position {
    top: number;
    left: number;
  }
  
  interface BaseModalProps {
    id: string;
    position: Position;
    type: string;
    moduleData: ModuleData;
    startUrl?: string;
    isEdit: boolean;
    isPublishedEnabled: boolean;
    handleClose: () => void;
    handleSave: (data: ModuleData, id: string, validate?: boolean) => void;
    children: ReactNode;
  }
  
  const BaseModal: React.FC<BaseModalProps> = ({
    id,
    position: { top, left },
    type,
    moduleData,
    startUrl,
    isEdit,
    isPublishedEnabled,
    handleClose,
    handleSave,
    children,
  }) => {
    const { t } = useTranslation();
  
    const [error, setError] = useState<any[]>([]);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isEditable, setIsEditable] = useState(false);
    const [toastVisible, setToastVisible] = useState(false);
    const [imageError, setImageError] = useState('');
    const [moduleDetails, setModuleDetails] = useState<ModuleData>(
      JSON.parse(JSON.stringify(moduleData))
    );
    const [name, setName] = useState<string>(moduleDetails.settings.nodeName || '');
    const [customImage, setCustomImage] = useState<string>(moduleData.settings.image || '');
  
    const { schema } = getFormSchema(type);
    const { validateFields } = useValidation({ schema });
  
    const textRef = useRef<HTMLTextAreaElement>(null);
  
    useEffect(() => {
      if (isPublishedEnabled && error.length === 0) {
        const copy = structuredClone(moduleDetails);
  
        validateFields(copy)
          .then(() => setError([]))
          .catch(err => {
            setIsSubmitted(true);
            setError(err);
          });
      }
    }, [isPublishedEnabled, validateFields, moduleDetails]);
  
    const hideSubmit = ['choice', 'whatsapp'];
  
    const updateObject = (data: ModuleData, path: string, value: string) => {
      const pathArray = path.split('.');
      let pointer: any = data;
  
      for (let i = 0; i < pathArray.length; i++) {
        const key = pathArray[i];
        if (!(key in pointer)) {
          console.error('Invalid path:', path);
          return;
        }
  
        if (i === pathArray.length - 1) {
          pointer[key] = value;
        } else {
          pointer = pointer[key];
        }
      }
    };
  
    const updateDetails = (e: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
  
      if (!name) return;
  
      const copy = structuredClone(moduleDetails);
      updateObject(copy, name, value);
      setModuleDetails(copy);
  
      if (isSubmitted) {
        validateFields(copy)
          .then(() => setError([]))
          .catch(setError);
      }
    };
  
    const childrenWithProps = React.Children.map(children, child =>
      React.isValidElement(child)
        ? React.cloneElement(child, {
            details: moduleDetails,
            updateDetails,
            setModuleDetails,
            startUrl,
            error,
            isEdit,
          })
        : child
    );
  
    const handleNameChange = (e: ChangeEvent<HTMLInputElement>) => {
      setName(e.target.value);
    };
  
    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && name) {
        setIsEditable(false);
        const copy = structuredClone(moduleData);
        copy.settings.nodeName = name;
        handleSave(copy, id, false);
      }
    };
  
    const handleCopy = () => {
      if (textRef.current) {
        textRef.current.select();
        document.execCommand('copy');
        setToastVisible(true);
        setTimeout(() => setToastVisible(false), 2000);
      }
    };
  
    const convertBase64 = (file: File): Promise<string | ArrayBuffer | null> =>
      new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
      });
  
    const updateImage = async (e: ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;
  
      if (!file.type.includes('image/png') && !file.type.includes('image/svg+xml')) {
        setImageError(t('editor.unsupportedFile'));
        return;
      }
  
      setImageError('');
      const base64 = await convertBase64(file);
  
      const copy = structuredClone(moduleData);
      copy.settings.image = base64;
      setCustomImage(base64 as string);
      handleSave(copy, id, false);
    };
  
    const submitForm = async (e: FormEvent) => {
      e.preventDefault();
      setIsSubmitted(true);
  
      const copy = structuredClone(moduleDetails);
      copy.settings.nodeName = name;
  
      try {
        await validateFields(copy);
        setError([]);
        handleSave(copy, id);
        handleClose();
      } catch (err) {
        setError(err);
      }
    };
  
    const adjustedTop = Math.max(0, top);
    const isFullSheet = ['form', 'appStart', 'message', 'flowConnector'].includes(type);
  
    return (
      <Sheet open={!!type}>
        <SheetContent hideOverlay side="left" className="w-96 p-0 flex flex-col">
          <form
            id="setting-form"
            onSubmit={submitForm}
            className={`relative flex flex-col bg-white overflow-hidden h-full ${
              isFullSheet ? 'form-message-modal' : 'w-80 border border-gray-300 rounded-lg'
            }`}
            style={{
              left: isFullSheet ? 0 : left,
              top: isFullSheet ? 0 : adjustedTop,
              zIndex: 20,
            }}
          >
            {/* Close Button */}
            <div className="mt-3">
              <button onClick={handleClose} className="absolute top-6 right-3 text-gray-400">
                <CloseIcon />
              </button>
  
              <h2 className="text-xs pl-2">
                <span className="text-gray-400">Node ID: </span>
                <span className="text-gray-600">{id}</span>
              </h2>
  
              {/* Header */}
              <div className="flex mt-3 pl-3 mb-3">
                {isEditable || name.length > 25 ? (
                  <input
                    type="text"
                    value={name}
                    onChange={handleNameChange}
                    onKeyDown={handleKeyDown}
                    style={{ height: '35px', width: '200px', borderRadius: '10px' }}
                  />
                ) : (
                  <>
                    <span className="mr-2">{getModuleText(type)}</span>
                    {isEdit && (
                      <button
                        onClick={() => setIsEditable(true)}
                        style={{ height: '20px', width: '20px' }}
                      >
                        <img src={editIcon} alt="Edit" style={{ height: '14px', width: '14px' }} />
                      </button>
                    )}
                  </>
                )}
              </div>
  
              <hr />
            </div>
  
            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto mt-4 space-y-4 pr-1">
              {type === 'http' && (
                <>
                  <h2 className="font-bold">Node Logo</h2>
                  <div className="flex items-center">
                    <img src={customImage || getModuleIcon('http')} className="mr-2 http-img" />
                    <label className="custom-file-upload">
                      <input type="file" style={{ display: 'none' }} onChange={updateImage} />
                      <img src={editIcon} alt="Edit" style={{ height: '12px', width: '12px' }} />
                    </label>
                  </div>
                  {imageError && <div className="text-red-500 text-xs mt-2">{imageError}</div>}
                  <h2 className="font-bold mt-3" style={{ color: '#232323' }}>
                    Configure HTTP
                  </h2>
                </>
              )}
  
              {childrenWithProps}
            </div>
  
            {/* Footer */}
            {!hideSubmit.includes(type) && isEdit && (
              <div className="flex justify-end gap-2 mb-6 pt-4 mr-4">
                <button
                  type="button"
                  onClick={handleClose}
                  className="border px-6 py-1 h-10 w-28 rounded-sm"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="submit"
                  className="bg-primary-500 text-white px-6 h-10 w-28 py-1 rounded-sm"
                >
                  {t('common.save')}
                </button>
              </div>
            )}
          </form>
        </SheetContent>
      </Sheet>
    );
  };
  
  export default BaseModal;
  