import React, { useEffect, useRef, useState } from 'react';
import { X, Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppDispatch } from '@/hooks/useRedux';
import { toggleFormNode } from '@/store/slices/uiSlice';
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'; // Adjust import based on your setup
import type { ModuleData } from '../types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FloatingField } from '@/components/ui/floating-label';

interface FormField {
  id: string;
  name: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
}

interface FormBuilderModalProps {
  details: ModuleData;
  setModuleDetails: React.Dispatch<React.SetStateAction<ModuleData>>;
  updateDetails: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error: any[];
  isEdit: boolean;
}

const FIELD_TYPES = [
  { label: 'text_field', value: 'text' },
  { label: 'date', value: 'date' },
  { label: 'future_date', value: 'future_date' },
  { label: 'past_date', value: 'past_date' },
  { label: 'custom_date', value: 'custom_date' },
];

export default function FormBuilderModal({ details, setModuleDetails }: FormBuilderModalProps) {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const didInitialize = useRef(false);

  const [prompt, setPrompt] = useState('');
  const [formType, setFormType] = useState('multi-ask-form');
  const [validateRequired, setValidateRequired] = useState(true);
  const [formId, setFormId] = useState(() => details?.coordinates?.nodeData?.id);
  const [fields, setFields] = useState<FormField[]>([
    {
      id: '1',
      label: '',
      name: '',
      type: 'text',
    },
  ]);

  useEffect(() => {
    if (!details || didInitialize.current) return;

    const process = details?.process || {};
    const settings = details?.settings || {};

    setFormId(details?.coordinates?.nodeData?.id);
    // setFormType(process.formConfig?.formType || 'multi');
    setPrompt(process.prompt || process.formConfig?.fields?.[0]?.label || '');
    setValidateRequired(settings.validateRequired ?? true);

    const checkForForms = (process.formConfig?.fields || []).map((field: any) => ({
      id: crypto.randomUUID(),
      name: field.name || '',
      type: field.type || 'text',
      label: field.label || '',
      required: field.required ?? false,
      options: field.options || [],
    }));

    if (checkForForms.length > 0) {
      setFields(checkForForms);
    }

    didInitialize.current = true;
  }, [details]);

  useEffect(() => {
    if (!didInitialize.current) return;
    setModuleDetails(prev => ({
      ...prev,
      process: { ...prev.process, prompt },
    }));
  }, [prompt]);

  useEffect(() => {
    if (!didInitialize.current) return;
    setModuleDetails(prev => ({
      ...prev,
      settings: { ...prev.settings, formType, validateRequired },
    }));
  }, [formType, validateRequired]);

  useEffect(() => {
    if (!didInitialize.current) return;
    setModuleDetails(prev => ({
      ...prev,
      process: {
        ...prev.process,
        formId,
        formConfig: {
          formType,
          fields: fields.map(f => ({
            name: f.name || f.label.replace(/\s+/g, '').toLowerCase(),
            type: f.type,
            label: prompt,
            required: f.required ?? false,
            ...(f.options ? { options: f.options } : {}),
          })),
          prompt,
        },
        prompt,
      },
    }));
  }, [fields, formId, prompt]);

  const updateField = (id: string, key: keyof FormField, value: any) => {
    setFields(prev => prev.map(field => (field.id === id ? { ...field, [key]: value } : field)));
  };

  const addField = () => {
    setFields(prev => [
      ...prev,
      {
        id: crypto.randomUUID(),
        name: '',
        type: 'text',
        label: '',
      },
    ]);
  };

  const removeField = (id: string) => {
    setFields(prev => prev.filter(field => field.id !== id));
  };

  const handleToggle = () => dispatch(toggleFormNode());
  console.log('Module details', details);

  return (
    <>
      <div className="px-4 pt-4 space-y-4">
        {/* <Input
          label="Prompt"
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          type="text"
          placeholder="Prompt"
        /> */}
        <FloatingField
          label="Prompt"
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          type="text"
          className="h-11"
        />
      </div>

      <div className="flex-1 px-4 space-y-4 mt-4 overflow-auto">
        {fields.map((field, index) => {
          const isLast = index === fields.length - 1;
          const isOnlyOne = fields.length === 1;

          return (
            <div key={field.id} className="flex items-center mt-4">
              {/* <Select>
                <SelectContent
                  onChange={() => updateField(field.id, 'type', fie.value)}
                ></SelectContent>
              </Select> */}
              <Select
                value={field.type}
                onValueChange={value => updateField(field.id, 'type', value)}
              >
                <SelectTrigger className="w-32 h-11 my-1 text-black font-normal border border-tertiary-300 bg-gray-100 text-sm rounded-md rounded-tr-none rounded-br-none">
                  <SelectValue placeholder={t('text_field')} />
                </SelectTrigger>
                <SelectContent>
                  {FIELD_TYPES.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {t(option.label)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                value={field.name}
                onChange={e => updateField(field.id, 'name', e.target.value)}
                placeholder="Enter label"
                className="flex-1 h-11 border-t border-r border-b border-l-0 border-tertiary-300 rounded-tl-none rounded-bl-none "
                type="text"
              />

              {isOnlyOne && (
                <Button
                  type="button"
                  variant="ghost"
                  className="text-primary px-0 py-0 border m-2"
                  onClick={addField}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              )}

              {!isOnlyOne && (
                <>
                  {!isLast && (
                    <Button
                      type="button"
                      variant="ghost"
                      className="text-muted-foreground px-0 py-0 m-2"
                      onClick={() => removeField(field.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  {isLast && (
                    <Button
                      type="button"
                      variant="ghost"
                      className="text-primary px-0 py-0 border  m-2"
                      onClick={addField}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  )}
                </>
              )}
            </div>
          );
        })}
      </div>
    </>
  );
}
