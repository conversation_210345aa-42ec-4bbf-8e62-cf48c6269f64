import React, { useEffect, useRef, useState } from 'react';
import { X, Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAppDispatch } from '@/hooks/useRedux';
import { toggleFormNode } from '@/store/slices/uiSlice';
import DropdownButton from '@/components/dropdownButton';
import { FloatingField } from '@/components/ui/floating-label';
import { useTranslation } from 'react-i18next';
import type { ModuleData } from '../types';
import { KnowledgeUnitType } from '@/types';
import {
  useGetKnowledgeUnitsQuery,
  useUpdateKnowledgeUnitMutation,
} from '@/store/api/knowledgeApi';
import { useSearchParams } from 'react-router-dom';
import { setActiveFlow } from '@/store/slices/flowsSlice';
import { useSelector, useDispatch } from 'react-redux';

interface FormField {
  id: string;
  name: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
}

interface FormBuilderModalProps {
  details: ModuleData;
  setModuleDetails: React.Dispatch<React.SetStateAction<ModuleData>>;
  updateDetails: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error: any[];
  isEdit: boolean;
}

const FIELD_TYPES = [
  { label: 'Text Field', value: 'text' },
  { label: 'Email', value: 'email' },
  { label: 'Select', value: 'select' },
  { label: 'Radio', value: 'radio' },
  { label: 'Checkbox', value: 'checkbox' },
];

export default function FormBuilderModal({ details, setModuleDetails }: FormBuilderModalProps) {
  const dispatch = useAppDispatch();
  const reduxDispatch = useDispatch();
  const selectedFlow = useSelector((state: any) => state.flows.activeFlowId);
  const { t } = useTranslation();
  const didInitialize = useRef(false);
  const [prompt, setPrompt] = useState('');
  const [formType, setFormType] = useState('multi');
  const [validateRequired, setValidateRequired] = useState(true);
  const [formId, setFormId] = useState(() => crypto.randomUUID());
  const [fields, setFields] = useState('');
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!; 
  console.log('flowID', selectedFlow);

  const [updateIntent] = useUpdateKnowledgeUnitMutation();

  const {
    data: intentsData,
    isLoading,
    error,
  } = useGetKnowledgeUnitsQuery({
    filter: {
      botId: {
        eq: botId,
      },
      type: {
        eq: KnowledgeUnitType.INTENT,
      },
    },
  });
  const intentItems = intentsData?.data.items.map(item => ({
    label: item.name,
    value: item.id,
  }));
  console.log('intentItems', intentsData);

  // Initialize from details once
  useEffect(() => {
    if (!details || didInitialize.current || !intentItems) return;

    const process = details?.process || {};
    const settings = details?.settings || {};

    const savedApId = process.intentId;

    setFormId(process.formId || crypto.randomUUID());
    setFormType(process.formConfig?.formType || 'multi');
    setPrompt(process.prompt || process.formConfig?.fields?.[0]?.label || '');
    setValidateRequired(settings.validateRequired ?? true);

    // Set saved apId as selected
    setFields(savedApId);

    didInitialize.current = true;
  }, [details, intentItems]); 

  // Sync fields
  useEffect(() => {
    if (!didInitialize.current) return;

    setModuleDetails(prev => ({
      ...prev,
      process: {
        intentId: fields,
      },
    }));
  }, [fields]);

  const updateField = (id: string) => {
    const selected = intentItems?.find(item => item.value === id);

    setFields(id);

    setModuleDetails(prev => ({
      ...prev,
      process: {
        ...prev.process,
        intentId: id,
        intentLabel: selected?.label ?? '',
      },
    }));
    console.log('Module details after update', details);

    updateIntent({
      id,
      flowId: selectedFlow.id,
      intentId: id, // explicitly send apId
      intentLabel: selected?.label, // send label too
    });
  };

  // const handleToggle = () => dispatch(toggleFormNode());

  // You can now use `selectedFlow` and `onSelectFlow` as needed in your component
  // For example, log the selected flow:
  // console.log('Selected Flow:', selectedFlow);

  return (
    <>
      {/* Header */}

      {/* Field list */}
      <div className="flex-1 px-4 space-y-4 overflow-auto">
        <FloatingField
          label="Label"
          as="select"
          value={fields}
          className='mt-4'
          onChange={value => updateField(value)}
          options={intentItems}
        />

        {/* <Button variant="ghost" className="text-primary mt-2" onClick={addField}>
          <Plus className="w-4 h-4 mr-1" />
          {t('common.addField')}
        </Button> */}
      </div>
    </>
  );
}
