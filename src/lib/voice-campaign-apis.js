import getAPIMap from "../api_map.js";
import axios from "axios";
import { tableDetails } from "../constants_map.js";
import getIP from "../getIP.js";
const IP = getIP();

const getCampaignList = async ({ queryKey, pageParam = 0 }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${queryKey[1].token}`,
    },
  };
  var url =
    IP +
    getAPIMap("listCampaign") +
    `?pgsize=${
      queryKey[0].includes("EXPORT") ? "1000000" : tableDetails.pageSize
    }&pgindex=${pageParam}&channels=voice`;
  if (queryKey[6]) {
    if (queryKey[6] === "CUSTOM") {
      url =
        url +
        `&duration=${queryKey[6]}&stdt=${queryKey[2]}&enddt=${queryKey[3]}`;
    } else {
      url = url + `&duration=${queryKey[6]}`;
    }
  }
  if (queryKey[5]) {
    url = url + `&name=${queryKey[5]}`;
  }
  if (queryKey[4].status) {
    url = url + `&status=${queryKey[4].status}`;
  }
  if (queryKey[4].scheduleType) {
    url = url + `&schtype=${queryKey[4].scheduleType}`;
  }
  if (queryKey[4].campaignType) {
    url = url + `&cmptype=${queryKey[4].campaignType}`;
  }
  const response = await axios.get(url, config);

  return { response, nextPage: pageParam + 1 };
};

const callerIdList = () => {
  // const config = {
  //   headers: {
  //     Authorization: `Bearer ${queryKey[1].token}`,
  //   },
  // };
  //var url = IP + getAPIMap("getServices") + `?ch=${queryKey[2]}`;
  //return axios.get(url, config);
  var data = [
    { callerId: "1", no: "9440981394" },
    { callerId: "2", no: "8878878897" },
  ];
  return data;
};
const getPromptList = async ({ queryKey }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${queryKey[1].token}`,
    },
  };
  var url = IP + getAPIMap("listfiles") + `?pgsize=100000&pgindex=1`;

  const response = await axios.get(url, config);

  return response;
};
const getCampainById = async (userDetails, id, channel) => {
  let url = IP + getAPIMap("campainById");
  url = url.replace("{id}", id);

  url = url.replace("{channel}", channel);
  url = url.replace("{recurrenceId}", 0);

  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  const response = await axios.get(url, config);
  return response;
};
const createIVR = async ({ userDetails, values }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  var url = IP + getAPIMap("createIVR");
  var response = await axios.post(url, values, config);

  return response;
};
const Job1CampaignSubmit = async ({ payload, userDetails }) => {
  //debugger;
  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  var url = IP + getAPIMap("Job1CampaignSubmit");
  var response = await axios.post(url, payload, config);
  return response;
};
const Job1CampaignUpload = async ({ payload, userDetails }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  var url = IP + getAPIMap("Job1CampaignUpload");
  var response = await axios.post(url, payload, config);
  return response;
};
const Job3CampaignUpload = async ({ payload, userDetails }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  var url = IP + getAPIMap("Job3CampaignUpload");
  var response = await axios.post(url, payload, config);
  return response;
};
const Job3CampaignSubmit = async ({ payload, userDetails }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  var url = IP + getAPIMap("Job3CampaignSubmit");
  var response = await axios.post(url, payload, config);
  return response;
};
const getAllNumbersByuser = async ({ queryKey /*pageParam = 1*/ }) => {
  //debugger;
  const config = {
    headers: {
      Authorization: `Bearer ${queryKey[1].token}`,
    },
  };
  var url = IP + getAPIMap("getSenderAddressListByuserId");

  if (queryKey[2]) {
    url = url.concat(`?paging=0`);
  }
  if (queryKey[3]) {
    url = url.concat(
      `&country=${queryKey[3]}&userId=${queryKey[1]?.sub}&capabilities=voice`
    );
  }

  const response = await axios.get(url, config);
  return response;
};
const voiceCampaignPieDetails = async ({ queryKey }) => {
  const config = {
    headers: {
      Authorization: `Bearer ${queryKey[1]}`,
    },
  };
  let url = IP + getAPIMap("campaignPieDetailsVoice");
  url = url.replace("{channel}", queryKey[3]);
  url = url.replace("{campaignId}", queryKey[2]);

  return await axios.get(url, config);
};
const cancelCampaign = async (options) => {
  let url = IP + getAPIMap("cancelCampaign");
  url = url.replace("{id}", options.campaignId);
  url = url.replace("{channel}", options.channelName);
  url = url.replace("{recurrenceId}", "all");

  const config = {
    headers: {
      Authorization: `Bearer ${options.userDetails.token}`,
    },
  };
  const response = await axios.post(url, {}, config);
  return response;
};
const audioForTextToSpeech = async (options) => {
  const url = IP + getAPIMap("audioForTextToSpeech");

  const config = {
    headers: {
      Authorization: `Bearer ${options.userDetails.token}`,
    },
  };
  const response = await axios.post(url, options.body, config);
  return response;
};
const dtmfApi = async ({ userDetails, id }) => {
  let url = IP + getAPIMap("dtmf");

  const config = {
    headers: {
      Authorization: `Bearer ${userDetails.token}`,
    },
  };
  url = url.replace("{id}", id);
  const response = await axios.get(url, config);
  return response;
};
const pauseCampaign = async (options) => {
  let url = IP + getAPIMap("pauseCampaign");
  url = url.replace("{id}", options.id);
  url = url.replace("{channel}", "voice");
  url = url.replace("{recurrenceId}", "0");

  const config = {
    headers: {
      Authorization: `Bearer ${options.userDetails.token}`,
    },
  };
  const response = await axios.post(url, {}, config);
  return response;
};

const resumeCampaign = async (options) => {
  let url = IP + getAPIMap("resumeCampaign");
  url = url.replace("{id}", options.id);
  url = url.replace("{channel}", "voice");
  url = url.replace("{recurrenceId}", "0");

  const config = {
    headers: {
      Authorization: `Bearer ${options.userDetails.token}`,
    },
  };
  const response = await axios.post(url, {}, config);
  return response;
};

export {
  getCampaignList,
  callerIdList,
  getPromptList,
  Job1CampaignSubmit,
  Job1CampaignUpload,
  getCampainById,
  createIVR,
  getAllNumbersByuser,
  Job3CampaignSubmit,
  Job3CampaignUpload,
  voiceCampaignPieDetails,
  cancelCampaign,
  audioForTextToSpeech,
  dtmfApi,
  pauseCampaign,
  resumeCampaign,
};
