export enum ChatbotStatus {
  Live = 'Live',
  Draft = 'Draft',
}

export enum PlatformType {
  Web = 'Web',
  WhatsApp = 'WhatsApp',
}

export enum DateFilter {
  All = 'All',
  Today = 'Today',
  Yesterday = 'Yesterday',
  Last7Days = 'Last 7 days',
  ThisMonth = 'This Month',
  Last30Days = 'Last 30 days',
}

export enum StatusFilter {
  Draft = 'Draft',
  Published = 'Published',
  All = 'All',
}
