import React from 'react';
import flag from '@/assets/icons/flag-uk.svg';

export const RoutesName = {
  HOME: '/app',
  NEURA_TALK_BUILDER: '/app/neuratalk-builder',
};

export const LoadingText = {
  LOADING_1: 'Hang tight! We are setting up your workspace...',
  LOADING_2: 'We are almost there...',
};

export enum Language {
  English = 'English',
  Spanish = 'Spanish',
  French = 'French',
}

export const languageOptions = [
  {
    value: Language.English,
    label: 'English',
    icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  },
  // {
  //   value: Language.Spanish,
  //   label: 'Spanish',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
  // {
  //   value: Language.French,
  //   label: 'French',
  //   icon: <img src={flag} alt="UK-flag" className="w-6 h-6" />,
  // },
];

export const dateOptions = [
  { label: 'All', value: 'All' },
  { label: 'Today', value: 'Today' },
  { label: 'Yesterday', value: 'Yesterday' },
  { label: 'Last 7 days', value: 'Last 7 days' },
  { label: 'This Month', value: 'This Month' },
  { label: 'Last 30 days', value: 'Last 30 days' },
] as const;