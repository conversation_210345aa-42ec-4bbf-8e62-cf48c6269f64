// Language types
export interface Language {
  id: string;
  name: string;
  code: string;
  createdAt: string;
}

export interface CreateLanguageRequest {
  name: string;
  code: string;
}

// Bot Language types
export interface BotLanguage {
  id: string;
  botId: string;
  langId: string;
  isDefault: boolean;
  createdAt: string;
  createdBy: string;
}

export interface CreateBotLanguageRequest {
  botId: string;
  langId: string;
}
