export interface Entity {
  id: string;
  name: string;
  botId: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

export interface CreateEntityRequest {
  botId: string;
  intentId: string;
  name: string;
  metadata?: Record<string, any>;
}

export interface UpdateEntityRequest {
  botId?: string;
  intentId?: string;
  name?: string;
  metadata?: Record<string, any>;
}
