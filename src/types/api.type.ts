// Filter types (matching common package)
export interface FilterCondition {
  eq?: any;
  ne?: any;
  gt?: number | Date;
  gte?: number | Date;
  lt?: number | Date;
  lte?: number | Date;
  like?: string;
  in?: any[];
  notIn?: any[];
  isNull?: boolean;
  isNotNull?: boolean;
}
export interface FilterObject {
  [column: string]: FilterCondition;
}

// Query params

export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: FilterObject;
}

export interface UuidParams {
  id: string;
}

// Response types
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
