import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { PaginationParams, ApiResponse, PaginatedResponse } from '@/types';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { InfiniteScrollStates } from '@/components/InfiniteScrollStates';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { debounce } from 'lodash';

// --- Pagination Context ---

interface UsePaginationResult<T> {
  items: T[];
  isLoading: boolean;
  isFetching: boolean;
  error: any;
  hasMore: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  searchTerm: string;
  filters: Record<string, any>;
  setSearchTerm: (term: string) => void;
  setFilters: (filters: Record<string, any>) => void;
  goToPage: (page: number) => void;
  fetchNextPage: () => void;
  refetch: () => void;
}

const PaginationContext = createContext<UsePaginationResult<any> | undefined>(undefined);

function usePaginationContext<T>() {
  const context = useContext(PaginationContext);
  if (context === undefined) {
    throw new Error('usePaginationContext must be used within a PaginationProvider');
  }
  return context as UsePaginationResult<T>;
}

// --- usePagination Hook ---

interface UsePaginationOptions<T, QueryArgs extends PaginationParams> {
  queryHook: (args: QueryArgs) => { data?: ApiResponse<PaginatedResponse<T>>; isLoading: boolean; isFetching: boolean; error: any; refetch: () => void; };
  queryArgs?: Omit<QueryArgs, 'page' | 'limit' | 'search' | 'filter' | 'query'>; // queryArgs for the specific API call
  limit?: number;
  paginationType?: 'infinite' | 'number';
}

function usePagination<T, QueryArgs extends PaginationParams>({
  queryHook,
  queryArgs = {} as Omit<QueryArgs, 'page' | 'limit' | 'search' | 'filter' | 'query'>,
  limit = 10,
  paginationType = 'infinite',
}: UsePaginationOptions<T, QueryArgs>) {
  const [page, setPage] = useState(1);
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTermState] = useState('');
  const [filters, setFiltersState] = useState<Record<string, any>>({});

  const { data, isLoading, isFetching, error, refetch } = queryHook({
    ...queryArgs,
    page,
    limit,
    ...(searchTerm && { search: searchTerm }),
    ...(Object.keys(filters).length > 0 && { filter: filters }),
  } as QueryArgs);

  useEffect(() => {
    if (data?.data?.items) {
      setAllData(prevData => {
        const newItems = data.data.items.filter(
          newItem => !prevData.some(prevItem => (prevItem as any).id === (newItem as any).id) // Assuming items have an 'id' property
        );
        return [...prevData, ...newItems];
      });
      setHasMore(data.data.pagination.hasNext);
    }
  }, [data]);

  // Reset pagination when queryArgs, search, or filters change
  useEffect(() => {
    setAllData([]);
    setPage(1);
    setHasMore(true);
  }, [queryArgs, searchTerm, filters]);

  const setSearchTerm = useCallback(
    debounce((term: string) => {
      setSearchTermState(term);
    }, 300),
    []
  );

  const setFilters = useCallback(
    debounce((newFilters: Record<string, any>) => {
      setFiltersState(newFilters);
    }, 300),
    []
  );

  const goToPage = useCallback((newPage: number) => {
    if (newPage > 0 && newPage <= (data?.data?.pagination.totalPages || 1)) {
      setPage(newPage);
      setAllData([]); // Clear data for new page fetch
    }
  }, [data]);

  const fetchNextPage = useCallback(() => {
    if (hasMore && !isLoading && !isFetching) {
      setPage(prevPage => prevPage + 1);
    }
  }, [hasMore, isLoading, isFetching]);

  return {
    items: allData,
    isLoading,
    isFetching,
    error,
    hasMore,
    currentPage: data?.data?.pagination.page || 1,
    totalPages: data?.data?.pagination.totalPages || 1,
    totalItems: data?.data?.pagination.total || 0,
    searchTerm,
    filters,
    setSearchTerm,
    setFilters,
    goToPage,
    fetchNextPage,
    refetch,
  };
}

// --- PaginatedList Component ---

interface PaginatedListProps<T> {
  renderItem: (item: T, index: number) => React.ReactNode;
  listClassName?: string;
  paginationType?: 'infinite' | 'number';
  searchPlaceholder?: string;
  renderSearchInput?: (searchTerm: string, setSearchTerm: (term: string) => void) => React.ReactNode;
  renderFilterControls?: (filters: Record<string, any>, setFilters: (filters: Record<string, any>) => void) => React.ReactNode;
  emptyStateComponent?: React.ReactNode;
  loadingMessage?: string;
  loadingMoreMessage?: string;
  errorMessage?: string;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  endOfListMessage?: string;
}

function PaginatedList<T>({
  renderItem,
  listClassName,
  paginationType = 'infinite',
  searchPlaceholder,
  renderSearchInput,
  renderFilterControls,
  emptyStateComponent,
  loadingMessage,
  loadingMoreMessage,
  errorMessage,
  emptyStateTitle,
  emptyStateDescription,
  endOfListMessage,
}: PaginatedListProps<T>) {
  const { t } = useTranslation();
  const { items, isLoading, isFetching, error, hasMore, fetchNextPage, refetch, searchTerm, setSearchTerm, filters, setFilters, currentPage, totalPages, goToPage } = usePaginationContext<T>();

  const infiniteScrollRef = useInfiniteScroll({
    fetchMore: fetchNextPage,
    hasMore,
    loading: isLoading || isFetching,
  });

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const defaultSearchInput = (
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
      <Input
        type="search"
        placeholder={searchPlaceholder || t('common.searchEllipsis')}
        value={searchTerm}
        onChange={handleSearchInputChange}
        className="pl-10 pr-3 py-2 h-10 border border-slate-300 rounded-md w-full bg-white shadow-sm text-sm placeholder-tertiary-400"
      />
    </div>
  );

  // Initial loading state
  if (isLoading && items.length === 0) {
    return (
      <InfiniteScrollStates
        isLoading={true}
        isFetchingMore={false}
        error={null}
        hasMore={true}
        itemCount={0}
        loadingMessage={loadingMessage}
      />
    );
  }

  // Initial error state
  if (error && items.length === 0) {
    return (
      <InfiniteScrollStates
        isLoading={false}
        isFetchingMore={false}
        error={error}
        hasMore={false}
        itemCount={0}
        onRetry={refetch}
        errorMessage={errorMessage}
      />
    );
  }

  // Empty state
  if (!isLoading && items.length === 0) {
    return emptyStateComponent || (
      <InfiniteScrollStates
        isLoading={false}
        isFetchingMore={false}
        error={null}
        hasMore={false}
        itemCount={0}
        emptyStateTitle={emptyStateTitle}
        emptyStateDescription={emptyStateDescription}
      />
    );
  }

  return (
    <div className={listClassName}>
      <div className="flex items-center space-x-4 mb-4">
        {renderSearchInput ? renderSearchInput(searchTerm, setSearchTerm) : defaultSearchInput}
        {renderFilterControls && renderFilterControls(filters, setFilters)}
      </div>

      {items.map((item, index) => renderItem(item, index))}

      {paginationType === 'infinite' && (
        <InfiniteScrollStates
          isLoading={false}
          isFetchingMore={isFetching}
          error={error && items.length > 0 ? error : null}
          hasMore={hasMore}
          itemCount={items.length}
          onRetry={refetch}
          loadingMoreMessage={loadingMoreMessage}
          errorMessage={errorMessage}
          endOfListMessage={endOfListMessage}
        />
      )}

      {paginationType === 'infinite' && hasMore && !isLoading && !isFetching && (
        <div ref={infiniteScrollRef} style={{ height: '20px' }} />
      )}

      {paginationType === 'number' && totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 mt-4">
          <button onClick={() => goToPage(currentPage - 1)} disabled={currentPage === 1}>
            {t('pagination.previous')}
          </button>
          <span>
            {currentPage} / {totalPages}
          </span>
          <button onClick={() => goToPage(currentPage + 1)} disabled={currentPage === totalPages}>
            {t('pagination.next')}
          </button>
        </div>
      )}
    </div>
  );
}

// --- Exported Components and Hooks ---

export { usePagination, PaginatedList, usePaginationContext };
