import React from 'react';
import { useTranslation } from 'react-i18next';

import LanguageGlob from '../assets/icons/language-glob.svg';
import CannedResponses from '../assets/icons/canned-responses.svg';
import LLMConfiguration from '../assets/icons/llm-configuration.svg';
import NLUBrain from '../assets/icons/nlu-brain.svg';
import Personalization from '../assets/icons/personalization.svg';

const settingsConfig = [
  {
    titleKey: 'settings.language',
    descriptionKey: 'settings.loremDescription',
    icon: LanguageGlob,
  },
  {
    titleKey: 'settings.nlu',
    descriptionKey: 'settings.loremDescription',
    icon: NLUBrain,
  },
  {
    titleKey: 'settings.personalization',
    descriptionKey: 'settings.loremDescription',
    icon: Personalization,
  },
  {
    titleKey: 'settings.llmConfiguration',
    descriptionKey: 'settings.loremDescription',
    icon: LLMConfiguration,
  },
  {
    titleKey: 'settings.cannedResponses',
    descriptionKey: 'settings.loremDescription',
    icon: CannedResponses,
  },
];

export default function SettingsGrid() {
  const { t } = useTranslation();

  return (
    <div className="p-6 bg-muted min-h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl">
        {settingsConfig.map((setting, index) => {
          const icon = setting.icon;
          return (
            <div
              key={index}
              className="bg-card rounded-lg border border-secondary-300 p-6 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-start justify-between space-x-4">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium mb-2 text-base">{t(setting.titleKey)}</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {t(setting.descriptionKey)}
                  </p>
                </div>
                <div className="flex-[0.6] flex-shrink-0 justify-items-end">
                  <img src={icon} />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
