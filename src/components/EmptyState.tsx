import React from 'react';
import { cn } from '@/lib/utils';
import emptyState from '@/assets/icons/empty-state.svg';
import { useTranslation } from 'react-i18next';

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  children,
  className,
}) => {
  const { t } = useTranslation();
  const defaultTitle = title || t('emptyState.title');
  const defaultDescription = description || t('emptyState.description');
  return (
    <div className={cn('flex-1 flex items-center justify-center bg-background', className)}>
      <div className="text-center max-w-md">
        <div className="w-40 h-4w-40 mx-auto mb-6 rounded-full flex items-center justify-center">
          {icon ?? <img src={emptyState} className="w-full" />}
        </div>

        <h3 className="text-lg font-semibold mb-1">{defaultTitle}</h3>
        <p className="text-secondary-700 ">{defaultDescription}</p>

        {children}
      </div>
    </div>
  );
};

export default EmptyState;
