'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

type DateType = 'date' | 'future_date' | 'past_date' | 'custom_date';

interface DatePickerProps {
  selected?: Date;
  onSelect: (date: Date | undefined) => void;
  fieldType?: DateType;
  customRange?: {
    start: Date;
    end: Date;
  };
  className?: string;
}
const getDisabledDate = (fieldType: string, customRange) => {
  const today = new Date();
  console.log('fieldType', customRange);

  switch (fieldType) {
    case 'future_date':
      return (date: Date) => date < new Date();
    case 'past_date':
      return (date: Date) => date > new Date();
    case 'custom_date':
      if (customRange) {
        return (date: Date) => date < customRange.start || date > customRange.end;
      }
      return () => false;
    case 'date':
    default:
      return () => false;
  }
};

export function DatePicker({
  selected,
  onSelect,
  fieldType = 'date',
  customRange,
  className,
}: DatePickerProps) {
  return (
    <div className="[[data-radix-popper-content-wrapper]:has(&)]:z-50">
      {' '}
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              '!z-50 w-full justify-start text-left font-normal',
              !selected && 'text-muted-foreground',
              className
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selected ? format(selected, 'PPP') : 'Pick a date'}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="!z-[9999] w-auto p-0" side="bottom" align="start" forceMount>
          <Calendar
            mode="single"
            selected={selected}
            onSelect={onSelect}
            disabled={getDisabledDate(fieldType, customRange)}
            // disabled={date => date < new Date()}
            className="z-50"
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
