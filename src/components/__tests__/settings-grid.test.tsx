import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import SettingsGrid from '../settings-grid';

describe('SettingsGrid', () => {
  it('renders icons for each card', () => {
    render(<SettingsGrid />);
    const icons = screen.getAllByRole('img');
    expect(icons).toHaveLength(5); // There are 5 icons in the mock data
  });
});
