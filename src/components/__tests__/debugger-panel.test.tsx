import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import DebuggerPanel from "../debugger-panel";

// Mock Redux hooks globally
const mockDispatch = vi.fn();
const mockUseAppSelector = vi.fn();

vi.mock("../../hooks/useRedux", () => ({
  useAppDispatch: () => mockDispatch,
  useAppSelector: () => mockUseAppSelector(),
}));

describe("DebuggerPanel", () => {
  beforeEach(() => {
    // Mock the Redux selector to control `showDebugger` state
    mockUseAppSelector.mockReturnValue({ showDebugger: true });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the panel when showDebugger is true", () => {
    render(<DebuggerPanel />);
    expect(screen.getByText("Debugger")).toBeInTheDocument();
    expect(screen.getByText("Console")).toBeInTheDocument();
    expect(screen.getByText("Debug")).toBeInTheDocument();
  });

  it("does not render the panel when showDebugger is false", () => {
    mockUseAppSelector.mockReturnValue({ showDebugger: false });
    render(<DebuggerPanel />);
    expect(screen.queryByText("Debugger")).not.toBeInTheDocument();
  });

});