import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import PreviewModal from '../preview-modal';

// Mocks
const mockDispatch = vi.fn();
const mockUseAppSelector = vi.fn();
const mockUseTranslation = () => ({ t: (key: string) => key });

vi.mock('react-i18next', () => ({
  useTranslation: () => mockUseTranslation(),
}));

vi.mock('react-router-dom', () => ({
  useSearchParams: () => [{ get: () => 'bot123' }],
}));

vi.mock('../../hooks/useRedux', () => ({
  useAppDispatch: () => mockDispatch,
  useAppSelector: () => mockUseAppSelector(),
}));

beforeEach(() => {
  mockUseAppSelector.mockReturnValue({ showPreview: true });
});

afterEach(() => {
  vi.resetAllMocks();
});

describe('PreviewModal', () => {
  it('renders the modal when showPreview is true', () => {
    render(<PreviewModal />);
    expect(screen.getByText('common.preview')).toBeInTheDocument();
  });

  it('does not render the modal when showPreview is false', () => {
    mockUseAppSelector.mockReturnValue({ showPreview: false });
    render(<PreviewModal />);
    expect(screen.queryByText('common.preview')).not.toBeInTheDocument();
  });

  it('displays the input and send button', () => {
    render(<PreviewModal />);
    expect(screen.getByPlaceholderText('common.typeMessage')).toBeInTheDocument();
    expect(screen.getByLabelText('Send')).toBeInTheDocument();
  });

  it('disables send button when input is empty', () => {
    render(<PreviewModal />);
    const sendButton = screen.getByLabelText('Send');
    expect(sendButton).toBeDisabled();
  });

  it('calls dispatch when Debugger button is clicked', () => {
    render(<PreviewModal />);
    const bugButton = screen
      .getAllByRole('button')
      .find(btn => btn.innerHTML.includes('lucide-bug-off'));
    fireEvent.click(bugButton!);
    expect(mockDispatch).toHaveBeenCalled();
  });

  it('shows confirmation dialog when closing with messages', async () => {
    render(<PreviewModal />);
    // Add a message to simulate active conversation
    const setMessagesSpy = vi.spyOn(React, 'useState');
    setMessagesSpy.mockReturnValueOnce([
      [{ sender: 'user', nodeType: 'user_message', data: { text: 'Hi' } }],
      vi.fn(),
    ] as any);
    fireEvent.click(screen.getAllByRole('button').pop()!); // Click close
    const confirmText = await screen.findByText(text =>
      text.includes('Want to end this conversation?')
    );
    expect(confirmText).toBeInTheDocument();
  });
});
