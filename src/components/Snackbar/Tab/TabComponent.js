import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useTheme } from "../../../context/ThemeContext";
function TabComponent(props) {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { CustomComponent } = props;

  const tabElement = props.default ? props.default : Object.keys(props.tabs)[0];
  const [tab, setTab] = useState(tabElement);
  let ele = "";
  if (!props.tab) {
    ele = Object.keys(props.tabs).filter((ele) => ele === tab)[0];
    if (!ele) {
      ele = Object.keys(props.tabs)[0];
    }
  }

  return (
    <div>
      <ul
        className={
          props.noBorder === true
            ? "nav nav-tabs  flex flex-col gap-8  mt-4 md:flex-row flex-wrap list-none pl-0"
            : "nav nav-tabs  flex flex-col gap-8  mt-4 border-b  md:flex-row flex-wrap list-none pl-0"
        }
        role="tablist"
        onClick={(event) => {
          if (event.target.id !== "") {
            if (props.setSelectedTabId) {
              props.setSelectedTabId(event.target.id);
            } else {
              setTab(event.target.id);

              props.selectedTabId && props.selectedTabId(event.target.id);
            }
          }
        }}
      >
        {Object.keys(props.tabs).map((ele, i) => {
          return (
            <>
              {props.tab ? (
                <li
                  style={
                    props.tab === ele
                      ? {
                          borderBottom: props.highlightColor
                            ? `1px solid ${props.highlightColor}`
                            : `1px solid ${theme.primaryColor}`,
                        }
                      : {}
                  }
                  key={i}
                  role="presentation"
                >
                  <div
                    style={
                      props.tab === ele
                        ? {
                            color: props.highlightColor
                              ? props.highlightColor
                              : theme.primaryColor,
                          }
                        : {}
                    }
                    className={
                      props.tab === ele
                        ? " nav-link block cursor-pointer font-bold text-sm leading-tight border-x-0 border-t-0 border-b-2 border-transparent  pb-1 focus:border-transparent active"
                        : " nav-link block cursor-pointer text-sm leading-tight text-[#7B7B7B] border-x-0 border-t-0 border-b-2 border-transparent pb-1 focus:border-transparent active"
                    }
                    role="tab"
                    id={ele}
                    aria-selected="true"
                  >
                    {props.noTranslate ? ele : t(ele)}
                  </div>
                </li>
              ) : (
                <li
                  style={
                    tab === ele
                      ? {
                          borderBottom: props.highlightColor
                            ? `1px solid ${props.highlightColor}`
                            : `1px solid ${theme.primaryColor}`,
                        }
                      : {}
                  }
                  key={i}
                  role="presentation"
                >
                  <div
                    style={
                      tab === ele
                        ? {
                            color: props.highlightColor
                              ? props.highlightColor
                              : theme.primaryColor,
                          }
                        : {}
                    }
                    className={
                      tab === ele
                        ? " nav-link block cursor-pointer font-bold text-sm leading-tight border-x-0 border-t-0 border-b-2 border-transparent pb-1 focus:border-transparent active"
                        : " nav-link block cursor-pointer text-sm leading-tight text-[#7B7B7B] border-x-0 border-t-0 border-b-2 border-transparent pb-1 focus:border-transparent active"
                    }
                    role="tab"
                    id={ele}
                    aria-selected="true"
                  >
                    {props.noTranslate ? ele : t(ele)}
                  </div>
                </li>
              )}
            </>
          );
        })}
      </ul>
      {CustomComponent ? CustomComponent : null}
      {props.tab ? (
        <div id="tabs-tabContent">
          {
            <div id={ele} role="tabpanel">
              {props.tabs[props.tab]}
            </div>
          }
        </div>
      ) : (
        <div id="tabs-tabContent">
          {
            <div id={ele} role="tabpanel">
              {props.tabs[ele]}
            </div>
          }
        </div>
      )}
    </div>
  );
}
export default TabComponent;
