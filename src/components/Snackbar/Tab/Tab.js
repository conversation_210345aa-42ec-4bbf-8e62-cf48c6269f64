import { useTheme } from "../../../context/ThemeContext";
function TabComponent(props) {
  const { theme } = useTheme();
  return (
    <div>
      <ul
        className={
          props.noBorder === true
            ? "  flex flex-col gap-8  mt-4 md:flex-row flex-wrap list-none pl-0"
            : "  flex flex-col gap-8  mt-4 border-b  md:flex-row flex-wrap list-none pl-0"
        }
        role="tablist"
        onClick={(event) => {
          if (event.target.id !== "") {
            props.setTab(event.target.id);
          }
        }}
      >
        {Object.keys(props.tabs).map((tab, i) => {
          return (
            <li
              style={
                props.tab === tab
                  ? {
                      borderBottom: props.highlightColor
                        ? `1px solid ${props.highlightColor}`
                        : `1px solid ${theme.primaryColor}`,
                    }
                  : {}
              }
              // className={
              //   props.tab === tab
              //     ? ` border-b-2 border-[${
              //         props.highlightColor ? props.highlightColor : "#ED1C24"
              //       }]`
              //     : ""
              // }
              key={i}
              role="presentation"
            >
              <div
                style={
                  props.tab === tab
                    ? {
                        color: props.highlightColor
                          ? props.highlightColor
                          : theme.primaryColor,
                      }
                    : {}
                }
                className={
                  props.tab === tab
                    ? ` nav-link block  cursor-pointer font-bold text-sm leading-tight  border-x-0 border-t-0 border-b-2 border-transparent  pb-1   focus:border-transparent active`
                    : " nav-link block cursor-pointer text-sm leading-tight text-[#7B7B7B]   border-x-0 border-t-0 border-b-2 border-transparent pb-1   focus:border-transparent active"
                }
                role="tab"
                id={tab}
              >
                {tab}
              </div>
            </li>
          );
        })}
      </ul>
      <div id="tabs-tabContent">
        {
          <div id={props.tab} role="tabpanel">
            {props.tabs[props.tab]}
          </div>
        }
      </div>
    </div>
  );
}
export default TabComponent;
