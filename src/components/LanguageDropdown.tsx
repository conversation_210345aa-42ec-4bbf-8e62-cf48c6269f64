import React, { useEffect, useMemo, useState } from 'react';
import { useGetBotLanguagesQuery, useGetLanguagesQuery } from '@/store/api';
import DropdownButton from './dropdownButton';
import { useSearchParams } from 'react-router-dom';
import { Language } from '@/types';
import ReactCountryFlag from 'react-country-flag';
import { getLanguage, getCountries } from '@ladjs/country-language';

interface IProps {
  onChange: (langId: string) => void;
  initialValue?: string;
}

const getCountryCodeFromLanguageCode = (langCode: string): string => {
  const lowerCaseLangCode = langCode.toLowerCase();

  const languageDetails = getLanguage(lowerCaseLangCode);

  if (languageDetails?.countries?.length > 0) {
    return languageDetails.countries[0].code_2;
  }

  const countries = getCountries();
  for (const country of countries) {
    if (country.languages.some(lang => lang === lowerCaseLangCode)) {
      return country.code_2;
    }
  }

  return 'US';
};

const LanguageDropdown: React.FC<IProps> = ({ onChange, initialValue }) => {
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;
  const [selectedLanguage, setSelectedLanguage] = useState<Language>();

  const { data: botLanguagesData } = useGetBotLanguagesQuery({ filter: { botId: { eq: botId } } });
  const botLanguages = botLanguagesData?.data?.items || [];

  const { data: languagesData } = useGetLanguagesQuery(
    {
      filter: {
        id: {
          in: botLanguages.map(botLanguage => botLanguage.langId),
        },
      },
    },
    {
      skip: !botLanguages.length,
    }
  );

  const botDefaultLanguage = useMemo(() => {
    return botLanguages.find(botLanguage => botLanguage.isDefault);
  }, [botLanguages]);

  const languageList = languagesData?.data?.items || [];

  const languages = useMemo(
    () =>
      languageList.map(language => {
        const countryCode = getCountryCodeFromLanguageCode(language?.code || '');
        return {
          value: language?.id ?? '',
          label: language?.name ?? '',
          icon: (
            <ReactCountryFlag
              countryCode={countryCode}
              svg
              className="w-5 h-5"
              alt={language.name}
              data-testid="country-flag"
            />
          ),
        };
      }),
    [languageList]
  );

  useEffect(() => {
    if (languageList?.length && !selectedLanguage) {
      const languageNode =
        //TODO: rm this code check
        languageList.find(
          language =>
            language.id === initialValue ||
            language.id === botDefaultLanguage?.langId ||
            language.code === 'en'
        ) ?? languageList[0];
      setSelectedLanguage(languageNode);
      onChange?.(languageNode.id);
    }
  }, [languageList, selectedLanguage]);

  const onChangeHandler = (langId: string) => {
    const languageNode = languageList.find(language => language.id === langId);
    setSelectedLanguage(languageNode);
    onChange(langId);
  };

  return (
    <DropdownButton
      options={languages ?? []}
      value={selectedLanguage?.id ?? ''}
      onChange={onChangeHandler}
    />
  );
};

export default LanguageDropdown;
