import * as React from 'react';
import { useState, useEffect } from 'react';
import { Player as LottiePlayer } from '@lottiefiles/react-lottie-player';
import {CONCENTRIC_CIRCLE_LOADER} from '@/lib/lottie/loader'
import loader from '@/assets/pngs/loader.png';
import { LoadingText } from '@/lib/constant';
const EditorLoader = () => {

    const [text, setText] = useState<string>(LoadingText.LOADING_1);
    useEffect(() => {
        setTimeout(() => {
            setText(LoadingText.LOADING_2);
        }, 2000);
    }, []);

    return (
            <div className='relative flex flex-col justify-center items-center w-screen h-screen'>
                <img src={loader} alt="Loader" className='absolute h-[125px] top-[calc(50%+35px)] left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10' />
                <LottiePlayer
                    autoplay
                    loop
                    src={CONCENTRIC_CIRCLE_LOADER}  
                    className='absolute h-[300px] w-[300px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-1'
                    />
                <p className='absolute top-[calc(50%+150px)] left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center text-md'>{text}</p>
            </div>
    );
  };

  export default EditorLoader;