'use client';

import React, { useState, useRef, useEffect } from 'react';
import { X, ExternalLink, BugOff, Smile, Send, CalendarDays } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/hooks/useRedux';
import { toggleDebugger, togglePreview } from '@/store/slices/uiSlice';
import { PlatformType } from '../lib/enums';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from './ui/select';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RichTextEditor } from '@/modules/rich-text-editor';
import { DatePicker } from '@/components/ui/date-picker';
import { format } from 'date-fns';

interface ChatMessage {
  sender: 'user' | 'bot';
  nodeType: 'user_message' | 'message' | 'form';
  data: any;
  conversationId?: string;
}

const API_URL = (botId: string) => `http://localhost:3001/api/v1/conversations/${botId}/message`;
const RESET_CONV = `http://localhost:3001/api/v1/conversations/clear`;
const USER_ID = 'user123';
const CHANNEL = 'web';

export default function PreviewModal() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { showPreview } = useAppSelector(state => state.ui);

  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id');

  const [platform, setPlatform] = useState<PlatformType>(PlatformType.Web);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Record<string, string | Date>>({});
  const [activeForm, setActiveForm] = useState<any | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const chatEndRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, showPreview]);

  if (!showPreview) return null;

  const handleFormInputChange = (fieldName: string, value: string | Date) => {
    console.log(`Form input changed: ${fieldName} =`, value);
    setFormData(prev => ({ ...prev, [fieldName]: value }));
  };

  const isFormValid = () => {
    if (!activeForm) return false;
    for (const field of activeForm.data.prompt) {
      if (field.required && !formData[field.fieldName]) {
        return false;
      }
    }
    return true;
  };

  const sendMessageToServer = async (payload: any) => {
    setError(null);
    setLoading(true);

    try {
      const res = await fetch(API_URL(botId!), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Bot-Id': botId!,
        },
        body: JSON.stringify(payload),
      });

      if (!res.ok) throw new Error(t('errors.failedToSend'));
      const data = await res.json();

      let botMessages: ChatMessage[] = [];

      if (data.success) {
        const responseData = data.data;
        botMessages = responseData.response.map((msg: any) => ({
          sender: 'bot',
          nodeType: msg.nodeType,
          data: msg.data,
        }));

        const formMessage = botMessages.find(
          msg => msg.nodeType === 'form' && msg?.data?.prompt?.length > 0
        );
        console.log('formMessage', formMessage);

        if (formMessage) {
          setActiveForm(formMessage);
          const initialData = formMessage.data.prompt.reduce(
            (acc: Record<string, string | Date>, field: any) => {
              acc[field.fieldName] = field.fieldType.includes('date') ? '' : '';
              return acc;
            },
            {}
          );
          setFormData(initialData);
        }
      } else {
        setError(t('errors.unexpectedResponse'));
      }

      if (botMessages.length > 0) {
        setMessages(prev => [...prev, ...botMessages]);
      }
    } catch (err: any) {
      setError(err.message || t('errors.somethingWrong'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return;

    if (activeForm && isFormValid()) {
      const formattedFormData = Object.fromEntries(
        Object.entries(formData).map(([key, value]) => [
          key,
          value instanceof Date ? format(value, 'yyyy-MM-dd') : value,
        ])
      );

      const userSubmissionText = Object.entries(formattedFormData)
        .filter(([, value]) => value)
        .map(([key, value]) => `<b>${key.replace(/_/g, ' ')}:</b> ${value}`)
        .join('<br/>');

      setMessages(prev => [
        ...prev,
        { data: { text: userSubmissionText }, nodeType: 'user_message', sender: 'user' },
      ]);

      await sendMessageToServer({
        content: JSON.stringify(formattedFormData),
        messageType: 'text',
        formData: formattedFormData,
        botId,
        metadata: {
          userId: USER_ID,
          channel: CHANNEL,
          formId: activeForm.data.formId,
        },
      });

      setActiveForm(null);
      setFormData({});
    } else if (!activeForm && input.trim()) {
      const singleFormMessage =
        messages[messages.length - 1]?.nodeType === 'form' &&
        messages[messages.length - 1]?.data?.prompt?.length === 1;

      const text = input.trim();
      setMessages(prev => [...prev, { data: { text }, nodeType: 'user_message', sender: 'user' }]);

      if (singleFormMessage) {
        const fieldName = messages[messages.length - 1].data.prompt[0].fieldName;
        const isDateField = messages[messages.length - 1].data.prompt[0].fieldType.includes('date');
        let formattedText = text;
        if (isDateField) {
          try {
            formattedText = format(new Date(text), 'yyyy-MM-dd');
          } catch (err) {
            console.error('Invalid date format for single form input:', text);
            setError('Invalid date format');
            return;
          }
        }
        await sendMessageToServer({
          content: text,
          messageType: 'text',
          formData: { [fieldName]: formattedText },
          botId,
          metadata: { userId: USER_ID, channel: CHANNEL },
        });
      } else {
        await sendMessageToServer({
          content: text,
          messageType: 'text',
          botId,
          metadata: { userId: USER_ID, channel: CHANNEL },
        });
      }

      setInput('');
    }
  };

  const handleDebugger = () => {
    dispatch(toggleDebugger());
  };

  const resetConversation = async () => {
    try {
      await fetch(RESET_CONV, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (err) {
      console.error('Reset failed:', err);
    }
  };

  const clearState = () => {
    setMessages([]);
    setInput('');
    setFormData({});
    setActiveForm(null);
    setError(null);
    dispatch(togglePreview());
  };

  const handleClose = () => {
    if (messages.length > 0) {
      setShowConfirmDialog(true);
    } else {
      clearState();
    }
  };

  const confirmClose = async (confirm: boolean) => {
    setShowConfirmDialog(false);
    if (confirm) {
      await resetConversation();
      clearState();
    }
  };
  // console.log('msg?.data?.prompt', messages);

  return (
    <div className="fixed right-0 top-0 bottom-0 border bg-background flex items-center justify-center z-50">
      <div className="w-96 h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-secondary-200">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-sm text-tertiary-600">{t('common.preview')}</h3>
            <div className="flex items-center space-x-2">
              <div className="relative min-w-32">
                <Select value={platform} onValueChange={setPlatform}>
                  <SelectTrigger className="px-4 py-1.5 border-none rounded-full text-sm bg-secondary-50 min-w-24 shadow-none focus:ring-0 focus:outline-none flex items-center justify-center gap-1 h-auto">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(PlatformType).map(value => (
                      <SelectItem key={value} value={value}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <button className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center">
                <ExternalLink className="w-4 h-4" />
              </button>
              <button
                className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
                onClick={handleDebugger}
              >
                <BugOff className="w-4 h-4" />
              </button>
              <button
                onClick={handleClose}
                className="w-6 h-6 text-secondary-400 hover:text-secondary-600 flex items-center justify-center"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Chat Interface */}
        <div className="flex-1 p-4 overflow-auto">
          <div className="bg-background rounded-lg h-full flex flex-col">
            {/* Chat Content */}
            <div className="flex-1 border-none p-4 space-y-3 overflow-y-auto">
              {messages.map((msg, idx) => {
                // console.log('msg?.data?.prompt[0]?.label', msg);

                if (msg.nodeType === 'form' && msg?.data?.prompt?.length > 1) {
                  return (
                    <div key={idx} className="flex justify-start">
                      <div className="bg-transparent text-secondary-900 w-full max-w-[90%]">
                        <p className="font-medium mb-3 text-gray-700">
                          {msg?.data?.prompt[0]?.label ?? 'Provide the required details'}
                        </p>
                        <div className="space-y-3">
                          {msg.data.prompt?.map((field: any) => (
                            <div key={field.fieldName} className="relative">
                              {field.fieldType.includes('date') ? (
                                <DatePicker
                                  selected={
                                    formData[field.fieldName] instanceof Date
                                      ? (formData[field.fieldName] as Date)
                                      : undefined
                                  }
                                  onSelect={date =>
                                    handleFormInputChange(field.fieldName, date ?? '')
                                  }
                                  fieldType={field.fieldType}
                                  customRange={
                                    field.fieldType === 'custom date'
                                      ? { start: new Date(1900, 0, 1), end: new Date(2100, 11, 31) }
                                      : undefined
                                  }
                                  className="w-full"
                                />
                              ) : (
                                <input
                                  type="text"
                                  name={field.fieldName}
                                  placeholder={field.fieldName}
                                  value={(formData[field.fieldName] as string) || ''}
                                  onChange={e =>
                                    handleFormInputChange(field.fieldName, e.target.value)
                                  }
                                  className="w-full bg-white border border-gray-200 rounded-lg px-3 py-2.5 text-base placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-300"
                                />
                              )}
                              {field.fieldType.includes('date') && (
                                <CalendarDays className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                }

                let content = '';
                if (msg.nodeType === 'user_message') {
                  content = msg.data.text;
                } else if (msg.nodeType === 'message') {
                  content = msg.data.text;
                } else if (msg.nodeType === 'form' && msg?.data?.prompt?.length === 1) {
                  content = msg.data.prompt?.[0].label;
                }

                if (content) {
                  return (
                    <div
                      key={idx}
                      className={cn(
                        'flex',
                        msg.sender === 'user' ? 'justify-end' : 'justify-start'
                      )}
                    >
                      <div
                        className={cn(
                          'max-w-[80%] px-4 py-2 rounded-2xl shadow-sm text-base',
                          msg.sender === 'user'
                            ? 'bg-primary-100 text-primary-900 rounded-br-md'
                            : 'bg-tertiary-100 text-secondary-900 rounded-bl-md'
                        )}
                      >
                        <RichTextEditor
                          content={content}
                          readOnly
                          isToolbar={false}
                          className="bg-transparent border-none shadow-none p-0"
                        />
                      </div>
                    </div>
                  );
                }
                return null;
              })}
              <div ref={chatEndRef} />
              {loading && (
                <div className="flex justify-start">
                  <div className="px-4 py-2 rounded-2xl bg-tertiary-100 text-secondary-400 text-base animate-pulse">
                    {t('form.typing')}
                  </div>
                </div>
              )}
              {error && <div className="text-error-500 text-xs mt-2">{error}</div>}
            </div>

            {/* Chat Input */}
            <form className="p-4 border-t" onSubmit={handleSubmit} autoComplete="off">
              <div className="flex flex-row items-center rounded-lg border border-secondary-300 py-1">
                <input
                  type="text"
                  placeholder={
                    activeForm ? 'Fill the form above to continue' : t('common.typeMessage')
                  }
                  className="border-none pl-2 w-60 text-sm bg-transparent focus:outline-none"
                  value={input}
                  onChange={e => setInput(e.target.value)}
                  disabled={loading || activeForm !== null}
                  aria-label="Type your message"
                />
                <button
                  type="button"
                  className="w-9 h-9 rounded-lg bg-primary-50 text-primary-600 flex items-center justify-center hover:bg-primary-100 transition-colors"
                  tabIndex={-1}
                  disabled
                >
                  <Smile className="w-6 h-6" />
                </button>
                <button
                  type="submit"
                  className="box-border w-8 h-8 bg-primary-600 text-white rounded-lg flex items-center justify-center hover:bg-primary-700 transition-colors disabled:opacity-50"
                  disabled={loading || (activeForm ? !isFormValid() : !input.trim())}
                  aria-label="Send"
                >
                  <Send className="w-6 h-6 p-0.5" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      {!!messages.length && (
        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent className="sm:max-w-80">
            <DialogHeader>
              <DialogTitle>Want to end this conversation?</DialogTitle>
              <DialogDescription>This will erase the chat and close the window.</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variantColor="success"
                className=" border-none  rounded-full px-6 py-2"
                onClick={() => confirmClose(true)}
              >
                Yes
              </Button>
              <Button
                variantColor="error"
                className="border-none rounded-full px-6 py-2"
                onClick={() => confirmClose(false)}
              >
                No
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
