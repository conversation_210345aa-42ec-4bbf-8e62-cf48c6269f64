import './styles/globals.css';
import './lib/i18n/config';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter, Route, Routes, useNavigate } from 'react-router-dom';
import Home from './pages/Home/Home';
import NeuraTalkBuilder from './pages/NeuratalkBuilder';
import { Provider } from 'react-redux';
import { store } from '../src/store/store';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from '@/components/ui/toaster';
import { RoutesName } from './lib/constant';

const queryClient = new QueryClient();
const container = document.getElementById('root');

if (!container) {
  throw new Error('Root container missing in index.html');
}

function withNavigate<T>(
  Component: React.ComponentType<T & { navigate: ReturnType<typeof useNavigate> }>
) {
  return function Wrapper(props: T) {
    const navigate = useNavigate();
    return <Component {...props} navigate={navigate} />;
  };
}

const HomeWithNavigation = withNavigate(Home);
const NeuraTalkBuilderWithNavigation = withNavigate(NeuraTalkBuilder);

const root = createRoot(container);
root.render(
  <React.StrictMode>
    {/* <QueryClientProvider client={queryClient}> */}
    <Provider store={store}>
      <BrowserRouter>
        <Routes>
          <Route path={RoutesName.HOME} element={<HomeWithNavigation />} />
          <Route
            path={RoutesName.NEURA_TALK_BUILDER}
            element={<NeuraTalkBuilderWithNavigation />}
          />
        </Routes>
      </BrowserRouter>
      <Toaster />
    </Provider>
    {/* </QueryClientProvider> */}
  </React.StrictMode>
);
