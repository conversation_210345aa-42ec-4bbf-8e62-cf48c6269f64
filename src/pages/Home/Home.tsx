import * as React from 'react';
import { Search, LayoutGrid, List, Funnel } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useDispatch, Provider } from 'react-redux';
import { NavigateFunction } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import ChatbotCard from './ChatbotCard';
import DropdownButton from '@/components/dropdownButton';
import EditorLoader from '@/components/editor-loader';
import MicroFrontendWrapper from '@/components/micro-frontend-wrapper';
import { AppDispatch, store } from '@/store/store';
import { deleteChatbot } from '@/store/slices/chatbotsSlice';
import {
  useGetBotsQuery,
  useCreateBotMutation,
  useDeleteBotMutation,
  useCloneBotMutation,
} from '@/store/api/chatBotApi';
import { dateOptions, RoutesName } from '@/lib/constant';
import { generateRandomSuffix } from '@/utils/getRandomValue';
import group from '../../assets/icons/Group_10874.svg';
import { getDateRange, getDaysAgo } from '@/utils/getDaysRange';
import { FilterOptions, HomeProps } from '@/types';

// Define types

// Sub-components
const HeaderSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <section className="relative flex h-[200px] shadow-none overflow-hidden">
      <img
        src={group}
        alt="NeuraTalk AI Background"
        //TODO: rm this
        className="absolute -top-[16.4rem] left-0 w-full object-fill h-auto z-0"
      />
      <div className="absolute bottom-0 left-0 w-full h-6 bg-gradient-to-b from-transparent to-white z-10" />
      <div className="absolute inset-0 w-1/2 flex flex-col justify-center px-6 top-6 left-20"> {/* TODO: Adjusted left value  */}
        <p className="text-tertiary-600 text-base max-w-3xl">
          <span className="font-medium text-tertiary-900 text-base">{t('home.title')} </span>
          {t('home.description')}
        </p>
      </div>
    </section>
  );
};

const FilterSection: React.FC<{
  filterOptions: FilterOptions;
  setFilterOptions: React.Dispatch<React.SetStateAction<FilterOptions>>;
}> = ({ filterOptions, setFilterOptions }) => {
  const { t } = useTranslation();

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilterOptions(prev => ({ ...prev, searchTerm: event.target.value }));
  };

  return (
    <div className="flex flex-col sm:flex-row items-center gap-3 w-full md:w-auto flex-wrap">
      <div className="relative sm:w-auto md:w-52">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
        <input
          type="search"
          placeholder={t('common.search')}
          className="pl-10 pr-3 py-2 h-10 border border-slate-300 rounded-md w-full bg-white shadow-sm text-sm placeholder-tertiary-400"
          value={filterOptions.searchTerm}
          onChange={handleSearchChange}
        />
      </div>
      <DropdownButton
        value={filterOptions.dateFilter}
        onChange={(value: FilterOptions['dateFilter']) =>
          setFilterOptions(prev => ({ ...prev, dateFilter: value }))
        }
        options={[...dateOptions]}
        placeholder="Date"
        className="min-w-36 text-tertiary-500 border-slate-300 border rounded-md flex justify-between items-center"
      />
      <Button
        variant="outline"
        className="bg-white shadow-sm border-tertiary-300 text-slate-500 hover:bg-tertiary-50 w-full sm:w-auto h-10"
        disabled={true}
      >
        <Funnel className="h-4 w-4 mr-2 text-tertiary--500" />
        {t('common.filter')}
      </Button>
    </div>
  );
};

const ActionButtons: React.FC<{ onCreateChatbot: () => void }> = ({ onCreateChatbot }) => {
  const { t } = useTranslation();
  return (
    <div className="flex items-center gap-2 w-full sm:w-auto justify-start md:justify-end">
      <Button
        variant="outline"
        size="icon"
        className="bg-error-50 text-error-600 border-error-200 hover:bg-error-100 shadow-sm h-10 w-10"
      >
        <LayoutGrid className="h-5 w-5" />
      </Button>
      <Button
        size="icon"
        className="bg-transparent text-tertiary-500 hover:border-tertiary-300 hover:bg-tertiary-200 hover:shadow-sm h-10 w-10"
        disabled={true}
      >
        <List className="h-5 w-5" />
      </Button>
      <Button
        className="bg-error-500 hover:bg-error-600 text-white font-semibold shadow-sm px-6 py-2 h-10 text-sm rounded-md"
        onClick={onCreateChatbot}
      >
        {t('common.create')}
      </Button>
    </div>
  );
};

const ChatbotList: React.FC<{
  chatbots: any[];
  onDelete: (id: string) => void;
  onClone: (id: string) => void;
  navigate: NavigateFunction;
}> = ({ chatbots, onDelete, onClone, navigate }) => {
  const { t } = useTranslation();
  return (
    <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {chatbots.length > 0 ? (
        chatbots.map(bot => (
          <ChatbotCard
            key={bot.id}
            id={bot.id}
            title={bot.name}
            description={bot.description}
            status={bot.status}
            lastUpdated={getDaysAgo(bot.updatedAt)}
            onDelete={onDelete}
            onClone={onClone}
            navigate={navigate}
          />
        ))
      ) : (
        <p className="col-span-full text-center text-tertiary-500 py-10">{t('home.noResults')}</p>
      )}
    </section>
  );
};

// Main Component
const Home: React.FC<HomeProps> = ({ navigate }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const [filterOptions, setFilterOptions] = React.useState<FilterOptions>({
    dateFilter: 'All',
    statusFilter: 'All',
    searchTerm: '',
  });
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(filterOptions.searchTerm);

  const { lowerBound, upperBound } = getDateRange(filterOptions.dateFilter);
  const filter = {
    status:
      filterOptions.statusFilter !== 'All'
        ? { eq: filterOptions.statusFilter }
        : { ne: 'inactive' },
    updatedAt:
      lowerBound || upperBound
        ? {
            ...(lowerBound && { gte: new Date(lowerBound) }),
            ...(upperBound && { lte: new Date(upperBound) }),
          }
        : undefined,
  };

  const { data: botsResponse, isLoading: isLoadingApps } = useGetBotsQuery({
    search: debouncedSearchTerm,
    sortField: 'updatedAt',
    sortOrder: 'desc',
    filter,
    page: 1,
    limit: 20,
  });

  const [createBot, { isLoading: isLoadingCreateBot }] = useCreateBotMutation();
  const [deleteBot] = useDeleteBotMutation();
  const [cloneBot] = useCloneBotMutation();

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(filterOptions.searchTerm);
    }, 500);
    return () => clearTimeout(handler);
  }, [filterOptions.searchTerm]);

  const handleCreateChatbot = async () => {
    const title = `New-Chatbot-${generateRandomSuffix()}`;
    try {
      const res = await createBot({
        title,
        domain: 'DEFAULT',
        description: 'No Description',
      }).unwrap();
      navigate(`${RoutesName.NEURA_TALK_BUILDER}?id=${res.id}`);
    } catch (error) {
      console.error('Error creating bot:', error);
    }
  };

  const handleDeleteChatbot = async (id: string) => {
    try {
      await deleteBot({ botId: id }).unwrap();
      dispatch(deleteChatbot(id));
    } catch (error) {
      console.error('Error deleting bot:', error);
    }
  };

  const handleCloneChatbot = async (id: string) => {
    try {
      const { id: newId } = await cloneBot({ botId: id }).unwrap();
      navigate(`${RoutesName.NEURA_TALK_BUILDER}?id=${newId}`);
    } catch (error) {
      console.error('Clone failed:', error);
      alert('Failed to clone chatbot.');
    }
  };

  if (isLoadingApps || isLoadingCreateBot) return <EditorLoader />;

  return (
    <div className="min-h-screen">
      <HeaderSection />
      <div className="flex flex-col px-6 mb-40 mx-auto max-w-7xl">
        <section className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
          <FilterSection filterOptions={filterOptions} setFilterOptions={setFilterOptions} />
          <ActionButtons onCreateChatbot={handleCreateChatbot} />
        </section>
        <ChatbotList
          chatbots={botsResponse?.bots || []}
          onDelete={handleDeleteChatbot}
          onClone={handleCloneChatbot}
          navigate={navigate}
        />
      </div>
    </div>
  );
};

export default function HomeWrapper({ navigate }: HomeProps) {
  return (
    <Provider store={store}>
      <MicroFrontendWrapper>
        <Home navigate={navigate} />
      </MicroFrontendWrapper>
    </Provider>
  );
}
