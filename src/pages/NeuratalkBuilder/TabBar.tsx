import React from 'react';
import { MonitorUp, TvMinimalPlay, Wrench } from 'lucide-react';
import { neuraTalkBuilderTabs } from './config';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/hooks/useRedux';
import { toggleFormNode, togglePreview } from '@/store/slices/uiSlice';
import { useTranslation } from 'react-i18next';
import { useBuildBotMutation } from '@/store/api/chatBotApi';
import { useSearchParams } from 'react-router-dom';
const TabBar = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [buildBot] = useBuildBotMutation();
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id');

  const deleteBotCache = async () => {
    // Delete fetch request
    const done = await fetch(`http://localhost:5005/api/v1/models/${botId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if (!done.ok) throw new Error('Failed to delete bot');
    return 'ok';
  };

  return (
    <div className="px-6 border-b bg-white">
      <div className="flex items-start justify-between">
        <TabsList className="flex flex-1 p-0 h-full items-stretch">
          {neuraTalkBuilderTabs.map(tab => {
            const Icon = tab.icon;
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  'p-3 flex-1 border border-b-0 rounded-lg rounded-br-none rounded-bl-none bg-primary-50 text-sm transition-colors flex items-center justify-center',
                  'data-[state=active]:text-primary-500 data-[state=active]:bg-primary-100 data-[state=active]:shadow-none',
                  'data-[state=inactive]:text-tertiary-600 data-[state=inactive]:hover:text-secondary-700 data-[state=inactive]:hover:bg-secondary-100',
                  'overflow-hidden'
                )}
              >
                {Icon}
                <span>{t(tab.labelKey)}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <div className="flex items-center space-x-3 flex-[0.5] justify-end">
          <Button variant={'ghost'} className="uppercase" onClick={() => dispatch(togglePreview())}>
            <TvMinimalPlay className="w-4 h-4" />
            <span>{t('common.preview')}</span>
          </Button>
          <Button
            variant={'outline'}
            className="uppercase"
            onClick={async () => {
              // await deleteBotCache()
              buildBot(botId);
            }}
          >
            <Wrench className="w-4 h-4" />
            <span>{t('Build')}</span>
          </Button>
          <Button disabled onClick={() => dispatch(toggleFormNode())}>
            <MonitorUp className="w-4 h-4" />
            <span>{t('common.publish')}</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TabBar;
