import React, { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import { store } from '../../store/store';

import PreviewModal from '@/components/preview-modal';
import Header from './header';
import TabsComponent from './Tabs';
import MicroFrontendWrapper from '@/components/micro-frontend-wrapper';
import DebuggerPanel from '@/components/debugger-panel';
import EditorLoader from '@/components/editor-loader';

function NeuraTalkBuilderContent({ navigate }) {
  const [isLoaderVisible, setIsLoaderVisible] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setIsLoaderVisible(false);
    }, 1000);
  }, []);

  if (isLoaderVisible) {
    return <EditorLoader />;
  }
  return (
    <div className="h-screen">
      <div className="flex bg-background h-full">
        <div className="flex-1 flex flex-col h-full overflow-hidden w-0">
          <Header navigate={navigate} />
          <TabsComponent />
        </div>
        <PreviewModal />
        <DebuggerPanel />
      </div>
    </div>
  );
}

export default function NeuraTalkBuilder({ navigate }) {
  return (
    <Provider store={store}>
      <MicroFrontendWrapper>
        <NeuraTalkBuilderContent navigate={navigate} />
      </MicroFrontendWrapper>
    </Provider>
  );
}
