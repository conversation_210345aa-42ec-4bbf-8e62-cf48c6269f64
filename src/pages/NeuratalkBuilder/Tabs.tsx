import React, { useState } from 'react';
import { Tabs, TabsContent } from '../../components/ui/tabs';
import { neuraTalkBuilderTabs } from './config';
import { cn } from '@/lib/utils';
import TabBar from './TabBar';
import { useTranslation } from 'react-i18next';

function TabsComponent() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(neuraTalkBuilderTabs[0].id);

  return (
    <Tabs
      value={activeTab}
      onValueChange={value => setActiveTab(value)}
      className="w-full h-0 flex-1 flex flex-col"
    >
      <TabBar />

      {neuraTalkBuilderTabs.map(tab => (
        <TabsContent
          key={tab.id}
          value={tab.id}
          className={cn('!mt-0 border-none h-0 flex flex-col', {
            'flex-1': activeTab === tab.id,
          })}
        >
          {tab.Component ? (
            <tab.Component />
          ) : (
            <div className="flex items-center justify-center p-6 bg-muted h-full text-secondary-500">
              <p>{t('tabs.contentComingSoon', { tabName: tab.labelKey })}</p>
            </div>
          )}
        </TabsContent>
      ))}
    </Tabs>
  );
}

export default TabsComponent;
