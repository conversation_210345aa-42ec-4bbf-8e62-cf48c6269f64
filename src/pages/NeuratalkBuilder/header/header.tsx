import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useGetBotByIdQuery, useUpdateBotMutation } from '@/store/api/chatBotApi';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/ui/use-toast';
import ChatbotBreadcrumb from './chatbotBreadcrumb';
import ChatbotInfo from './chatbotInfo';
import EditChatbotDialog from './editChatbotDialog';

export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml'];
const NAME_REGEX = /^[a-zA-Z0-9._-]+$/;
const MAX_NAME_LENGTH = 50;
const MAX_DESC_LENGTH = 150;
const MAX_FILE_SIZE = 2 * 1024 * 1024;

const Header = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id');
  const { data: bot, isLoading, error } = useGetBotByIdQuery(botId, { skip: !botId });
  const [updateBot, { isLoading: isUpdating }] = useUpdateBotMutation();

  const fileInputRef = useRef(null);
  const uploadIntervalRef = useRef(null);

  const [state, setState] = useState({
    dialogOpen: false,
    editableTitle: '',
    editableDomain: '',
    editableDescription: '',
    previewUrl: null,
    selectedImage: null,
    uploadProgress: 0,
    isUploading: false,
    isDragging: false,
    errors: { name: '', domain: '', description: '', image: '' },
  });

  const updateState = updates => setState(prev => ({ ...prev, ...updates }));

  const getAvatarUrlFromName = name => {
    const words = name.trim().split(' ');
    if (words.length === 0) return '';
    const initials = words[0][0] + (words.length > 1 ? words[words.length - 1][0] : '');
    return `https://ui-avatars.com/api/?name=${initials}&rounded=true&background=a0a0a0`;
  };

  const validateField = (field, value) => {
    switch (field) {
      case 'name':
        if (value.length > MAX_NAME_LENGTH) return t('editor.nameMaxError');
        if (!value.trim()) return t('editor.nameRequired');
        if (!NAME_REGEX.test(value)) return t('editor.invalidName');
        return '';
      case 'domain':
        return value ? '' : t('editor.domainRequired');
      case 'description':
        return value.length > MAX_DESC_LENGTH ? t('editor.descMaxError') : '';
      case 'image':
        if (!value) return '';
        if (value.size > MAX_FILE_SIZE) return t('editor.fileTooLarge');
        if (!SUPPORTED_IMAGE_TYPES.includes(value.type)) return t('editor.unsupportedFile');
        return '';
      default:
        return '';
    }
  };

  const handleFieldChange = field => value => {
    const error = validateField(field, value);
    updateState({
      [field === 'name'
        ? 'editableTitle'
        : field === 'domain'
          ? 'editableDomain'
          : 'editableDescription']: value,
      errors: { ...state.errors, [field]: error },
    });
  };

  const handleImageChange = file => {
    if (!file) return;
    const error = validateField('image', file);
    if (error) {
      updateState({ errors: { ...state.errors, image: error } });
      return;
    }

    if (state.previewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(state.previewUrl);
    }
    if (uploadIntervalRef.current) {
      clearInterval(uploadIntervalRef.current);
    }

    updateState({
      selectedImage: file,
      isUploading: true,
      uploadProgress: 0,
      errors: { ...state.errors, image: '' },
    });

    uploadIntervalRef.current = setInterval(() => {
      setState(prev => {
        const newProgress = prev.uploadProgress + 10;
        if (newProgress >= 100) {
          clearInterval(uploadIntervalRef.current);
          uploadIntervalRef.current = null;
          return {
            ...prev,
            isUploading: false,
            uploadProgress: 100,
            previewUrl: URL.createObjectURL(file),
          };
        }
        return { ...prev, uploadProgress: newProgress };
      });
    }, 100);
  };

  const removeImage = () => {
    if (state.previewUrl?.startsWith('blob:')) {
      URL.revokeObjectURL(state.previewUrl);
    }
    if (fileInputRef.current) fileInputRef.current.value = '';
    updateState({
      selectedImage: null,
      previewUrl: null,
      uploadProgress: 0,
      isUploading: false,
      errors: { ...state.errors, image: '' },
    });
  };

  const isSaveEnabled = () => {
    const isTitleValid = state.editableTitle.trim().length > 0 && !state.errors.name;
    const isDomainValid = state.editableDomain && !state.errors.domain;
    const isDescriptionValid = !state.errors.description;
    const isAnyFieldChanged =
      state.editableTitle.trim() !== (bot?.name || '').trim() ||
      state.editableDomain.trim() !== (bot?.domain || '').trim() ||
      (state.editableDescription.trim() || 'No description') !==
        (bot?.description || 'No description').trim() ||
      (state.selectedImage !== null && state.selectedImage !== bot?.image);

    return isTitleValid && isDomainValid && isDescriptionValid && isAnyFieldChanged;
  };

  const handleSave = async e => {
    e.preventDefault();
    if (!botId || !isSaveEnabled()) return;

    try {
      const payload = {
        name: state.editableTitle.trim(),
        domain: state.editableDomain,
        description: state.editableDescription.trim() || 'No description',
        ...(state.selectedImage && { image: state.selectedImage }),
      };
      await updateBot({ id: botId, payload }).unwrap();
      toast({ description: 'Chatbot updated successfully' });
      updateState({ dialogOpen: false });
    } catch (err) {
      console.error('Update failed:', err);
      toast({ description: t('editor.updateFailed'), variant: 'destructive' });
    }
  };

  useEffect(() => {
    if (state.dialogOpen && bot) {
      updateState({
        editableTitle: bot.name || '',
        editableDomain: bot.domain || '',
        editableDescription: bot.description || '',
        previewUrl: bot.image || null,
        selectedImage: null,
        uploadProgress: 0,
        isUploading: false,
        errors: { name: '', domain: '', description: '', image: '' },
      });
    }
    return () => {
      if (uploadIntervalRef.current) {
        clearInterval(uploadIntervalRef.current);
        uploadIntervalRef.current = null;
      }
      if (state.previewUrl?.startsWith('blob:')) {
        URL.revokeObjectURL(state.previewUrl);
      }
    };
  }, [state.dialogOpen, bot]);

  if (isLoading) return <div data-testid="loading">{t('editor.loading')}</div>;
  if (error) return <div data-testid="error">{t('editor.loadError')}</div>;

  return (
    <div className="bg-white" data-testid="header-container">
      <ChatbotBreadcrumb t={t} />
      <ChatbotInfo
        bot={bot}
        previewUrl={state.previewUrl}
        getAvatarUrlFromName={getAvatarUrlFromName}
        onEdit={() => updateState({ dialogOpen: true })}
        t={t}
      />
      <EditChatbotDialog
        open={state.dialogOpen}
        onOpenChange={open => updateState({ dialogOpen: open })}
        state={state}
        handleFieldChange={handleFieldChange}
        handleImageChange={handleImageChange}
        removeImage={removeImage}
        handleSave={handleSave}
        isUpdating={isUpdating}
        fileInputRef={fileInputRef}
        isSaveEnabled={isSaveEnabled}
        t={t}
      />
    </div>
  );
};

export default Header;
