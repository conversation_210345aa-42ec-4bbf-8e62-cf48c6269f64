import React from 'react';
import {
  B<PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON>rumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

const ChatbotBreadcrumb = ({ t }) => (
  <div className="px-6 py-3 flex justify-between items-center">
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink href="#" data-testid="breadcrumb-neuraTalk">
            {t('navigation.neuraTalk')}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage data-testid="breadcrumb-create">
            {t('navigation.create')}
          </BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  </div>
);

export default ChatbotBreadcrumb;