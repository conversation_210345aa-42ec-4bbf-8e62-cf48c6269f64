import React from 'react';
import { Pencil } from 'lucide-react';
import Group_24426 from '@/assets/icons/Group_24426.svg';

const ChatbotInfo = ({ bot, previewUrl, getAvatarUrlFromName, onEdit, t }) => (
  <div className="px-6 py-4 border-secondary-200">
    <div className="flex items-start space-x-3">
      <div className="w-9 h-9 p-1 bg-secondary-200 rounded-full flex items-center justify-center">
        <img
          src={previewUrl || (bot?.name ? getAvatarUrlFromName(bot.name) : Group_24426)}
          alt="Chatbot Icon"
          className="w-full h-full object-contain"
          data-testid="chatbot-image"
        />
      </div>
      <div className="flex-1">
        <div className="flex items-center space-x-2">
          <h3 className="text-sm font-medium text-black" data-testid="chatbot-name">
            {bot?.name || t('chatbot.untitled')}
          </h3>
          <button
            type="button"
            aria-label="Edit Chatbot"
            data-testid="edit-button"
            onClick={onEdit}
          >
            <Pencil className="w-3.5 h-3.5" />
          </button>
        </div>
        <div className="text-xs font-medium text-tertiary-500 mt-0.5" data-testid="chatbot-domain">
          {bot?.domain ?? t('chatbot.noDomain')}
        </div>
        <div className="text-xs text-tertiary-500 mt-1" data-testid="chatbot-description">
          {bot?.description || t('chatbot.noDescription')}
        </div>
      </div>
    </div>
  </div>
);

export default ChatbotInfo;
