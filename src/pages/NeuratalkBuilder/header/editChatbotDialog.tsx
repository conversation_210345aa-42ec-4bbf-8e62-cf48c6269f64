import React, { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON>ooter,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { ImageUp, X, FileImage } from 'lucide-react';
import { FloatingField } from '@/components/ui/floating-label';
import { SUPPORTED_IMAGE_TYPES } from './header';

const DOMAIN_OPTIONS_CONFIG = [
  { value: 'Ecomm', labelKey: 'domains.ecomm' },
  { value: 'Telecom', labelKey: 'domains.telecom' },
  { value: 'Retail', labelKey: 'domains.retail' },
  { value: 'Travel', labelKey: 'domains.travel' },
  { value: 'Other', labelKey: 'domains.other' },
];

const EditChatbotDialog = ({
  open,
  onOpenChange,
  state,
  handleFieldChange,
  handleImageChange,
  removeImage,
  handleSave,
  isUpdating,
  fileInputRef,
  isSaveEnabled,
  t,
}) => {
  const DOMAIN_OPTIONS = DOMAIN_OPTIONS_CONFIG.map(opt => ({
    value: opt.value,
    label: t(opt.labelKey),
  }));

  useEffect(() => {
    const label = document.querySelector('label[for="image-upload"]');
    if (!label) return;

    const handleDragOver = e => {
      e.preventDefault();
      if (!state.isUploading) onOpenChange({ ...state, isDragging: true });
    };
    const handleDragLeave = e => {
      e.preventDefault();
      onOpenChange({ ...state, isDragging: false });
    };
    const handleDrop = e => {
      e.preventDefault();
      onOpenChange({ ...state, isDragging: false });
      if (!state.isUploading) handleImageChange(e.dataTransfer.files?.[0]);
    };

    label.addEventListener('dragover', handleDragOver);
    label.addEventListener('dragenter', handleDragOver);
    label.addEventListener('dragleave', handleDragLeave);
    label.addEventListener('drop', handleDrop);

    return () => {
      label.removeEventListener('dragover', handleDragOver);
      label.removeEventListener('dragenter', handleDragOver);
      label.removeEventListener('dragleave', handleDragLeave);
      label.removeEventListener('drop', handleDrop);
    };
  }, [state.isUploading, state.dialogOpen]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="fixed left-1/2 top-1/2 z-50 w-full max-w-md -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white shadow-lg px-6"
        data-testid="edit-dialog"
      >
        <DialogHeader>
          <DialogTitle data-testid="dialog-title">{t('common.edit')}</DialogTitle>
        </DialogHeader>
        <hr className="border-secondary-200 my-1 -mx-6 w-[calc(100%+3rem)]" />
        <form onSubmit={handleSave} className="space-y-6" data-testid="edit-form">
          <div>
            <FloatingField
              label={t('editor.chatbotName')}
              value={state.editableTitle}
              onChange={handleFieldChange('name')}
              type="text"
              autoFocus
              disabled={isUpdating}
              data-testid="name-input"
            />
            {state.errors.name && (
              <p className="text-red-500 text-xs mt-1" data-testid="name-error">
                {state.errors.name}
              </p>
            )}
          </div>
          <div>
            <FloatingField
              label={t('editor.domain')}
              as="select"
              value={state.editableDomain}
              onChange={handleFieldChange('domain')}
              options={DOMAIN_OPTIONS}
              disabled={!state.editableTitle.trim() || state.errors.name || isUpdating}
              data-testid="domain-select"
            />
            {state.errors.domain && (
              <p className="text-red-500 text-xs mt-1" data-testid="domain-error">
                {state.errors.domain}
              </p>
            )}
          </div>
          <div>
            <FloatingField
              label={t('editor.description')}
              as="textarea"
              value={state.editableDescription}
              onChange={handleFieldChange('description')}
              disabled={
                !state.editableTitle.trim() ||
                state.errors.name ||
                !state.editableDomain ||
                state.errors.domain ||
                isUpdating
              }
              data-testid="description-textarea"
            />
            {state.errors.description && (
              <p className="text-red-500 text-xs mt-1" data-testid="description-error">
                {state.errors.description}
              </p>
            )}
          </div>
          <div className="relative bg-tertiary-50 w-full h-auto rounded-md overflow-hidden">
            <input
              ref={fileInputRef}
              id="image-upload"
              type="file"
              accept={SUPPORTED_IMAGE_TYPES.join(',')}
              className="hidden"
              onChange={e => handleImageChange(e.target.files?.[0])}
              disabled={state.isUploading || isUpdating}
              data-testid="image-input"
            />
            {!state.selectedImage && !state.previewUrl ? (
              <label
                htmlFor="image-upload"
                className={`cursor-pointer w-full h-full py-4 flex flex-col items-center justify-center border-dashed border rounded-md text-tertiary-400 transition ${state.isDragging ? 'bg-tertiary-50 border-tertiary-400' : 'hover:bg-tertiary-100'}`}
                data-testid="image-upload-label"
              >
                <ImageUp className="w-8 h-8 mb-1" />
                <div>{t('editor.uploadImage')}</div>
                <div className="text-xs mt-1 text-tertiary-400">{t('editor.uploadFormat')}</div>
              </label>
            ) : (
              <div
                className="w-full flex items-center gap-2 p-2 bg-tertiary-50 rounded-md border"
                data-testid="image-preview"
              >
                {state.previewUrl && !state.isUploading ? (
                  <img
                    src={state.previewUrl}
                    alt="Preview"
                    className="w-8 h-8 object-cover"
                    data-testid="image-preview-img"
                  />
                ) : (
                  <FileImage className="w-6 h-6 text-tertiary-400" data-testid="file-icon" />
                )}
                <span className="flex-1 text-sm truncate" data-testid="image-name">
                  {state.selectedImage?.name || 'Current Image'}
                </span>
                {/* {state.isUploading && (
                  <div className="flex-1">
                    <Progress value={state.uploadProgress} data-testid="upload-progress" />
                  </div>
                )} */}
                <button
                  type="button"
                  onClick={removeImage}
                  className="ml-2 p-1 hover:bg-tertiary-100"
                  data-testid="remove-image-button"
                >
                  <X className="w-6 h-6 text-tertiary-600" />
                </button>
              </div>
            )}
            {state.errors.image && (
              <p className="text-red-500 text-xs mt-1" data-testid="image-error">
                {state.errors.image}
              </p>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <button
                type="button"
                className="px-6 py-2 w-28 rounded border text-tertiary-700 bg-white hover:bg-tertiary-100"
                data-testid="cancel-button"
              >
                {t('common.cancel')}
              </button>
            </DialogClose>
            <button
              type="submit"
              className="px-6 py-2 w-28 rounded bg-primary-600 text-white disabled:opacity-50"
              disabled={!isSaveEnabled() || isUpdating}
              data-testid="save-button"
            >
              {t('common.save')}
            </button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditChatbotDialog;
