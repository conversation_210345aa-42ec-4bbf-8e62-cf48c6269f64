import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChatbotCard from '@/pages/Home/ChatbotCard';
import { MemoryRouter } from 'react-router-dom';
import { ChatbotStatus } from '@/lib/enums';
import { beforeEach, describe, expect, test, vi } from 'vitest';
import userEvent from '@testing-library/user-event';

const mockNavigate = vi.fn();
const mockOnDelete = vi.fn();
const mockOnClone = vi.fn();

describe('ChatbotCard Edit Modal Functionality', () => {
  beforeEach(() => {
    render(
      <MemoryRouter>
        <ChatbotCard
          id="bot-1"
          title="TestBot"
          description="Initial description"
          status={ChatbotStatus.Live}
          lastUpdated="2 days ago"
          onDelete={mockOnDelete}
          onClone={mockOnClone}
          navigate={mockNavigate}
        />
      </MemoryRouter>
    );
  });

  test('should render the card with correct content', () => {
    expect(screen.getByTestId('title')).toHaveTextContent('TestBot');
    expect(screen.getByTestId('description')).toHaveTextContent('Initial description');
    expect(screen.getByTestId('status')).toHaveTextContent(ChatbotStatus.Live);
    expect(screen.getByTestId('last-updated')).toHaveTextContent(/last updated/i);
  });

  test('should call navigate when card is clicked', async() => {
    const card = screen.getByText('TestBot').closest('div');
    await userEvent.click(card!);
    expect(mockNavigate).toHaveBeenCalledWith('/app/neuratalk-builder?id=bot-1');
  });

  test('should call onClone when clone is clicked', async () => {
    const moreOptionsBtn = screen.getByTestId('more-options-bot-1');
    userEvent.click(moreOptionsBtn);
    const cloneBtn = await screen.findByTestId('clone-button-bot-1');
    fireEvent.click(cloneBtn);
    expect(mockOnClone).toHaveBeenCalledWith('bot-1');
  });

  test('should open and confirm delete dialog', async () => {
    const moreOptionsBtn = screen.getByTestId('more-options-bot-1');
    await userEvent.click(moreOptionsBtn);
    const deleteBtn = screen.getByTestId('delete-button-bot-1');
    fireEvent.click(deleteBtn);

    const confirmBtn = await screen.findByText(/yes, delete/i);
    fireEvent.click(confirmBtn);
    expect(mockOnDelete).toHaveBeenCalledWith('bot-1');
  });

  test('should close dialog on cancel', async () => {
    const moreOptionsBtn = screen.getByTestId('more-options-bot-1');
    await userEvent.click(moreOptionsBtn);
    
    const deleteBtn = screen.getByTestId('delete-button-bot-1');
    fireEvent.click(deleteBtn);

    const cancelBtn = await screen.findByText(/no, cancel/i);
    fireEvent.click(cancelBtn);
    expect(screen.queryByText(/are you sure you want to delete/i)).not.toBeInTheDocument();
  });
});
