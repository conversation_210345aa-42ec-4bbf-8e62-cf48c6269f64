import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Header from '../NeuratalkBuilder/header';
import { afterAll, beforeAll, describe, expect, it, vi } from 'vitest';
import { render } from '@/test/utils';
import { useTranslation } from 'react-i18next';

// Mock API calls
vi.mock('@/store/api/languageApi', () => ({
  useGetBotLanguagesQuery: vi.fn(() => ({
    data: {
      data: {
        items: [{ langId: 'en' }],
      },
    },
  })),
  useGetLanguagesQuery: vi.fn(() => ({
    data: {
      data: {
        items: [{ id: 'en', name: 'English', code: 'en' }],
      },
    },
  })),
}));

// Mock useTranslation
vi.mock('react-i18next', async () => {
  const actual = await vi.importActual('react-i18next');
  return {
    ...actual,
    useTranslation: () => ({
      t: (key: string) => {
        if (key === 'editor.domain') return 'Domain';
        if (key === 'common.selectOption') return 'Select option';
        if (key === 'domains.telecom') return 'Telecom';
        return key;
      },
    }),
  };
});

// Mock Redux hooks and actions
let currentChatbotState = {
  title: 'Test Chatbot',
  domain: 'ecomm',
  description: 'A test chatbot description',
};

vi.mock('@/hooks/useRedux', () => ({
  useAppSelector: vi.fn(selector => selector({ chatbot: { ...currentChatbotState } })),
  useAppDispatch: () => vi.fn(),
}));

vi.mock('@/store/slices/chatbotSlice', () => ({
  updateTitle: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, title: payload };
    return { type: 'chatbot/updateTitle', payload };
  }),
  updateDomain: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, domain: payload };
    return { type: 'chatbot/updateDomain', payload };
  }),
  updateDescription: vi.fn(payload => {
    currentChatbotState = { ...currentChatbotState, description: payload };
    return { type: 'chatbot/updateDescription', payload };
  }),
}));

// Mock SVG import
vi.mock('../../../public/Group_24426.svg', () => ({ default: 'mocked-icon.svg' }));

// Mock createObjectURL for file upload
beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => 'blob:mock-url');
});

afterAll(() => {
  vi.restoreAllMocks();
});

describe.skip('Header', () => {
  it('renders chatbot title, domain, and description', async () => {
    render(<Header />);
    const editBtn = await screen.findByTestId('edit-button');
    await userEvent.click(editBtn);
    expect(await screen.findByTestId('edit-dialog')).toBeInTheDocument();
  });

  it('displays the edit dialog with all form fields when edit is clicked', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const titleInput = await screen.findByTestId('title-input');
    const descriptionTextarea = await screen.findByTestId('description-textarea');

    expect(titleInput).toBeVisible();
    expect(descriptionTextarea).toBeVisible();
  });

  it('disables SAVE button if fields are not changed or incomplete', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const saveButton = await screen.findByTestId('save-button');
    expect(saveButton).toBeDisabled();
  });

  it('handles image upload with valid file', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const nameInput = await screen.findByTestId('name-input');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'Valid_Bot-123.name');
    expect(screen.queryByTestId('name-error')).not.toBeInTheDocument();
  });

  it('4. shows error for invalid characters in chatbot name', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const nameInput = await screen.findByTestId('name-input');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'Invalid@Name');
    expect(await screen.findByTestId('name-error')).toHaveTextContent('editor.invalidName');
  });

  it('5. shows error if name exceeds 50 characters', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const longName = 'a'.repeat(51);
    const nameInput = await screen.findByTestId('name-input');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, longName);
    expect(await screen.findByTestId('name-error')).toHaveTextContent('editor.nameMaxError');
  });

  it('6. shows error if name is empty', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const nameInput = await screen.findByTestId('name-input');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, ' ');
    expect(await screen.findByTestId('name-error')).toHaveTextContent('editor.nameRequired');
  });

  it('7-9. accepts valid description, shows error > 150 chars, allows blank', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const descInput = screen.getByTestId('description-textarea');
    await userEvent.clear(descInput);
    await userEvent.type(descInput, 'Description with, punctuation.');
    expect(screen.queryByTestId('description-error')).not.toBeInTheDocument();

    await userEvent.clear(descInput);
    await userEvent.type(descInput, 'a'.repeat(151));
    expect(await screen.findByTestId('description-error')).toHaveTextContent('editor.descMaxError');

    await userEvent.clear(descInput);
    await userEvent.type(descInput, ' ');
    expect(screen.queryByTestId('description-error')).not.toBeInTheDocument();
  });

  it('10-12. allows only pre-defined domains and validates selection', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));
    const domainSelect = await screen.findByTestId('domain-select');
    await userEvent.selectOptions(domainSelect, 'Ecomm');
    expect(domainSelect).toHaveValue('Ecomm');

    await userEvent.selectOptions(domainSelect, '');
    expect(await screen.findByTestId('domain-error')).toHaveTextContent('editor.domainRequired');
  });

  it('13-15. validates file type and size for image upload', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const imageInput = screen.getByTestId('image-input');

    const invalidFile = new File(['dummy'], 'file.txt', { type: 'text/plain' });
    await userEvent.upload(imageInput, invalidFile);
    expect(await screen.findByTestId('image-error')).toHaveTextContent('editor.unsupportedFile');

    const largeFile = new File([new ArrayBuffer(2 * 1024 * 1024 + 1)], 'large.png', {
      type: 'image/png',
    });
    Object.defineProperty(largeFile, 'size', { value: 2 * 1024 * 1024 + 1 });
    await userEvent.upload(imageInput, largeFile);
    expect(await screen.findByTestId('image-error')).toHaveTextContent('editor.fileTooLarge');
  });

  it('16. allows drag and drop image upload', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const dropZone = screen.getByTestId('image-upload-label');
    const imageFile = new File(['(⌐□_□)'], 'avatar.png', { type: 'image/png' });

    fireEvent.drop(dropZone, {
      dataTransfer: { files: [imageFile] },
    });

    await waitFor(() => expect(screen.getByTestId('image-preview')).toBeInTheDocument());
  });

  it('18-20. allows saving only if all valid, blocks if any invalid', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const nameInput = screen.getByTestId('name-input');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'Valid-Name');

    const domainSelect = screen.getByTestId('domain-select');
    await userEvent.selectOptions(domainSelect, 'Ecomm');

    const descInput = screen.getByTestId('description-textarea');
    await userEvent.clear(descInput);
    await userEvent.type(descInput, 'A good bot.');

    const saveBtn = screen.getByTestId('save-button');
    expect(saveBtn).not.toBeDisabled();

    // Trigger error
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, '@invalid');
    expect(saveBtn).toBeDisabled();
  });

  it('19. shows success toast on successful update', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const nameInput = screen.getByTestId('name-input');
    const domainSelect = screen.getByTestId('domain-select');
    const descInput = screen.getByTestId('description-textarea');
    const saveBtn = screen.getByTestId('save-button');

    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'UpdatedBot');

    await userEvent.selectOptions(domainSelect, 'Ecomm');
    await userEvent.clear(descInput);
    await userEvent.type(descInput, 'Updated Description');

    await userEvent.click(saveBtn);
    await waitFor(() => {
      expect(screen.queryByTestId('edit-dialog')).not.toBeInTheDocument();
    });
  });

  it('enables SAVE button when form is valid', async () => {
    render(<Header />);
    await userEvent.click(screen.getByTestId('edit-button'));

    const titleInput = await screen.findByTestId('title-input');
    const descriptionTextarea = await screen.findByTestId('description-textarea');
    const saveButton = await screen.findByTestId('save-button');

    // Initially, the button is disabled
    expect(saveButton).toBeDisabled();

    // Clear existing value and fill out the form
    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'New Title');
    await waitFor(() => expect(titleInput).toHaveValue('New Title'));

    const domainSelectElement = await screen.findByLabelText('Domain');
    // Wait for the domain select to be enabled before interacting with it
    await waitFor(() => expect(domainSelectElement).toBeEnabled());

    await userEvent.click(domainSelectElement); // Open the select dropdown

    const telecomOption = await waitFor(() => screen.findByRole('option', { name: 'Telecom' })); // Find the option by its label
    await userEvent.click(telecomOption); // Click the option

    await waitFor(() => expect(screen.getByRole('combobox', { name: 'Telecom' })).toBeInTheDocument());

    await userEvent.clear(descriptionTextarea);
    await userEvent.type(descriptionTextarea, 'New Description');
    await waitFor(() => expect(descriptionTextarea).toHaveValue('New Description'));

    // The button should now be enabled
    expect(saveButton).toBeEnabled();
  });
});