import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Copy, CheckCircle2 } from 'lucide-react';
import { Channel } from '../types';
import { useTranslation } from 'react-i18next';

interface ConfigurationViewProps {
  channel?: Channel;
  selectedWABANumber: string;
  webhookUrl: string;
  onChangeNumber: () => void;
  onSwitch: () => void;
}

const ConfigurationView: React.FC<ConfigurationViewProps> = ({
  channel,
  selectedWABANumber,
  webhookUrl,
  onChangeNumber,
  onSwitch,
}) => {
  const { t } = useTranslation();
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="flex-1 p-8 max-w-4xl">
      <h2 className="text-lg font-medium mb-8">{t('channels.selectWABA')}</h2>

      {/* Selected WABA Number */}
      <Card className="mb-6 border-tertiary-300">
        <CardContent className="px-0">
          <div className="border-b p-6 border-tertiary-300 flex items-center justify-between">
            <div className="flex items-center gap-1">
              <span className="font-medium">{selectedWABANumber}</span>
              <CheckCircle2 className="w-6 h-6 fill-primary text-white" />
            </div>
            <Button
              variant="ghost"
              onClick={onChangeNumber}
              className="text-primary-600 border-primary-300 hover:bg-primary-100"
            >
              {t('channels.changeNumber')}
            </Button>
          </div>

          <div className="p-6 flex flex-col gap-2">
            <h3 className="text-sm font-medium text-tertiary-500">{t('channels.webhook')}</h3>
            <div className="flex items-center justify-between text-tertiary-700 bg-tertiary-100 px-3 py-1.5 border border-tertiary-300 rounded-md break-all">
              <span className="font-mono text-sm ">{webhookUrl}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(webhookUrl)}
                className="ml-2 p-2 h-8 w-8"
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>

            <p className="text-tertiary-500">{t('channels.webhookInstruction')}</p>
          </div>
        </CardContent>
      </Card>

      <Card className="border-tertitext-tertiary-200">
        <CardContent className="p-9">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">M</span>
                </div>
                <div className="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">360</span>
                </div>
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">BSP</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-tertiary-900">{t('channels.switchToMeta')}</h4>
                <p className="text-sm text-tertiary-600">{t('channels.switchDescription')}</p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={onSwitch}
              className="text-primary-600 px-7 border-primary-300 hover:bg-primary-50"
            >
              {t('channels.switch')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfigurationView;
