import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WABANumber } from './types';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

interface WABASelectionProps {
  wabaNumbers: WABANumber[];
  selectedNumber: string | null;
  onNumberSelect: (numberId: string) => void;
  onCancel: () => void;
  onConnect: () => void;
}

const WABASelection: React.FC<WABASelectionProps> = ({
  wabaNumbers,
  selectedNumber,
  onNumberSelect,
  onCancel,
  onConnect,
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex-1 p-8 max-w-4xl">
      <h2 className="text-lg font-medium mb-8">{t('channels.selectWABA')}</h2>

      <div className="space-y-4 mb-8">
        {wabaNumbers.map(waba => (
          <Card
            key={waba.id}
            onClick={() => onNumberSelect(waba.id)}
            className={cn(
              'cursor-pointer border transition-all duration-200 hover:shadow-md',
              selectedNumber === waba.id
                ? 'border-primary-500 border-2 bg-primary-50'
                : 'border-tertiary-200 hover:border-tertiary-300'
            )}
          >
            <CardContent className="px-4 py-7">
              <div className="flex items-center justify-between">
                <span className="font-medium">{waba.number}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-end gap-3">
        <Button
          variant="outline"
          onClick={onCancel}
          className="px-6 py-2 border-tertiary-300 text-tertiary-700 hover:bg-tertiary-50"
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={onConnect}
          disabled={!selectedNumber}
          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t('channels.connect')}
        </Button>
      </div>
    </div>
  );
};

export default WABASelection;
