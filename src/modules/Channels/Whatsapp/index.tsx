import React, { useState } from 'react';
import WABASelection from './WABASelection';
import OnboardingView from './OnboardingView';
import ConfigurationView from './ConfigurationView';
import { OnboardingOptionId, onboardingOptions, wabaNumbers, WhatsappViewTab } from './config';

function WhatsappView() {
  const [view, setView] = useState(WhatsappViewTab.ONBOARDING);
  const [selectedWABANumber, setSelectedWABANumber] = useState<string | null>(null);

  const onOptionSelect = (optionId: OnboardingOptionId) => {
    if (optionId === OnboardingOptionId.Meta) {
      return;
    }
    setView(WhatsappViewTab.WABA_SELECTION);
  };

  const onNumberSelect = (number: string) => {
    setSelectedWABANumber(number);
  };

  const onCancel = () => {
    setView(WhatsappViewTab.ONBOARDING);
  };

  const onConnect = () => {
    setView(WhatsappViewTab.CONFIGURATION);
  };

  const onChangeNumber = () => {
    setSelectedWABANumber(null);
    setView(WhatsappViewTab.WABA_SELECTION);
  };

  const renderView = {
    [WhatsappViewTab.ONBOARDING]: (
      <OnboardingView options={onboardingOptions} onOptionSelect={onOptionSelect} />
    ),
    [WhatsappViewTab.WABA_SELECTION]: (
      <WABASelection
        wabaNumbers={wabaNumbers}
        selectedNumber={selectedWABANumber}
        onNumberSelect={onNumberSelect}
        onCancel={onCancel}
        onConnect={onConnect}
      />
    ),
    [WhatsappViewTab.CONFIGURATION]: (
      <ConfigurationView
        selectedWABANumber={wabaNumbers.find(waba => waba.id === selectedWABANumber)?.number ?? ''}
        webhookUrl="https://ngage.cpaas.com/v1/bot/wellness_care"
        onChangeNumber={onChangeNumber}
        onSwitch={onSwitch}
      />
    ),
  };

  return renderView[view];
}

export default WhatsappView;
function onSwitch(): void {
  throw new Error('Function not implemented.');
}
