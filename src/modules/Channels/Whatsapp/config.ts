import { OnboardingOption, WABANumber } from './types';

export enum WhatsappViewTab {
  ONBOARDING = 'onboarding',
  WABA_SELECTION = 'waba-selection',
  CONFIGURATION = 'configuration',
}

export enum OnboardingOptionId {
  Ngage = 'ngage',
  Meta = 'meta',
}

// Mock data for WABA numbers
export const wabaNumbers: WABANumber[] = [
  { id: '1', number: '+91 9642456783' },
  { id: '2', number: '+91 8456822104' },
  { id: '3', number: '+91 9735481921' },
  { id: '4', number: '+91 9642456785' },
];

export const onboardingOptions: OnboardingOption[] = [
  {
    id: OnboardingOptionId.Ngage,
    title: 'NGAGE',
    descriptionKey: 'whatsapp.onboarding.ngage.description',
    icon: '/api/placeholder/32/32',
    buttonTextKey: 'common.getStarted',
  },
  {
    id: OnboardingOptionId.Meta,
    title: 'Meta Cloud API',
    descriptionKey: 'whatsapp.onboarding.meta.description',
    icon: '/api/placeholder/32/32',
    buttonTextKey: 'common.getStarted',
  },
];
