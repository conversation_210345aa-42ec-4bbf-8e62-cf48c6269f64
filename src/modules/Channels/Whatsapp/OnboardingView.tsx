import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';
import { OnboardingOption } from './types';
import { OnboardingOptionId } from './config';
import { useTranslation } from 'react-i18next';

interface OnboardingViewProps {
  options: OnboardingOption[];
  onOptionSelect: (optionId: OnboardingOptionId) => void;
}

const OnboardingView: React.FC<OnboardingViewProps> = ({ options, onOptionSelect }) => {
  const { t } = useTranslation();
  return (
    <div className="flex-1 p-8 max-w-4xl">
      <div className="max-w-3xl">
        <h2 className="text-lg font-medium mb-8">{t('common.onboarding')}</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {options.map(option => (
            <Card
              key={option.id}
              className="border border-tertiary-200 hover:shadow-md transition-shadow"
            >
              <CardContent className="px-8 py-12 text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-tertiary-100 rounded-full flex items-center justify-center">
                  <img
                    src={option.icon}
                    alt={option.title}
                    className="w-8 h-8"
                    onError={e => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>

                <h3 className="font-semibold mb-2">{option.title}</h3>

                <p className="text-sm text-tertiary-600 mb-6 leading-relaxed">
                  {t(option.descriptionKey)}
                </p>

                <Button
                  onClick={() => onOptionSelect(option.id)}
                  className="mt-5 bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium"
                >
                  {t(option.buttonTextKey)}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OnboardingView;
