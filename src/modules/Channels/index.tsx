import React, { useState } from 'react';
import { Tabs } from '@/components/ui/tabs';
import { ChannelTabState } from './types';
import { ChannelFilterType, ChannelId, ChannelMainTab } from './enums';
import { availableChannels, filters, mainChannelTab, myChannels } from './config';
import MainContent from './MainContent';
import SidebarContainer from './Sidebar/SidebarContainer';
import TabNavigation from './Sidebar/TabNavigation';

const ChannelsTab: React.FC = () => {
  const [state, setState] = useState<ChannelTabState>({
    mainTab: ChannelMainTab.AVAILABLE,
    searchQuery: '',
    activeFilter: ChannelFilterType.ALL,
    selectedChannel: null,
  });

  const handleChannelSelect = (channelId: ChannelId) => {
    setState(prev => ({ ...prev, selectedChannel: channelId }));
  };

  const handleSearchChange = (query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  };

  const handleFilterChange = (filterId: string) => {
    setState(prev => ({ ...prev, activeFilter: filterId }));
  };

  const currentChannels =
    state.mainTab === ChannelMainTab.AVAILABLE ? availableChannels : myChannels;

  return (
    <Tabs
      value={state.mainTab}
      onValueChange={value => setState(prev => ({ ...prev, mainTab: value as ChannelMainTab }))}
      className="flex flex-1 h-0 w-full"
    >
      <div className="flex flex-col h-full w-[440px] border-r border-tertiary-300">
        <TabNavigation
          activeTab={state.mainTab}
          tabs={mainChannelTab}
          onTabChange={value => setState(prev => ({ ...prev, mainTab: value as ChannelMainTab }))}
        />
        <SidebarContainer
          activeTab={state.mainTab}
          channels={currentChannels}
          selectedChannel={state.selectedChannel}
          searchQuery={state.searchQuery}
          activeFilter={state.activeFilter}
          filters={filters}
          onChannelSelect={handleChannelSelect}
          onSearchChange={handleSearchChange}
          onFilterChange={handleFilterChange}
        />
      </div>
      <MainContent selectedChannel={state.selectedChannel} />
    </Tabs>
  );
};

export default ChannelsTab;
