import { ChannelFilterType, ChannelId, ChannelMainTab } from "./enums";

export interface Channel {
  id: ChannelId;
  name: string;
  type: 'native' | 'text' | 'voice';
  status: 'active' | 'inactive' | 'connected' | 'available';
  icon: string;
  description?: string;
  isVerified?: boolean;
  isConnected?: boolean;
  phoneNumber?: string;
  webhookUrl?: string;
  color?: string;
}

export interface ChannelFilter {
  id: string;
  labelKey: string;
  type: ChannelFilterType;
}

export interface ChannelTabState {
  mainTab: ChannelMainTab;
  searchQuery: string;
  activeFilter: string;
  selectedChannel: ChannelId | null;
}
