import React from 'react';
import EmptyState from '@/components/EmptyState';
import { ChannelId } from './enums';
import WhatsappView from './Whatsapp';
import { useTranslation } from 'react-i18next';

interface MainContentProps {
  selectedChannel: ChannelId | null;
}

const MainContent: React.FC<MainContentProps> = ({ selectedChannel }) => {
  const { t } = useTranslation();
  if (!selectedChannel) {
    return <EmptyState title={t('channels.selectChannels')} description={t('channels.nothingSelected')} />;
  }

  const renderChannel = {
    [ChannelId.WhatsApp]: <WhatsappView />,
  };

  return renderChannel[selectedChannel] ?? <EmptyState />;
};

export default MainContent;
