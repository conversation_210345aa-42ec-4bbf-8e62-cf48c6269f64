# Channels Module Test Suite

This directory contains comprehensive test cases for the Channels module using Vitest (Vite's testing framework).

## Restructured Test Architecture

Following best practices for coverage-focused testing, the test suite has been consolidated into **2 main test files** that provide comprehensive coverage while maintaining simplicity and maintainability.

### Test Files

- **`channels.test.tsx`** - Main Channels module tests (18 tests)
- **`whatsapp.test.tsx`** - WhatsApp-specific component tests (20 tests)

### Key Improvements Made

1. **Real UI Components**: Uses actual UI components instead of mocks for better integration testing
2. **Co-located Mock Data**: Test data is defined within test files for better maintainability
3. **Consolidated Coverage**: Fewer files with focused, comprehensive test coverage
4. **Simplified Mocking**: Only essential utilities are mocked, not entire component libraries

## Test Coverage Results

**Total Tests**: 38 tests ✅ **All Passing**

### Channels Module Coverage

- **Lines**: 48.17% (Channels module specific)
- **Branches**: 94.44%
- **Functions**: 66.66%
- **Statements**: 48.17%

### Components Tested

#### channels.test.tsx (18 tests)

- **ChannelsTab**: Main component with tab switching, filtering, searching
- **MainContent**: Content area with empty states and channel selection
- **ChannelListItem**: Individual channel items with selection and styling
- **Integration Tests**: Complete user workflows and state management

#### whatsapp.test.tsx (20 tests)

- **OnboardingView**: Option selection and navigation
- **WABASelection**: Number selection with styling and interactions
- **ConfigurationView**: Configuration display and clipboard functionality
- **Integration Flow**: Complete WhatsApp onboarding workflow

### User Interactions Covered

- ✅ Channel selection and deselection
- ✅ Filtering channels by type (native, text, voice)
- ✅ Searching channels by name
- ✅ Tab switching between Available and My Channels
- ✅ WhatsApp onboarding flow
- ✅ WABA number selection and configuration
- ✅ Copy to clipboard functionality
- ✅ Image error handling
- ✅ State persistence across navigation
- ✅ Complete user workflows
- ✅ Complex filtering and searching
- ✅ Error handling and recovery

## Architectural Decisions

### 1. Real UI Components vs Mocking

**Decision**: Use real UI components instead of mocks
**Rationale**:

- Better integration testing
- Catches real component interaction bugs
- Reduces maintenance overhead of keeping mocks in sync
- More confidence in test results

### 2. Consolidated Test Files

**Decision**: 2 focused test files instead of 14+ granular files
**Rationale**:

- Easier to maintain and understand
- Faster test execution (less setup/teardown)
- Better for coverage-focused testing
- Reduces file management complexity

### 3. Co-located Mock Data

**Decision**: Define test data within test files
**Rationale**:

- Self-contained tests are easier to understand
- Reduces coupling between test files
- Changes are localized and easier to track
- No shared state issues between tests

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:ui

# Run tests once
npm run test:run

# Run tests with coverage
npm run test:coverage
```

## Test Utilities

The test suite uses custom utilities located in `src/test/utils.tsx`:

- **Custom render function** - Wraps components with necessary providers
- **Mock factories** - Create test data for channels, filters, etc.
- **Component mocks** - Mock UI components for isolated testing

## Mocking Strategy

### UI Components

- Radix UI components are mocked with simple HTML elements
- Lucide React icons are mocked with test-friendly elements
- Custom UI components maintain their essential props

### External Dependencies

- `navigator.clipboard` for copy functionality
- `window.matchMedia` for responsive behavior
- `ResizeObserver` and `IntersectionObserver` for layout

### Data Sources

- Channel configuration data is mocked
- WABA numbers and onboarding options are mocked
- Filter configurations are mocked

## Test Patterns

### Component Testing

```typescript
describe('ComponentName', () => {
  const defaultProps = { /* ... */ };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly', () => {
    render(<ComponentName {...defaultProps} />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('handles user interaction', () => {
    const mockHandler = vi.fn();
    render(<ComponentName {...defaultProps} onAction={mockHandler} />);

    fireEvent.click(screen.getByText('Button'));
    expect(mockHandler).toHaveBeenCalledWith('expected-value');
  });
});
```

### Integration Testing

```typescript
describe('User Flow', () => {
  it('completes end-to-end workflow', async () => {
    render(<MainComponent />);

    // Step 1: Initial state
    expect(screen.getByText('Initial State')).toBeInTheDocument();

    // Step 2: User action
    fireEvent.click(screen.getByText('Action Button'));

    // Step 3: Wait for state change
    await waitFor(() => {
      expect(screen.getByText('New State')).toBeInTheDocument();
    });
  });
});
```

## Coverage Goals

- **Lines**: 80%+
- **Functions**: 80%+
- **Branches**: 80%+
- **Statements**: 80%+

## Best Practices

1. **Test behavior, not implementation** - Focus on what users see and do
2. **Use descriptive test names** - Clearly state what is being tested
3. **Arrange, Act, Assert** - Structure tests clearly
4. **Mock external dependencies** - Keep tests isolated and fast
5. **Test edge cases** - Handle error conditions and boundary cases
6. **Use waitFor for async operations** - Handle state changes properly
7. **Clean up after tests** - Clear mocks and reset state

## Debugging Tests

### Common Issues

- **Component not found**: Check if mocks are properly configured
- **Async operations**: Use `waitFor` for state changes
- **Event handling**: Ensure proper event simulation
- **Mock functions**: Verify mock setup and calls

### Debug Commands

```bash
# Run specific test file
npm test -- ChannelSidebar.test.tsx

# Run tests with verbose output
npm test -- --reporter=verbose

# Run tests in debug mode
npm test -- --inspect-brk
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use the provided test utilities
3. Add appropriate mocks for new dependencies
4. Ensure good coverage of new functionality
5. Update this README if adding new test patterns
