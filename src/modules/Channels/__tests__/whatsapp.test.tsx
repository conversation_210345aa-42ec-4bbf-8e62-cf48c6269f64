import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils';

// Import the main WhatsApp component and its dependencies
import WhatsappView from '../Whatsapp/index';
import OnboardingView from '../Whatsapp/OnboardingView';
import WABASelection from '../Whatsapp/WABASelection';
import ConfigurationView from '../Whatsapp/ConfigurationView';


// Mock clipboard API
const mockWriteText = vi.fn().mockResolvedValue(undefined);
Object.defineProperty(navigator, 'clipboard', {
  value: { writeText: mockWriteText },
  writable: true,
});

describe('WhatsApp Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('WhatsappView Component', () => {
    it('renders the onboarding view by default', () => {
      render(<WhatsappView />);
      
      expect(screen.getByText('NGAGE')).toBeInTheDocument();
      expect(screen.getByText('Meta Cloud API')).toBeInTheDocument();
    });

    it('navigates to WABA selection when NGAGE option is selected', async () => {
      render(<WhatsappView />);
      
      // Click on NGAGE option
      const ngageButtons = screen.getAllByText('GET STARTED');
      const ngageButton = ngageButtons[0]; // First button is for NGAGE
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
        expect(screen.getByText('+91 9642456783')).toBeInTheDocument();
      });
    });

    it('does not navigate when Meta option is selected', () => {
      render(<WhatsappView />);
      
      // Click on Meta option
      const metaButtons = screen.getAllByText('GET STARTED');
      const metaButton = metaButtons[1]; // Second button is for Meta
      fireEvent.click(metaButton);

      // Should still be on onboarding view
      expect(screen.getByText('NGAGE')).toBeInTheDocument();
      expect(screen.getByText('Meta Cloud API')).toBeInTheDocument();
    });

    it('navigates through the complete flow: onboarding -> WABA selection -> configuration', async () => {
      render(<WhatsappView />);

      // 1. Start with onboarding view
      expect(screen.getByText('NGAGE')).toBeInTheDocument();

      // 2. Click NGAGE to go to WABA selection
      const ngageButton = screen.getAllByText('GET STARTED')[0];
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
      });

      // 3. Select a WABA number
      const firstWabaCard = screen.getByText('+91 9642456783').closest('div');
      if (firstWabaCard) {
        fireEvent.click(firstWabaCard);
      }

      // 4. Click Connect to go to configuration
      const connectButton = screen.getByText('CONNECT');
      fireEvent.click(connectButton);

      await waitFor(() => {
        expect(screen.getByText('CHANGE NUMBER')).toBeInTheDocument();
        expect(screen.getByText('+91 9642456783')).toBeInTheDocument();
        expect(screen.getByText('https://ngage.cpaas.com/v1/bot/wellness_care')).toBeInTheDocument();
      });
    });

    it('handles cancel flow from WABA selection back to onboarding', async () => {
      render(<WhatsappView />);

      // Navigate to WABA selection
      const ngageButton = screen.getAllByText('GET STARTED')[0];
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
      });

      // Click Cancel to go back to onboarding
      const cancelButton = screen.getByText('CANCEL');
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.getByText('NGAGE')).toBeInTheDocument();
        expect(screen.getByText('Meta Cloud API')).toBeInTheDocument();
      });
    });

    it('handles change number flow from configuration back to WABA selection', async () => {
      render(<WhatsappView />);

      // Navigate through to configuration
      const ngageButton = screen.getAllByText('GET STARTED')[0];
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
      });

      const firstWabaCard = screen.getByText('+91 9642456783').closest('div');
      if (firstWabaCard) {
        fireEvent.click(firstWabaCard);
      }

      const connectButton = screen.getByText('CONNECT');
      fireEvent.click(connectButton);

      await waitFor(() => {
        expect(screen.getByText('CHANGE NUMBER')).toBeInTheDocument();
      });

      // Click Change Number to go back to WABA selection
      const changeNumberButton = screen.getByText('CHANGE NUMBER');
      fireEvent.click(changeNumberButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
        expect(screen.getByText('CANCEL')).toBeInTheDocument();
      });
    });

    it('maintains selected WABA number state correctly', async () => {
      render(<WhatsappView />);

      // Navigate to WABA selection
      const ngageButton = screen.getAllByText('GET STARTED')[0];
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
      });

      // Select second WABA number
      const secondWabaCard = screen.getByText('+91 8456822104').closest('div');
      if (secondWabaCard) {
        fireEvent.click(secondWabaCard);
      }

      // Connect to configuration
      const connectButton = screen.getByText('CONNECT');
      fireEvent.click(connectButton);

      await waitFor(() => {
        expect(screen.getByText('+91 8456822104')).toBeInTheDocument();
      });
    });

    it('resets selected number when changing number', async () => {
      render(<WhatsappView />);

      // Navigate through to configuration with a selected number
      const ngageButton = screen.getAllByText('GET STARTED')[0];
      fireEvent.click(ngageButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
      });

      const firstWabaCard = screen.getByText('+91 9642456783').closest('div');
      if (firstWabaCard) {
        fireEvent.click(firstWabaCard);
      }

      const connectButton = screen.getByText('CONNECT');
      fireEvent.click(connectButton);

      await waitFor(() => {
        expect(screen.getByText('CHANGE NUMBER')).toBeInTheDocument();
      });

      // Change number should reset selection
      const changeNumberButton = screen.getByText('CHANGE NUMBER');
      fireEvent.click(changeNumberButton);

      await waitFor(() => {
        expect(screen.getByText('Select a WABA Number to connect')).toBeInTheDocument();
        // Verify no number is selected by checking that Connect button behavior
        const newConnectButton = screen.getByText('CONNECT');
        expect(newConnectButton).toBeInTheDocument();
      });
    });
  });

  describe('Individual Component Tests', () => {
    // Keep some focused unit tests for individual components
    describe('OnboardingView', () => {
      const mockOptions = [
        {
          id: 'ngage' as const,
          title: 'NGAGE',
          descriptionKey: 'whatsapp.onboarding.ngage.description',
          icon: '/test-icon.svg',
          buttonTextKey: 'common.getStarted',
        },
      ];

      it('renders onboarding options correctly', () => {
        const mockOnOptionSelect = vi.fn();
        render(<OnboardingView options={mockOptions} onOptionSelect={mockOnOptionSelect} />);

        expect(screen.getByText('NGAGE')).toBeInTheDocument();
        expect(screen.getByText('Onboard WABA using NGAGE WhatsApp channel and integrate it with your Chatbot.')).toBeInTheDocument();
      });

      it('calls onOptionSelect when option is clicked', () => {
        const mockOnOptionSelect = vi.fn();
        render(<OnboardingView options={mockOptions} onOptionSelect={mockOnOptionSelect} />);

        const button = screen.getByText('GET STARTED');
        fireEvent.click(button);

        expect(mockOnOptionSelect).toHaveBeenCalledWith('ngage');
      });
    });

    describe('WABASelection', () => {
      const mockWabaNumbers = [
        { id: '1', number: '+91 9642456783' },
        { id: '2', number: '+91 8456822104' },
      ];

      it('renders WABA numbers correctly', () => {
        const mockHandlers = {
          onNumberSelect: vi.fn(),
          onCancel: vi.fn(),
          onConnect: vi.fn(),
        };

        render(
          <WABASelection
            wabaNumbers={mockWabaNumbers}
            selectedNumber={null}
            {...mockHandlers}
          />
        );

        expect(screen.getByText('+91 9642456783')).toBeInTheDocument();
        expect(screen.getByText('+91 8456822104')).toBeInTheDocument();
      });

      it('calls appropriate handlers when buttons are clicked', () => {
        const mockHandlers = {
          onNumberSelect: vi.fn(),
          onCancel: vi.fn(),
          onConnect: vi.fn(),
        };

        render(
          <WABASelection
            wabaNumbers={mockWabaNumbers}
            selectedNumber="1"
            {...mockHandlers}
          />
        );

        fireEvent.click(screen.getByText('CANCEL'));
        expect(mockHandlers.onCancel).toHaveBeenCalled();

        fireEvent.click(screen.getByText('CONNECT'));
        expect(mockHandlers.onConnect).toHaveBeenCalled();
      });
    });

    describe('ConfigurationView', () => {
      it('renders configuration details correctly', () => {
        const mockHandlers = {
          onChangeNumber: vi.fn(),
          onSwitch: vi.fn(),
        };

        render(
          <ConfigurationView
            selectedWABANumber="+91 9642456783"
            webhookUrl="https://ngage.cpaas.com/v1/bot/wellness_care"
            {...mockHandlers}
          />
        );

        expect(screen.getByText('+91 9642456783')).toBeInTheDocument();
        expect(screen.getByText('https://ngage.cpaas.com/v1/bot/wellness_care')).toBeInTheDocument();
      });

      it('calls onChangeNumber when change number button is clicked', () => {
        const mockHandlers = {
          onChangeNumber: vi.fn(),
          onSwitch: vi.fn(),
        };

        render(
          <ConfigurationView
            selectedWABANumber="+91 9642456783"
            webhookUrl="https://ngage.cpaas.com/v1/bot/wellness_care"
            {...mockHandlers}
          />
        );

        fireEvent.click(screen.getByText('CHANGE NUMBER'));
        expect(mockHandlers.onChangeNumber).toHaveBeenCalled();
      });

      it('copies content to clipboard when copy buttons are clicked', async () => {
        const mockHandlers = {
          onChangeNumber: vi.fn(),
          onSwitch: vi.fn(),
        };

        render(
          <ConfigurationView
            selectedWABANumber="+91 9642456783"
            webhookUrl="https://ngage.cpaas.com/v1/bot/wellness_care"
            {...mockHandlers}
          />
        );

        // Find copy buttons and test clipboard functionality
        const wabaText = screen.getByText('+91 9642456783');
        const copyButton = wabaText.parentElement?.querySelector('button');

        if (copyButton) {
          fireEvent.click(copyButton);
          expect(mockWriteText).toHaveBeenCalledWith('+91 9642456783');
        }
      });
    });
  });
});
