import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@/test/utils';

// Import actual components (no mocking)
import ChannelsTab from '../index';
import MainContent from '../MainContent';
import ChannelListItem from '../Sidebar/ChannelListItem';

// Mock the config module with test data (must be hoisted)
vi.mock('../config', () => ({
  availableChannels: [
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      type: 'text',
      status: 'available',
      icon: '/whatsapp-icon.svg',
      isVerified: true,
      description: 'Connect with customers on WhatsApp',
    },
    {
      id: 'telegram',
      name: 'Telegram',
      type: 'text',
      status: 'available',
      icon: '/telegram-icon.svg',
      description: 'Reach customers on Telegram',
    },
    {
      id: 'webhook',
      name: 'Webhook',
      type: 'native',
      status: 'available',
      icon: '/webhook-icon.svg',
      description: 'Custom webhook integration',
    },
    {
      id: 'alexa',
      name: '<PERSON><PERSON>',
      type: 'voice',
      status: 'available',
      icon: '/alexa-icon.svg',
      description: 'Voice interactions with <PERSON>a',
    },
  ],
  myChannels: [
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      type: 'text',
      status: 'connected',
      icon: '/whatsapp-icon.svg',
      phoneNumber: '+91 9642456783',
      webhookUrl: 'https://ngage.cpaas.com/v1/bot/wellness_care',
      isVerified: true,
    },
  ],
  filters: [
    { id: 'all', labelKey: 'channels.filters.all', type: 'all' },
    { id: 'native', labelKey: 'channels.filters.native', type: 'native' },
    { id: 'text', labelKey: 'channels.filters.text', type: 'text' },
    { id: 'voice', labelKey: 'channels.filters.voice', type: 'voice' },
  ],
  mainChannelTab: [
    { id: 'available', labelKey: 'channels.tabs.available' },
    { id: 'my-channels', labelKey: 'channels.tabs.myChannels' },
  ],
}));

// Mock clipboard API for copy functionality
const mockWriteText = vi.fn().mockResolvedValue(undefined);
Object.defineProperty(navigator, 'clipboard', {
  value: { writeText: mockWriteText },
  writable: true,
});

// Test data factories
const createMockChannel = (overrides = {}) => ({
  id: 'test-channel',
  name: 'Test Channel',
  type: 'text' as const,
  status: 'available' as const,
  icon: '/test-icon.svg',
  isVerified: false,
  ...overrides,
});

describe('Channels Module', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ChannelsTab - Main Component', () => {
    it('renders the main channels interface', () => {
      render(<ChannelsTab />);

      expect(screen.getByText('Available')).toBeInTheDocument();
      expect(screen.getByText('My Channels')).toBeInTheDocument();
    });

    it('displays available channels by default', () => {
      render(<ChannelsTab />);

      expect(screen.getByText('WhatsApp')).toBeInTheDocument();
      expect(screen.getByText('Telegram')).toBeInTheDocument();
      expect(screen.getByText('Webhook')).toBeInTheDocument();
      expect(screen.getByText('Alexa')).toBeInTheDocument();
    });

    it('switches between Available and My Channels tabs', async () => {
      render(<ChannelsTab />);

      // Initially shows available channels
      expect(screen.getByText('Telegram')).toBeInTheDocument();

      // Click on My Channels tab
      const myChannelsTab = screen.getByText('My Channels');
      fireEvent.click(myChannelsTab);

      await waitFor(() => {
        // Should show connected channels only
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        // Should not show Telegram (not in my channels)
        expect(screen.queryByText('Telegram')).not.toBeInTheDocument();
      });
    });

    it('handles search functionality', async () => {
      render(<ChannelsTab />);

      const searchInput = screen.getByPlaceholderText('Search...');
      fireEvent.change(searchInput, { target: { value: 'WhatsApp' } });

      await waitFor(() => {
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.queryByText('Telegram')).not.toBeInTheDocument();
      });
    });

    it('handles filter functionality', async () => {
      render(<ChannelsTab />);

      // Click on Text filter
      const textFilter = screen.getByText('Text');
      fireEvent.click(textFilter);

      await waitFor(() => {
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.getByText('Telegram')).toBeInTheDocument();
        expect(screen.queryByText('Webhook')).not.toBeInTheDocument();
      });
    });

    it('combines search and filter functionality', async () => {
      render(<ChannelsTab />);

      // Apply text filter
      const textFilter = screen.getByText('Text');
      fireEvent.click(textFilter);

      // Search for WhatsApp
      const searchInput = screen.getByPlaceholderText('Search...');
      fireEvent.change(searchInput, { target: { value: 'WhatsApp' } });

      await waitFor(() => {
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.queryByText('Telegram')).not.toBeInTheDocument();
        expect(screen.queryByText('Webhook')).not.toBeInTheDocument();
      });
    });

    it('shows empty state when no channel is selected', () => {
      render(<ChannelsTab />);

      expect(screen.getByText('Select channels to configure')).toBeInTheDocument();
      expect(screen.getByText('Nothing is selected')).toBeInTheDocument();
    });
  });

  describe('MainContent Component', () => {
    it('renders empty state when no channel is selected', () => {
      render(<MainContent selectedChannel={null} />);

      expect(screen.getByText('Select channels to configure')).toBeInTheDocument();
      expect(screen.getByText('Nothing is selected')).toBeInTheDocument();
    });

    it('renders WhatsApp view when WhatsApp channel is selected', () => {
      render(<MainContent selectedChannel="whatsapp" />);

      // Should not show empty state
      expect(screen.queryByText('Select channels to configure')).not.toBeInTheDocument();
    });

    it('renders empty state for unsupported channel types', () => {
      render(<MainContent selectedChannel="telegram" />);

      // The actual empty state shows different text
      expect(screen.getByText('Nothing here yet')).toBeInTheDocument();
      expect(screen.getByText('There is currently no content to display.')).toBeInTheDocument();
    });
  });

  describe('ChannelListItem Component', () => {
    const mockOnSelect = vi.fn();

    beforeEach(() => {
      mockOnSelect.mockClear();
    });

    it('renders channel information correctly', () => {
      const channel = createMockChannel({
        id: 'whatsapp',
        name: 'WhatsApp',
        type: 'text',
        isVerified: true,
      });

      render(<ChannelListItem channel={channel} isSelected={false} onSelect={mockOnSelect} />);

      expect(screen.getByText('WhatsApp')).toBeInTheDocument();
      expect(screen.getByText('text')).toBeInTheDocument();
      expect(screen.getByAltText('WhatsApp')).toBeInTheDocument();
    });

    it('shows verified icon for verified channels', () => {
      const channel = createMockChannel({ isVerified: true });

      render(<ChannelListItem channel={channel} isSelected={false} onSelect={mockOnSelect} />);

      // Check for CheckCircle2 icon (it should be in the DOM)
      const verifiedIcon = screen.getByRole('button').querySelector('svg');
      expect(verifiedIcon).toBeInTheDocument();
    });

    it('applies selected styling when selected', () => {
      const channel = createMockChannel();

      render(<ChannelListItem channel={channel} isSelected={true} onSelect={mockOnSelect} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('!bg-primary-50');
    });

    it('calls onSelect when clicked', () => {
      const channel = createMockChannel({ id: 'whatsapp' });

      render(<ChannelListItem channel={channel} isSelected={false} onSelect={mockOnSelect} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnSelect).toHaveBeenCalledWith('whatsapp');
    });

    it('handles image load error gracefully', () => {
      const channel = createMockChannel({ icon: '/invalid-icon.svg' });

      render(<ChannelListItem channel={channel} isSelected={false} onSelect={mockOnSelect} />);

      const image = screen.getByAltText('Test Channel');
      fireEvent.error(image);

      expect(image.style.display).toBe('none');
    });
  });

  describe('Integration Tests', () => {
    it('allows complete user workflow: browse, filter, search, and select', async () => {
      render(<ChannelsTab />);

      // 1. Initial state - should show available channels
      expect(screen.getByText('WhatsApp')).toBeInTheDocument();
      expect(screen.getByText('Telegram')).toBeInTheDocument();

      // 2. Test filtering by type
      const textFilter = screen.getByText('Text');
      fireEvent.click(textFilter);

      await waitFor(() => {
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.getByText('Telegram')).toBeInTheDocument();
        expect(screen.queryByText('Webhook')).not.toBeInTheDocument();
      });

      // 3. Test search functionality
      const searchInput = screen.getByPlaceholderText('Search...');
      fireEvent.change(searchInput, { target: { value: 'WhatsApp' } });

      await waitFor(() => {
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
        expect(screen.queryByText('Telegram')).not.toBeInTheDocument();
      });

      // 4. Clear search and test voice filter
      fireEvent.change(searchInput, { target: { value: '' } });
      const voiceFilter = screen.getByText('Voice');
      fireEvent.click(voiceFilter);

      await waitFor(() => {
        expect(screen.getByText('Alexa')).toBeInTheDocument();
        expect(screen.queryByText('WhatsApp')).not.toBeInTheDocument();
      });
    });

    it('maintains search and filter state across tab switches', async () => {
      render(<ChannelsTab />);

      // Apply text filter and search
      const textFilter = screen.getByText('Text');
      fireEvent.click(textFilter);

      const searchInput = screen.getByPlaceholderText('Search...');
      fireEvent.change(searchInput, { target: { value: 'WhatsApp' } });

      // Switch to My Channels
      const myChannelsTab = screen.getByText('My Channels');
      fireEvent.click(myChannelsTab);

      // Filter and search should still be applied
      await waitFor(() => {
        expect(searchInput).toHaveValue('WhatsApp');
        expect(screen.getByText('WhatsApp')).toBeInTheDocument();
      });
    });

    it('handles empty search results gracefully', async () => {
      render(<ChannelsTab />);

      const searchInput = screen.getByPlaceholderText('Search...');
      fireEvent.change(searchInput, { target: { value: 'NonExistentChannel' } });

      await waitFor(() => {
        expect(screen.queryByText('WhatsApp')).not.toBeInTheDocument();
        expect(screen.queryByText('Telegram')).not.toBeInTheDocument();
      });

      // Should still show empty state in main content
      expect(screen.getByText('Select channels to configure')).toBeInTheDocument();
    });
  });
});
