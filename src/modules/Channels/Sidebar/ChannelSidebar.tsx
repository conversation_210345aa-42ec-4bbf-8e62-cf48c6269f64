import React from 'react';
import { Channel, ChannelFilter } from '../types';
import ChannelSearch from './ChannelSearch';
import ChannelFilterComponent from './ChannelFilter';
import ChannelListItem from './ChannelListItem';
import { ChannelFilterType, ChannelId } from '../enums';

interface ChannelSidebarProps {
  channels: Channel[];
  selectedChannel: string | null;
  searchQuery: string;
  activeFilter: string;
  filters: ChannelFilter[];
  onChannelSelect: (channelId: ChannelId) => void;
  onSearchChange: (query: string) => void;
  onFilterChange: (filterId: string) => void;
}

const ChannelSidebar: React.FC<ChannelSidebarProps> = ({
  channels,
  selectedChannel,
  searchQuery,
  activeFilter,
  filters,
  onChannelSelect,
  onSearchChange,
  onFilterChange,
}) => {
  const filteredChannels = channels.filter(channel => {
    const matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = activeFilter === ChannelFilterType.ALL || channel.type === activeFilter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="border-tertiary-200 flex flex-col h-0 flex-1">
      {/* Search */}
      <ChannelSearch searchQuery={searchQuery} onSearchChange={onSearchChange} />

      {/* Filters */}
      <ChannelFilterComponent
        activeFilter={activeFilter}
        filters={filters}
        onFilterChange={onFilterChange}
      />

      {/* Channel List */}
      <div className="flex-1 overflow-y-auto">
        {filteredChannels.map(channel => (
          <ChannelListItem
            key={channel.id}
            channel={channel}
            isSelected={selectedChannel === channel.id}
            onSelect={onChannelSelect}
          />
        ))}
      </div>
    </div>
  );
};

export default ChannelSidebar;
