import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChannelFilter } from '../types';
import { useTranslation } from 'react-i18next';

interface IProps {
  filters: ChannelFilter[];
  activeFilter: string;
  onFilterChange: (filterId: string) => void;
}

function ChannelFilterComponent({ filters, activeFilter, onFilterChange }: IProps) {
  const { t } = useTranslation();
  
  return (
    <div className="px-4 py-3">
      <div className="flex gap-1.5 flex-wrap">
        {filters.map(filter => (
          <Button
            key={filter.id}
            variant="ghost"
            size="sm"
            onClick={() => onFilterChange(filter.id)}
            className={cn(
              'h-7 px-3 text-xs font-medium rounded-full border transition-all duration-200',
              activeFilter === filter.id
                ? '!bg-primary-600 text-white border-primary-600 hover:bg-primary-700 shadow-sm'
                : 'bg-background text-tertiary-600 border-tertiary-200 hover:bg-tertiary-50 hover:border-gray-300'
            )}
          >
            {t(filter.labelKey)}
          </Button>
        ))}
      </div>
    </div>
  );
}

export default ChannelFilterComponent;
