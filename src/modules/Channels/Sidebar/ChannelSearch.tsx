import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface IProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const ChannelSearch = ({ onSearchChange, searchQuery }: IProps) => {
  const { t } = useTranslation();
  return (
    <div className="border-b border-tertiary-300 border-background">
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-500 w-4 h-4" />
        <Input
          placeholder={`${t('common.search')}...`}
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          className="pl-5 !py-6 focus-visible:ring-0 focus-visible:ring-offset-0 text-sm border-0 rounded-none placeholder:text-secondary-400"
        />
      </div>
    </div>
  );
};

export default ChannelSearch;
