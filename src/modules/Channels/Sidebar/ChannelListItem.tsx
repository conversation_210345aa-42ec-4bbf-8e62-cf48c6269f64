import React from 'react';
import { cn } from '@/lib/utils';
import { Channel } from '../types';
import { CheckCircle2 } from 'lucide-react';
import { ChannelId } from '../enums';

interface ChannelListItemProps {
  channel: Channel;
  isSelected: boolean;
  onSelect: (channelId: ChannelId) => void;
}

const ChannelListItem: React.FC<ChannelListItemProps> = ({ channel, isSelected, onSelect }) => {
  return (
    <button
      onClick={() => onSelect(channel.id)}
      className={cn(
        'w-full flex items-center gap-3 p-4 border-b border-tertiary-300 hover:bg-secondary-50 transition-colors text-left',
        isSelected && '!bg-primary-50'
      )}
    >
      <div className="relative">
        <div className="h-12 w-12 rounded-lg flex items-center justify-center shadow-sm">
          <img
            src={channel.icon}
            alt={channel.name}
            onError={e => {
              e.currentTarget.style.display = 'none';
            }}
          />
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-sm truncate">{channel.name}</h3>
          {channel.isVerified && (
            <CheckCircle2 className="w-5 h-5 fill-primary text-background flex-shrink-0" />
          )}
        </div>
        <p className="text-xs text-tertiary-500 capitalize mt-0.5">{channel.type}</p>
      </div>
    </button>
  );
};

export default ChannelListItem;
