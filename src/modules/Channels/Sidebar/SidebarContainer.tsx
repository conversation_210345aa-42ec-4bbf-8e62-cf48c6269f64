import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import ChannelSidebar from './ChannelSidebar';
import { ChannelId, ChannelMainTab } from '../enums';
import { Channel, ChannelFilter } from '../types';
import { cn } from '@/lib/utils';

interface SidebarContainerProps {
  activeTab: string;
  channels: Channel[];
  selectedChannel: string | null;
  searchQuery: string;
  activeFilter: string;
  filters: ChannelFilter[];
  onChannelSelect: (channelId: ChannelId) => void;
  onSearchChange: (query: string) => void;
  onFilterChange: (filterId: string) => void;
}

const SidebarContainer: React.FC<SidebarContainerProps> = ({
  activeTab,
  channels,
  selectedChannel,
  searchQuery,
  activeFilter,
  filters,
  onChannelSelect,
  onSearchChange,
  onFilterChange,
}) => {
  return (
    <>
      {Object.values(ChannelMainTab).map(tab => (
        <TabsContent
          key={tab}
          value={tab}
          className={cn('h-0 flex flex-col mt-0', {
            'flex-1': activeTab === tab,
          })}
        >
          <ChannelSidebar
            channels={channels}
            selectedChannel={selectedChannel}
            searchQuery={searchQuery}
            activeFilter={activeFilter}
            filters={filters}
            onChannelSelect={onChannelSelect}
            onSearchChange={onSearchChange}
            onFilterChange={onFilterChange}
          />
        </TabsContent>
      ))}
    </>
  );
};

export default SidebarContainer;
