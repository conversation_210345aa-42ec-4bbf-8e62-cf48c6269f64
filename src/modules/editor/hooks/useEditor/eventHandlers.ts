import { dia, ui } from 'rappid';
import { EventHandlerContext } from '../../types';
import { jointJsEditorTheme as theme } from '../../utils/constants';
import { customLinkTools, getElementTools } from '../../utils/tools';

export const eventHandlers = {
  setupInitialPaperEvents: (context: Pick<EventHandlerContext, 'paper' | 'scroller'>) => {
    const { paper, scroller } = context;
    const { startPanning } = scroller!;

    const handleElementPointerDown = (elementView: dia.ElementView, evt: dia.Event) => {
      evt.data = { startPosition: elementView.model.position() };
    };

    const handleLinkMouseEnter = (linkView: dia.LinkView) => {
      const targetView = (linkView as any).targetView;
      const isInteractive = paper.options?.interactive;

      if (targetView?.model?.attributes?.type !== 'choiceOption' && isInteractive) {
        linkView.addTools(customLinkTools);
      }
    };

    paper.on('blank:pointerdown', startPanning);
    paper.on('element:pointerdown', handleElementPointerDown);
    paper.on('link:mouseenter', handleLinkMouseEnter);

    // Cleanup function
    return () => {
      paper.off('blank:pointerdown', startPanning);
      paper.off('element:pointerdown', handleElementPointerDown);
      paper.off('link:mouseenter', handleLinkMouseEnter);
    };
  },

  handleElementClick:
    ({ setCurrentElementView, setModalTypeDetails, paper }: EventHandlerContext) =>
    (elementView: dia.ElementView) => {
      setCurrentElementView(elementView);
      paper.removeTools();

      elementView.model.attr('rect/stroke-width', 2);
      elementView.model.attr('rect/stroke', theme.primaryColor);

      const {
        model: {
          id,
          attributes: { type },
        },
        el,
      } = elementView;
      const { x: elX, y: elY } = el.getBoundingClientRect();

      setModalTypeDetails(null);
      setTimeout(() => {
        const isFullscreen = window.innerHeight === window.screen.height;
        let left = isFullscreen ? elX + 100 : elX - 100;
        const modalWidth = 700;
        if (left + modalWidth > window.screen.width) {
          left = elX - 500;
        }

        let top = 65;
        const modalHeight = 400;
        const modalTopPosition = elY + modalHeight;
        if (isFullscreen) {
          if (modalTopPosition > window.screen.height) {
            top = elY - modalHeight;
          } else {
            top = elY + 60;
          }
        }

        setModalTypeDetails({ id: id as string, type, top, left });
      }, 0);
    },

  handleBlankMouseOver:
    ({ hoverElement, paper }: EventHandlerContext) =>
    () => {
      if (!hoverElement) paper.removeTools();
    },

  handleElementMouseEnter:
    ({ isEdit, modalTypeDetails, graph, setHoverElement }: EventHandlerContext) =>
    (elementView: dia.ElementView) => {
      const { x, y } = elementView.el.getBoundingClientRect();
      if (isEdit && !modalTypeDetails) {
        const {
          el,
          model: {
            attributes: { type },
          },
        } = elementView;
        const successors = graph.getSuccessors(elementView.model);
        setHoverElement(successors.length === 0 ? { x, y } : null);

        const { width } = el.getBoundingClientRect();
        const tools = getElementTools(type, width, successors);
        elementView.addTools(tools);
      }
    },

  handleElementPointerUp:
    ({ paper, removeLastUndoState }: EventHandlerContext) =>
    (elementView: dia.ElementView, evt: dia.Event) => {
      const { model: element } = elementView;
      const { model: graph } = paper;
      const elementsUnder = graph.findModelsInArea(element.getBBox()).filter(el => el !== element);

      if (elementsUnder.length > 0) {
        const { x, y } = evt.data.startPosition;
        element.position(x, y);
        setTimeout(() => removeLastUndoState(), 500);
      }
    },

  handleLinkConnect:
    ({ paper, graph, changeJointJsonToLeapOnDrop }: EventHandlerContext) =>
    (linkView: dia.LinkView) => {
      paper.removeTools();
      const { sourceView } = linkView as any;
      if (sourceView.model.attributes.type === 'choice') {
        changeJointJsonToLeapOnDrop(graph);
      }
    },

  handleGraphAdd:
    ({ graph, changeJointJsonToLeapOnDrop }: EventHandlerContext) =>
    (element: dia.Element, _collection: any, opt: dia.CollectionAddOptions) => {
      const isPortEmpty = !element.attributes?.ports?.items?.length;
      if (isPortEmpty && (opt.stencil || element.attributes.type !== 'standard.Link')) {
        if (element.attributes.type !== 'appEnd') {
          if (element.attributes.type === 'choice') {
            element.addPorts([
              {
                group: 'out',
                args: { x: 12 },
                attrs: { portBody: { fill: theme.successColor, stroke: theme.successColor } },
              },
              {
                group: 'out',
                args: { x: 40 },
                attrs: { portBody: { fill: theme.errorColor, stroke: theme.errorColor } },
              },
              { group: 'in' },
            ]);
          } else {
            element.addPorts([{ group: 'out' }, { group: 'in' }]);
          }
        } else {
          element.addPorts([{ group: 'in' }]);
        }
        changeJointJsonToLeapOnDrop(graph);
      }
    },

  handleGraphChange:
    ({ autoUpdateHandler }: EventHandlerContext) =>
    (cell: dia.Cell, action: any) => {
      if (action?.propertyPath?.includes('attrs')) return;
      autoUpdateHandler(null, { action: 'update', nodeId: cell.id as string });
    },

  handleGraphRemove:
    ({ autoUpdateHandler }: EventHandlerContext) =>
    (cell: dia.Cell) => {
      autoUpdateHandler(null, { action: 'remove', nodeId: cell.id as string });
    },
};
