import { dia, shapes, ui, connectionStrategies } from 'rappid';
import { jointJsEditorTheme as theme } from '../../utils/constants';
import { nodeList } from '../../utils/nodeList';

export const createDefaultLink = () =>
  new shapes.standard.Link({
    attrs: { line: { strokeWidth: 1, class: 'animate-link stroke-primary' } },
    manhattan: true,
    router: { name: 'manhattan' },
    connector: { name: 'rounded' },
    z: 0,
  });

export const handleConnectionStrategy: connectionStrategies.ConnectionStrategy = (
  end,
  endView,
  _a,
  _b,
  _c,
  d
) => {
  const ports = (endView.model as any).getPorts();
  const inPort = ports.find((d: any) => d.group === 'in');
  if (d === 'target') {
    return { id: end.id, port: inPort.id };
  }
  return end;
};

export const handleValidateMagnet =
  (graph: dia.Graph | null) => (cellView: dia.CellView, magnet: SVGElement) => {
    const {
      attributes: { type },
    } = cellView.model;
    if (magnet.getAttribute('port-group') === 'in') return false;

    if (type === 'choice') {
      const fillColor = magnet.getAttribute('fill');
      const portId = magnet.getAttribute('port');
      if (fillColor === theme.errorColor) {
        const links = graph?.getConnectedLinks(cellView.model, { outbound: true });
        return !links?.some(link => link.attributes.source.port === portId);
      }
      return false;
    }

    const links = graph?.getConnectedLinks(cellView.model, { outbound: true });
    return !links?.length;
  };

export const handleValidateConnection =
  () => (cellViewS: dia.CellView, _magnetS: SVGElement, cellViewT: dia.CellView) => {
    const sourceNode = cellViewS.model.attributes.type;
    const sourceNodeId = cellViewS.model.id;
    const targetNode = cellViewT.model.attributes.type;
    const targetNodeId = cellViewT.model.id;

    if (
      sourceNode === 'standard.Link' ||
      targetNode === 'standard.Link' ||
      targetNode === 'choiceOption'
    ) {
      return false;
    }

    if (targetNode === 'appStart' || sourceNodeId === targetNodeId) {
      return false;
    }

    const validatorMapping: Record<string, string[]> = {
      appStart: nodeList.filter(k => k !== 'appStart' && k !== 'appEnd'),
      appEnd: [],
    };

    const validNodes = validatorMapping[sourceNode];
    return validNodes?.length ? validNodes.includes(targetNode) : true;
  };

export const getContentOptions = (paperScroller: ui.PaperScroller): any /** TODO: fix this */ => {
  const visibleArea = paperScroller.getVisibleArea();
  return {
    padding: {
      bottom: visibleArea.height / 2,
      top: visibleArea.height / 2,
      left: visibleArea.width / 2,
      right: visibleArea.width / 2,
    },
    scrollWhileDragging: true,
    allowNewOrigin: 'any',
  };
};
