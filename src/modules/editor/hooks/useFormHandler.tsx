import { useCallback } from 'react';
import { dia, ui } from 'rappid';
import { ModalTypeDetails, SettingDetails } from '../types';
import { jointJsEditorTheme as theme } from '../utils/constants';
import { getFormSchema } from '../PluginForms/utils/schema';
import { validateFieldFunction } from '../PluginForms/utils/useValidation';

interface UseFormHandlerProps {
  graphInstance: dia.Graph | null;
  settingDetails: SettingDetails;
  scrollInstance: ui.PaperScroller | null;
  currentElementView: dia.ElementView | null;
  setModalTypeDetails: React.Dispatch<React.SetStateAction<ModalTypeDetails | null>>;
  setCurrentElementView: React.Dispatch<React.SetStateAction<dia.ElementView | null>>;
}

export const useFormHandler = ({
  graphInstance,
  settingDetails,
  scrollInstance,
  currentElementView,
  setModalTypeDetails,
  setCurrentElementView,
}: UseFormHandlerProps) => {
  const validateSettingModal = useCallback(async () => {
    const errors: Array<{ type: string; errorDetails: unknown }> = [];
    let errorNode: dia.Cell | null = null;

    await Promise.all(
      Object.keys(settingDetails).map(async key => {
        const cell = graphInstance?.getCell(key);
        if (!cell) return;

        cell.attr('rect/stroke', theme.primaryColor);
        cell.attr('rect/filter', 'drop-shadow(0px 0px 10px #53535333)');

        const {
          attributes: { type },
        } = cell;
        const { schema } = getFormSchema(type);

        try {
          await validateFieldFunction(settingDetails[key], schema);
        } catch (error) {
          cell.attr('rect/stroke', theme.errorColor);
          cell.attr('rect/filter', 'drop-shadow(0px 0px 10px #BF101733)');
          if (!errorNode) errorNode = cell;
          errors.push({ type, errorDetails: error });
        }
      })
    );

    if (errorNode && errors.length) {
      const position = (errorNode as dia.Cell).attributes.position;
      scrollInstance?.scroll(position.x, position.y + 400, { animation: { duration: 100 } });
    }

    return errors;
  }, [graphInstance, settingDetails, scrollInstance]);

  const handleFormClose = useCallback(
    async (isValidationNotNeeded?: boolean) => {
      if (currentElementView) {
        currentElementView.model.attr('rect/stroke-width', 1);
        currentElementView.model.attr('rect/stroke', theme.primaryColor);
        currentElementView.model.attr('rect/filter', 'drop-shadow(0px 0px 10px #53535333)');
      }
      setModalTypeDetails(null);
      setCurrentElementView(null);

      if (!isValidationNotNeeded) {
        await validateSettingModal();
      }
    },
    [currentElementView, setModalTypeDetails, setCurrentElementView, validateSettingModal]
  );

  return {
    validateSettingModal,
    handleFormClose,
  };
};
