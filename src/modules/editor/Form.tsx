import React, { lazy, Suspense } from 'react';
import Default from './PluginForms/Default';
import BaseModal from './PluginForms/BaseModal';
import { StencilNodes, StencilNodesType } from './utils/constants';
import { useTranslation } from 'react-i18next';

interface ModalTypeDetails {
  top: number;
  left: number;
}

interface FormProps {
  type: StencilNodesType;
  id: string;
  handleClose: () => void;
  handleSave: () => void;
  moduleData: any;
  modalTypeDetails: ModalTypeDetails;
  startUrl?: string;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
}

// Dynamically load component based on type
const loadModuleComponent = (moduleName: StencilNodesType) => {
  return lazy(() => import(`./PluginForms/${moduleName}`));
};

const nodesArr = Object.values(StencilNodes);

const Form: React.FC<FormProps> = ({
  type,
  id,
  handleClose,
  handleSave,
  moduleData,
  modalTypeDetails,
  startUrl,
  isEdit,
  isPublishedEnabled,
}) => {
  const { t } = useTranslation();
  const { top, left } = modalTypeDetails;

  if (!nodesArr.includes(type)) {
    return (
      <Default
        handleSave={handleSave}
        handleClose={handleClose}
        moduleData={moduleData}
        id={id}
        isEdit={isEdit}
        position={{ top, left }}
      />
    );
  }

  const ModuleComponent = loadModuleComponent(type);
  return (
    <BaseModal
      handleSave={handleSave}
      startUrl={startUrl}
      handleClose={handleClose}
      moduleData={moduleData}
      isEdit={isEdit}
      id={id}
      position={{ top, left }}
      type={type}
      isPublishedEnabled={isPublishedEnabled}
    >
      <Suspense fallback={<div>{t('form.loadingForm')}</div>}>
        <ModuleComponent />
      </Suspense>
    </BaseModal>
  );
};

export default Form;
