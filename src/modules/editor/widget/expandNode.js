import React, { useState } from 'react';
import { Close } from '@mui/icons-material';
import SearchImage from '../assets/common/pluginIcons/search.svg';
import addIcon from '../assets/common/pluginIcons/plusforexpandnode.svg';

import { getModuleIcon, getModuleText, getModuleDescription, getModuleTags } from '../config';

//TODO: need to work on this
import {
  nodeGroupsConfig,
  tabDetails as stencilTabDetails,
  nodeGroupsConfigComingSoon,
} from './stencilConfig';
import ReactPortal from '../../components/ReactPortal';
import './expandNode.css';
const ExpandNode = props => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('All');

  const isFullscreenEnabled = window.innerHeight == window.screen.height;

  const nodes = stencilTabDetails.reduce((acc, d) => {
    const data = nodeGroupsConfig[d.key].map(k => k.type);
    return acc.concat(data);
  }, []);

  const nodesComingSoon = stencilTabDetails.reduce((acc, d) => {
    const data = nodeGroupsConfigComingSoon[d.key].map(k => k.type);
    return acc.concat(data);
  }, []);

  const tabDetails = [
    {
      key: 'All',
      label: 'All',
    },
    ...stencilTabDetails,
  ];

  const handleTabChange = group => {
    setSelectedGroup(group);
  };

  const filteredNodes = nodes
    .filter(node => {
      const nodeText = getModuleText(node).toLowerCase();
      const searchQueryLower = searchQuery.toLowerCase();
      const inSelectedGroup =
        selectedGroup === 'All' ||
        nodeGroupsConfig[selectedGroup].some(groupNode => groupNode.type === node);

      return inSelectedGroup && (nodeText.includes(searchQueryLower) || searchQueryLower === '');
    })
    .sort((a, b) => {
      const textA = getModuleText(a).toLowerCase();
      const textB = getModuleText(b).toLowerCase();
      return textA.localeCompare(textB);
    });

  const filteredComingSoonNodes = nodesComingSoon
    .filter(node => {
      const nodeText = getModuleText(node).toLowerCase();
      const searchQueryLower = searchQuery.toLowerCase();
      const inSelectedGroup =
        selectedGroup === 'All' ||
        nodeGroupsConfigComingSoon[selectedGroup].some(groupNode => groupNode.type === node);

      return inSelectedGroup && (nodeText.includes(searchQueryLower) || searchQueryLower === '');
    })
    .sort((a, b) => {
      const textA = getModuleText(a).toLowerCase();
      const textB = getModuleText(b).toLowerCase();
      return textA.localeCompare(textB);
    });

  const onNodeAdd = nodeName => {
    props.handleNodeAdd(nodeName);
    props.onClose();
  };

  return (
    <>
      <ReactPortal
        wrapperId={!isFullscreenEnabled ? 'react-portal-modal' : 'react-portal-modal-container'}
      >
        <>
          {/* Backdrop */}
          <div
            className="fixed top-0 left-0 z-40 w-full h-full"
            style={{ backgroundColor: '#707070' }}
            onClick={() => props.onClose()}
          ></div>

          {/* Modal */}
          <div className="fixed top-2 left-0 w-full h-full flex items-center justify-center z-50">
            <div
              className="bg-white p-6 rounded-lg shadow-lg pb-0 pt-0 relative"
              style={{ width: '80%', height: '90%' }}
            >
              <div className="text-sm py-4">
                <div className="flex mt-1">
                  {tabDetails.map(tab => (
                    <div
                      key={tab.key}
                      className="mr-5 mt-1 text-sm cursor-pointer"
                      style={{
                        color: selectedGroup === tab.key ? '#3b82f6' : 'inherit',
                      }}
                      onClick={() => handleTabChange(tab.key)}
                    >
                      {tab.label}
                    </div>
                  ))}
                  <div className="relative ml-8">
                    <img
                      src={SearchImage}
                      alt="Search"
                      className="w-4 h-4 absolute left-4 top-3 text-[#7B7B7B] search-img"
                    />
                    <input
                      type="text"
                      name="name"
                      placeholder="Search"
                      style={{
                        height: '31px',
                        border: '1px solid #CACACA',
                        borderRadius: '30px',
                        fontSize: '12px',
                        paddingLeft: '25px',
                        width: '200px',
                      }}
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Close
                    onClick={() => props.onClose()}
                    className="absolute top-5 right-4 cursor-pointer"
                    style={{ color: '#A7A9AC' }}
                  />
                </div>
              </div>

              <div
                className="pt-3 overflow-auto  p-4 rounded-xl"
                style={{
                  backgroundColor: '#F4F7FB',
                  maxHeight: 'calc(100% - 100px)',
                }}
              >
                <div className="grid gap-4 md:grid-cols-4">
                  {filteredNodes.map((node, index) => (
                    <div
                      className="p-4 border h-60 rounded-xl bg-white  shadow-sm flex flex-col"
                      style={{ boxShadow: '0px 4px 10px #0000000D' }}
                      key={index}
                    >
                      <div className="flex items-center">
                        <img
                          src={getModuleIcon(node)}
                          alt="SMS"
                          className="w-10 h-10 mr-2 border rounded-xl p-2"
                          style={{ borderColor: '#E9E9E9' }}
                        />
                        <p className="text-center font-bold">{getModuleText(node)}</p>
                        <div className="flex-grow"></div> {/* This will create a flexible space */}
                        <button
                          style={{
                            fontSize: '18px',
                            color: '#3b82f6',
                            border: '2px solid #3b82f6',
                            borderRadius: '50%',
                            width: '27px',
                            height: '27px',
                            verticalAlign: 'text-top',
                            cursor:
                              getModuleText(node) === 'Start' || getModuleText(node) === 'End'
                                ? 'not-allowed'
                                : props.isReadOnly
                                  ? 'not-allowed'
                                  : 'pointer',
                          }}
                          onClick={() => onNodeAdd(node)}
                          disabled={
                            getModuleText(node) === 'Start' ||
                            getModuleText(node) === 'End' ||
                            props.isReadOnly
                          }
                        >
                          <img
                            src={addIcon}
                            alt="Search"
                            style={{
                              paddingLeft: '5px',
                              height: '17px',
                              width: '17px',
                            }}
                          />
                        </button>
                      </div>

                      <p
                        className="text-sm text-gray-600 mt-5 h-20"
                        style={{
                          color: '#A7A7A7',
                          fontSize: '12px',
                          textAlign: 'justify',
                          lineHeight: '1.5',
                        }}
                      >
                        {getModuleDescription(node)}
                      </p>

                      <div className=" border-b mt-10"></div>
                      <div
                        style={{
                          maxWidth: 'auto',
                          display: 'flex',
                          gap: '6px',
                          marginTop: '5px',
                        }}
                      >
                        {getModuleTags(node).map((tag, index) => (
                          <p
                            key={index}
                            style={{
                              color: '#7A7A7A',
                              fontSize: '12px',
                              backgroundColor: '#E9E9E9',
                              borderRadius: '15px',
                              padding: '4px 8px',
                            }}
                          >
                            {tag}
                          </p>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                {filteredComingSoonNodes.length > 0 && (
                  <div className="coming-soon-divider-expand">
                    <span>Coming Soon</span>
                  </div>
                )}
                <div className="grid gap-4 md:grid-cols-4">
                  {filteredComingSoonNodes.map((node, index) => (
                    <div
                      className="p-4 border h-60 rounded-xl bg-white  shadow-sm flex flex-col"
                      style={{
                        boxShadow: '0px 4px 10px #0000000D',
                        // background: "#e9e9e97a",
                      }}
                      key={index}
                    >
                      <div className="flex items-center">
                        <img
                          src={getModuleIcon(node)}
                          alt="SMS"
                          className="w-10 h-10 mr-2 border rounded-xl p-2"
                          style={{ borderColor: '#E9E9E9' }}
                        />
                        <p className="text-center font-bold">{getModuleText(node)}</p>
                        <div className="flex-grow"></div> {/* This will create a flexible space */}
                      </div>

                      <p
                        className="text-sm text-gray-600 mt-5 h-20"
                        style={{
                          color: '#A7A7A7',
                          fontSize: '12px',
                          textAlign: 'justify',
                          lineHeight: '1.5',
                        }}
                      >
                        {getModuleDescription(node)}
                      </p>

                      <div className=" border-b mt-10"></div>
                      <div
                        style={{
                          maxWidth: 'auto',
                          display: 'flex',
                          gap: '6px',
                          marginTop: '5px',
                        }}
                      >
                        {getModuleTags(node).map((tag, index) => (
                          <p
                            key={index}
                            style={{
                              color: '#7A7A7A',
                              fontSize: '12px',
                              backgroundColor: '#E9E9E9',
                              borderRadius: '15px',
                              padding: '4px 8px', // Adjust padding as needed
                            }}
                          >
                            {tag}
                          </p>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                {!filteredComingSoonNodes.length && !filteredNodes.length && (
                  <div>
                    <p className="text-gray-600">No results found.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      </ReactPortal>
    </>
  );
};

export default ExpandNode;
