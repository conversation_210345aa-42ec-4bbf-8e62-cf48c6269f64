export const tabDetails = [
    {
      key: "Channels",
      label: "Channels",
    },
    {
      key: "Utilities",
      label: "Utilities",
    },
    {
      key: "Marketplace",
      label: "Marketplace",
    },
  ];
  
export const nodeGroupsConfig = {
    Channels: [
      {
        type: "sms",
      },
      {
        type: "email",
      },
      {
        type: "voice",
      },
      {
        type: "whatsapp",
      },
      {
        type: "rcs",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
    Utilities: [
      {
        type: "appStart",
      },
      {
        type: "appEnd",
      },
      {
        type: "addcontact",
      },
      {
        type: "choice",
      },
      {
        type: "http",
      },
      {
        type: "repeat",
      },
      {
        type: "script",
      },
      {
        type: "waitforresponse",
      },
      {
        type: "menu",
      },
      {
        type: "network",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
    Marketplace: [
      {
        type: "shopify",
      },
  
      {
        type: "openai",
      },
  
      {
        type: "zendesk",
      },
  
      {
        type: "freshdesk",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
  };
  
  export const nodeGroupsConfigComingSoon = {
    Channels: [
      {
        type: "ussd",
      },
  
      {
        type: "messenger",
      },
      {
        type: "instagram",
      },
      {
        type: "line",
      },
      {
        type: "telegram",
      },
      {
        type: "wechat",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
    Utilities: [
  
      {
        type: "webhook",
      },
      {
        type: "keypress",
      },
      {
        type: "hangup",
      },
      {
        type: "jump",
      },
      {
        type: "datetime",
      },
      {
        type: "callforward",
      },
  
      {
        type: "googlesheet",
      },
      {
        type: "database",
      },
      {
        type: "language",
      },
      {
        type: "wait",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
    Marketplace: [
      {
        type: "salesforce",
      },
      {
        type: "servicenow",
      },
      {
        type: "hubspot",
      },
      {
        type: "zapier",
      },
      {
        type: "quickbooks",
      },
      {
        type: "stripe",
      },
      {
        type: "amazons3",
      },
      {
        type: "amazonsns",
      },
      {
        type: "adp",
      },
      {
        type: "excel",
      },
      {
        type: "workday",
      },
      {
        type: "monday",
      },
      {
        type: "zoho",
      },
      {
        type: "slack",
      },
      {
        type: "facebook",
      },
      {
        type: "script",
      },
      {
        type: "dialogflow",
      },
    ].sort((a, b) => a.type.localeCompare(b.type)),
  };
  