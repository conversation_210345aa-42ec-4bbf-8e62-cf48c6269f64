import React, { useEffect, useState } from 'react';
import FlowsPanel from './widget/flowsPanel';
import NeuraTalkEditor from './neuratalk-editor';
import { useGetApplicationDetailsQuery } from '@/store/slices/apiSlice';
import { useSelector, useDispatch } from 'react-redux';
import { setActiveFlow } from '@/store/slices/flowsSlice';
const EditorWrapper = () => {
  // const [selectedFlow, setSelectedFlow] = useState({});
  const dispatch = useDispatch();
  const selectedFlow = useSelector(state => state.flows.activeFlowId);
  // const {
  //   data: flow,
  //   isLoading,
  //   isError,
  //  } = useGetApplicationDetailsQuery({ appId: selectedFlow.appId }, { skip: !selectedFlow.appId });
  // console.log('SelectedFlows:', flow);
  const onSelectFlow = selectedFlow => {
    dispatch(setActiveFlow(selectedFlow));
  };

  useEffect(() => {
    return () => {//TODO: rm this after implementing default flo
      dispatch(setActiveFlow(''));
    };
  }, []);

  return (
    <div>
      <FlowsPanel onFlowSelect={onSelectFlow} />
      <NeuraTalkEditor id={selectedFlow.appId} />
    </div>
  );
};

export default EditorWrapper;
