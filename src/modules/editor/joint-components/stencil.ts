import { dia } from 'rappid';
import { getModuleIcon, getModuleText } from '@/modules/editor/utils/config';
import { jointJsEditorTheme as theme } from '@/modules/editor/utils/constants';


// Size mapping for specific node types
const sizeMapping: Record<string, { width: number; height: number }> = {
  appStart: { width: 98, height: 52 },
  appEnd: { width: 98, height: 52 },
};

// Default size for other nodes
const defaultSize = { width: 130, height: 52 };

// Common attributes for rect
const commonRectAttrs = {
  fill: theme.backgroundColor,
  stroke: theme.primaryColor,
  'stroke-width': 1,
  shadowColor: theme.errorColor,
  shadowBlur: 20,
  filter: 'drop-shadow(0px 0px 10px #53535333)',
  rx: 15,
  ry: 15,
};

// Common attributes for image
const commonImageAttrs = {
  width: 50,
  height: 50,
  x: 0,
  y: 0,
};

// Common attributes for text
const commonTextAttrs = {
  'font-size': 12,
  'text-anchor': 'start',
  fill: theme.primaryColor,
  x: 60,
  y: 29,
};

// Common markup for standard nodes
const standardMarkup = [
  { tagName: 'rect', selector: 'rect' },
  { tagName: 'image', selector: 'image' },
  { tagName: 'text', selector: 'text' },
];

// Get stencil definition based on node type
export const getStencilByType = (type: string) => {
  // Get size for the node
  const { width, height } = sizeMapping[type] ?? defaultSize;

  // const iconSize = nodesData[type]?.iconSize ?? 50;
  // const iconPosition = iconSize === 20 ? { x: 15, y: 15 } : { x: 0, y: 0 };

  // ChoiceOption node
  if (type === 'choiceOption') {
    return dia.Element.define(
      type,
      {
        size: { width, height: 60 },
        attrs: {
          rect: {
            ...commonRectAttrs,
            width: 'calc(w)',
            height: 'calc(h)',
            stroke: theme.primaryColor,
            rx: 6,
            ry: 6,
          },
          text: {
            text: '',
            'font-size': 10,
            'text-anchor': 'middle',
            fill: theme.primaryColor,
            textVerticalAnchor: 'middle',
            x: 'calc(0.5*w)',
            y: 15,
          },
          rect1: {
            width: 60,
            height: 20,
            fill: '#F2EEEE',
            stroke: '#F2EEEE',
            'stroke-width': 1,
            shadowBlur: 20,
            filter: 'drop-shadow(0px 0px 10px #53535333)',
            rx: 6,
            ry: 6,
            y: 30,
            x: 'calc(0.07*w)',
          },
          text1: {
            text: '',
            'font-size': 9,
            'text-anchor': 'middle',
            fill: theme.primaryColor,
            'font-weight': 'bold',
            textVerticalAnchor: 'middle',
            x: 'calc(0.5*w)',
            y: 40,
          },
        },
      },
      {
        markup: [
          { tagName: 'rect', selector: 'rect' },
          { tagName: 'text', selector: 'text' },
          { tagName: 'rect', selector: 'rect1' },
          { tagName: 'text', selector: 'text1' },
        ],
      }
    );
  }

  // Choice node
  if (type === 'choice') {
    return dia.Element.define(
      type,
      {
        size: { width, height },
        attrs: {
          rect: {
            ...commonRectAttrs,
            width: 50,
            height: 50,
          },
          image: {
            ...commonImageAttrs,
            'xlink:href': getModuleIcon(type),
          },
          text: {
            ...commonTextAttrs,
            text: getModuleText(type),
          },
          rect4: {
            fill: '#FFE4EC',
            stroke: '#FFE4EC',
            strokeWidth: 1,
            x: 50,
            y: 50,
            width: 80,
            height: 20,
            rx: 6,
            ry: 6,
          },
          label4: {
            text: 'No Match',
            fill: theme.errorColor,
            fontSize: 10,
            textVerticalAnchor: 'middle',
            textAnchor: 'middle',
            pointerEvents: 'none',
            x: 90,
            y: 60,
          },
          rect5: {
            fill: '#CBFFE7',
            stroke: '#CBFFE7',
            strokeWidth: 1,
            x: -55,
            y: 50,
            width: 60,
            height: 20,
            rx: 6,
            ry: 6,
          },
          label5: {
            text: 'Match',
            fill: theme.successColor,
            fontSize: 10,
            textVerticalAnchor: 'middle',
            textAnchor: 'middle',
            pointerEvents: 'none',
            x: -25,
            y: 60,
          },
        },
      },
      {
        markup: [
          { tagName: 'rect', selector: 'rect' },
          { tagName: 'image', selector: 'image' },
          { tagName: 'text', selector: 'text' },
          { tagName: 'rect', selector: 'rect4' },
          { tagName: 'rect', selector: 'rect5' },
          { tagName: 'text', selector: 'label4' },
          { tagName: 'text', selector: 'label5' },
        ],
      }
    );
  }

  // Default node (for appStart, appEnd, whatsapp, etc.)
  return dia.Element.define(
    type,
    {
      size: { width, height },
      attrs: {
        rect: {
          ...commonRectAttrs,
          width: 50,
          height: 50,
        },
        image: {
          ...commonImageAttrs,
          // width: iconSize, // <-- Use iconSize from config
          // height: iconSize,
          // x: iconPosition.x,
          // y: iconPosition.y,
          'xlink:href': getModuleIcon(type),
        },
        text: {
          ...commonTextAttrs,
          text: getModuleText(type),
        },
      },
    },
    { markup: standardMarkup }
  );
};
