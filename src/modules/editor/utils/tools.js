import { dia, linkTools, elementTools } from "rappid";

let updateSettingDetails = null;
let toggleNodeModal = null;

export const initUtilFunction = (fun, fun1) => {
  updateSettingDetails = fun;
  toggleNodeModal = fun1;
};

if (!document.getElementById("modal-root")) {
  const modalRoot = document.createElement("div");
  modalRoot.id = "modal-root";
  document.body.appendChild(modalRoot);
}

const removeButton = new linkTools.Remove();

export const customLinkTools = new dia.ToolsView({
  tools: [removeButton],
});

var removeElement = new elementTools.Remove({
  focusOpacity: 0.5,
  rotate: true,
  // top-mid
  x: 45,
  y: 8,
  offset: { x: 0, y: 0 },
  action: function (evt, elementView, toolView) {
    if (
      elementView.model.attributes.type === "choiceOption" ||
      elementView.model.attributes.type === "choice"
    ) {
      const childs = elementView.model.graph.getSuccessors(elementView.model);

      let parentId = null;

      if (elementView.model.attributes.type === "choice") {
        parentId = elementView.model.id;
      } else {
        const [{ id: parentChoiceId }] =
          elementView.model.graph.getPredecessors(elementView.model);

        parentId = parentChoiceId;
      }

      childs.forEach((child) => {
        if (child.attributes.type !== "appEnd") {
          child.remove();
        }
      });

      setTimeout(() => {
        updateSettingDetails(parentId, elementView.model.id);
      }, 0);
    }
    elementView.model.remove({ ui: true, tool: toolView.cid });
  },
});

elementTools.editButton = elementTools.Button.extend({
  name: "edit-button",

  options: {
    markup: [
      // {
      //     tagName: 'image',
      //     selector: 'icon',
      //     attributes: {
      //         src: whatsapp,
      //         width: '10px',
      //         height: '10px',
      //         zIndex:9999
      //     },
      // },
      // {
      //     tagName: 'path',
      //     selector: 'icon',
      //     attributes: {
      //         'd': 'M0 0h24v24H0z',
      //         'fill': 'none',
      //         'stroke': 'none',
      //     }
      // },

      // {
      //     tagName: 'path',
      //     selector: 'icon2',
      //     attributes: {
      //         'd': 'M13.5 6.5l4 4',
      //     }
      // },
      {
        tagName: "circle",
        selector: "button",
        attributes: {
          r: 7,
          fill: "red",
          cursor: "pointer",
        },
      },
      {
        tagName: "path",
        selector: "icon1",
        attributes: {
          d: "M-7 8h4l10.5 -10.5a1.5 1.5 0 0 0 -4 -4l-10.5 10.5v4",
          fill: "#fff",
          stroke: "#fff",
          transform: "scale(0.5)",
          width: "5px",
          height: "5px",
        },
      },
    ],
    // distance: 60,
    offset: {},
    x: "15%",
    y: "0%",
    action: function (evt) {
      alert("View id: " + this.id + "\n" + "Model id: " + this.model.id);
    },
  },
});

export const customElementTools = new dia.ToolsView({
  tools: [removeElement],
});

export const getElementTools = (type, width, childs) => {
  let x = 45;

  const xMapping = {
    choice: {
      x: 100,
    },
  };

  if (xMapping[type]) {
    x = xMapping[type].x;
  }

  if (type === "choiceOption") {
    x = width;
  }

  const removeElement = new elementTools.Remove({
    focusOpacity: 0.5,
    rotate: true,
    // top-mid
    x,
    y: 8,
    offset: { x: 0, y: 0 },
    action: function (evt, elementView, toolView) {
      if (
        elementView.model.attributes.type === "choiceOption" ||
        elementView.model.attributes.type === "choice"
      ) {
        const childs = elementView.model.graph.getSuccessors(elementView.model);

        let parentId = null;

        if (elementView.model.attributes.type === "choice") {
          parentId = elementView.model.id;
        } else {
          const [{ id: parentChoiceId }] =
            elementView.model.graph.getPredecessors(elementView.model);

          parentId = parentChoiceId;
        }

        childs.forEach((child) => {
          if (child.attributes.type !== "appEnd") {
            child.remove();
          }
        });

        setTimeout(() => {
          updateSettingDetails(parentId, elementView.model.id);
        }, 0);
      }
      elementView.model.remove({ ui: true, tool: toolView.cid });
    },
  });

  return new dia.ToolsView({
    tools:
      childs?.length === 0 && type !== "appEnd" && type !== "choice"
        ? [removeElement]
        : [removeElement],
  });
};
