import React from "react";
import CustomInput from "./utils/custom-input";

const openAI = (props) => {
  const { details, updateDetails, error } = props;

  return (
    <>
      <h2 className="font-bold mb-2 mt-3 input-label" style={{ color: "#232323" }}>
        Configure openAI
      </h2>

      <CustomInput
        onChange={updateDetails}
        name="settings.api_key"
        value={details.settings.api_key}
        error={error}
        label="API key"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.prompt"
        value={details.process.prompt}
        error={error}
        label="Prompt"
      />
    </>
  );
};

export default openAI;
