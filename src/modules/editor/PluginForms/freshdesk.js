import React from "react";
import CustomInput from "./utils/custom-input";
import CustomSelect from "./utils/custom-select";

const Freshdesk = (props) => {
  const { details, updateDetails, error } = props;

  const options = [
    {
      value: "Create Ticket",
      label: "Create Ticket",
    },
    {
      value: "Delete Ticket",
      label: "Delete Ticket",
    },
    {
      value: "Update Ticket",
      label: "Update Ticket",
    },
  ];

  return (
    <>
      <CustomInput
        onChange={updateDetails}
        name="settings.api_key"
        value={details.settings.api_key}
        error={error}
        label="API key"
      />

      <CustomInput
        onChange={updateDetails}
        name="settings.domain"
        value={details.settings.domain}
        error={error}
        label="Domain"
      />

      <CustomSelect
        onChange={updateDetails}
        name="process.type"
        value={details.process.type}
        error={error}
        label="Action"
        options={options}
      />

      <CustomInput
        onChange={updateDetails}
        name="process.subject"
        value={details.process.subject}
        error={error}
        label="Subject"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.description"
        value={details.process.description}
        error={error}
        label="Description"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.email"
        value={details.process.email}
        error={error}
        label="Email"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.priority"
        value={details.process.priority}
        error={error}
        label="Priority"
      />
      <CustomInput
        onChange={updateDetails}
        name="process.status"
        value={details.process.status}
        error={error}
        label="Status"
      />
    </>
  );
};

export default Freshdesk;
