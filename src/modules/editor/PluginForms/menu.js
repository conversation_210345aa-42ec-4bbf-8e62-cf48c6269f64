import React from "react";
import ErrorMessage from "./utils/ErrorMessage";
import CustomInput from "./utils/custom-input";

const APIForm = (props) => {
  const { details, updateDetails, error } = props;
  const defaultHeader = { menuName: "", menuValue: "",  showRemoveButton: false };

  if (details.process.menuItems.length === 0) {
    details.process.menuItems.push(defaultHeader);
  }

  const handleAddHeader = (index, event) => {
    if (event) {
      event.preventDefault();
    }

    const temp = [...details.process.menuItems];
    const newHeader = { menuName: "", menuValue: "" };

    const isDuplicate = temp.some(
      (header) =>
        header.menuName.toLowerCase() === newHeader.menuName.toLowerCase() ||
        header.menuValue.toLowerCase() === newHeader.menuValue.toLowerCase()
    );

    if (!isDuplicate) {
      //temp.splice(index + 1, 0, newHeader);
      temp.splice(index + 1, 0, { menuName: "", menuValue: "", showRemoveButton: true });
      updateDetails({ target: { value: temp, name: "process.menuItems" } });
    } else {
      alert("Error: Duplicate values are not allowed for menuName or menuValue.");
    }
  };

  const headerChange = (event, index) => {
    const { name, value } = event.target;
    const copy = [...details.process.menuItems];

    const isDuplicate = copy.some(
      (header, i) =>
        i !== index &&
        (header.menuName.toLowerCase() === value.toLowerCase() ||
          header.menuValue.toLowerCase() === value.toLowerCase())
    );

    if (!isDuplicate) {
      copy[index][name] = value;
      updateDetails({ target: { value: copy, name: "process.menuItems" } });
    } else {
      alert("Error: Duplicate values are not allowed for menuName or menuValue.");
    }
  };

  const handleRemoveHeader = (index) => {
    if (index > 0) {
      updateDetails({
        target: {
          value: details.process.menuItems.filter((_, i) => i !== index),
          name: "process.menuItems",
        },
      });
    }
  };

  const headerNavigationChange = (event) => {
    const { name, value } = event.target;
    const navigationOption = name.split(".")[0];
    const type = name.split(".")[1];
    const copy = details.process.menuNavigationOptions;

    // Check if the updated value already exists in the menuNavigationOptions
    const isDuplicate =
      Object.values(copy).some(
        (option) =>
          option[type].toLowerCase() === value.toLowerCase()
      );

    if (!isDuplicate) {
      copy[navigationOption][type] = value;
      updateDetails({
        target: { value: copy, name: "process.menuNavigationOptions" },
      });
    } else {
      alert("Duplicate value for navigation option. Please enter unique values.");
    }
  };

  return (
    <>
      <CustomInput
        onChange={updateDetails}
        name="process.variable_name"
        value={details.process.variable_name}
        error={error}
        label="Menu key"
      />
      <h2 className="font-bold input-label">Configure Menu</h2>
      {details.process.menuItems.map((header, index) => (
        <>
          <div key={index} className="flex items-center mt-2">
            <input
              className="text-md p-2 border border-gray-300 mr-2 rounded"
              style={{ width: "20ch" }}
              type="text"
              name={"menuName"}
              value={header.menuName}
              onChange={(event) => headerChange(event, index)}
              placeholder={"Key"}
            />
            <div style={{ display: "flex", alignItems: "center" }}>
              <input
                className="text-md p-2 border border-gray-300 rounded"
                style={{ width: "13ch" }}
                type="text"
                name="menuValue"
                value={header.menuValue}
                onChange={(event) => headerChange(event, index)}
                placeholder={"Action"}
              />
              <div
                style={{
                  color: "white",
                  display: "flex",
                  flexDirection: "column",
                  background: "#ED1C24",
                  height: "41px",
                  width: "20px",
                  marginLeft: "-20px",
                  borderTopRightRadius: "5px",
                  borderBottomRightRadius: "5px",
                }}
              >
                {header.showRemoveButton && (
                  <button
                    style={{ fontSize: "13" }}
                    onClick={(event) => {
                      event.preventDefault();
                      handleRemoveHeader(index);
                    }}
                  >
                    -
                  </button>
                )}

                <button
                  onClick={(event) => {
                    event.preventDefault();
                    handleAddHeader(index, event);
                  }}
                >
                  +
                </button>
              </div>
            </div>
          </div>
          <ErrorMessage name={`process.menuItems[${index}]`} error={error} />
        </>
      ))}
      <h2 className="font-bold mb-1 mt-3 input-label">Navigation</h2>
      {Object.keys(details.process.menuNavigationOptions).map(
        (navigationOption, index) => (
          <>
            <div key={index} className="flex items-center mt-2">
              <div>
                <h3
                  className="font-bold mb-2 input-label"
                  style={{ fontSize: "11px", color: "#232323" }}
                >
                  {getNavigationOptionHeading(navigationOption)}
                </h3>
                <div className="flex items-center">
                  <input
                    className="text-md p-2 border border-gray-300 mr-2 rounded"
                    style={{ width: "7ch" }}
                    type="text"
                    name={`${navigationOption}.code`}
                    value={
                      details.process.menuNavigationOptions[navigationOption]
                        .code
                    }
                    onChange={(event) => headerNavigationChange(event, index)}
                    placeholder={"Key"}
                  />

                  <div style={{ display: "flex", alignItems: "center" }}>
                    <input
                      className="text-md p-2 border border-gray-300 rounded"
                      style={{ width: "26ch" }}
                      type="text"
                      name={`${navigationOption}.message`}
                      value={
                        details.process.menuNavigationOptions[navigationOption]
                          .message
                      }
                      onChange={(event) => headerNavigationChange(event, index)}
                      placeholder={"Action"}
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        )
      )}
    </>
  );
};

function getNavigationOptionHeading(navigationOption) {
  switch (navigationOption) {
    case "mainMenu":
      return "Main Menu";
    case "backwardSkipMenu":
      return "Backward Skip Menu";
    case "firstPage":
      return "First Page";
    case "lastPage":
      return "Last Page";
    case "previousPage":
      return "Previous Page";
    case "nextPage":
      return "Next Page";
    default:
      return `Navigation Option `;
  }
}

export default APIForm;
