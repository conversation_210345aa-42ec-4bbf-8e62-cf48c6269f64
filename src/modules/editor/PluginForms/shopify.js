import React from "react";
import CustomInput from "./utils/custom-input";

const Shopify = (props) => {
  const { details, updateDetails, error } = props;

  return (
    <>
      <h2 className="font-bold mb-2 mt-3 input-label" style={{ color: "#232323" }}>
        Configure Shopify
      </h2>

      <CustomInput
        onChange={updateDetails}
        name="settings.access_token"
        value={details.settings.access_token}
        error={error}
        label="API Token"
      />

      <CustomInput
        onChange={updateDetails}
        name="settings.domain"
        value={details.settings.domain}
        error={error}
        label="Domain"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.tax"
        value={details.process.tax}
        error={error}
        label="Tax"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.currency"
        value={details.process.currency}
        error={error}
        label="Currency"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.title"
        value={details.process.title}
        error={error}
        label="Title"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.price"
        value={details.process.price}
        error={error}
        label="Price"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.rate_of_tax"
        value={details.process.rate_of_tax}
        error={error}
        label="Rate of tax"
      />
    </>
  );
};

export default Shopify;
