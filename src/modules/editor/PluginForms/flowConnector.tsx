import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { useFormContext } from 'react-hook-form';

import { FloatingField } from '@/components/ui/floating-label';
import { FormControl, FormField, FormItem } from '@/components/ui/form';

import { useGetFlowsQuery } from '@/store/api/flowApi';

export default function FlowConnector() {
  const { control } = useFormContext();
  const selectedFlowId = useSelector((state: any) => state.flows.activeFlowId);
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;

  const { data: flowsData } = useGetFlowsQuery({ botId });

  const flowOptions = useMemo(() => {
    if (!flowsData) return [];

    // Handle different possible data structures from the API
    const flows = Array.isArray(flowsData)
      ? flowsData
      : flowsData.data?.items || flowsData.items || [];

    return flows
      .filter((f: any) => f.id !== selectedFlowId?.id)
      .map((f: any) => ({
        label: f.name,
        value: f.id,
      }));
  }, [flowsData, selectedFlowId]);

  return (
    <div className="flex flex-col gap-4 px-4 py-4">
      {/* Target Flow ID */}
      <FormField
        control={control}
        name="process.targetFlowId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                label="Target Flow"
                as="select"
                value={field.value || ''}
                onChange={field.onChange}
                options={flowOptions}
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
}
