import React, { useEffect, useRef, useState } from 'react';
import { useAppDispatch } from '@/hooks/useRedux';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { FloatingField } from '@/components/ui/floating-label';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

import { useGetFlowsQuery } from '@/store/api/flowApi'; // <-- update this if your flow fetch logic differs
import type { ModuleData } from '../types';

interface FlowConnectorProps {
  details: ModuleData;
  setModuleDetails: React.Dispatch<React.SetStateAction<ModuleData>>;
  updateDetails: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error: any[];
  isEdit: boolean;
}

export default function FlowConnector({ details, setModuleDetails }: FlowConnectorProps) {
  const { t } = useTranslation();
  const selectedFlowId = useSelector((state: any) => state.flows.activeFlowId);
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;

  const { data: flowsData } = useGetFlowsQuery({ botId });

  const didInitialize = useRef(false);

  const [flowId, setFlowId] = useState('');
  const [passExistingContext, setPassExistingContext] = useState(true);
  const [resumeFromThisContext, setResumeFromThisContext] = useState(false);
  const [routingType, setRoutingType] = useState('conditional');

  const items = flowsData?.data?.items || flowsData || [];
  console.log('FlowIDand', selectedFlowId, flowId);

  const flowOptions = items
    .filter((f: any) => f.id !== selectedFlowId.id || f.id === flowId)
    .map((f: any) => ({
      label: f.name,
      value: f.id,
    }));

  // Hydrate from `details`
  useEffect(() => {
    if (!details || didInitialize.current) return;

    const process = details.process?.flowConfig || {};
    const settings = details.settings || {};

    setFlowId(process.flowId || '');
    setPassExistingContext(process.passExistingContext ?? true);
    setResumeFromThisContext(process.resumeFromThisContext ?? false);
    setRoutingType(settings.routingType || 'conditional');

    didInitialize.current = true;
  }, [details]);
  const selectedFlow = items.find(item => item.id === flowId);
  console.log('flowOptions', selectedFlow);

  // Sync to parent
  useEffect(() => {
    if (!didInitialize.current || !selectedFlow) return;

    setModuleDetails(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        routingType,
      },
      process: {
        ...prev.process,
        flowConfig: {
          flowId,
          journeyId: selectedFlow.appId,
          passExistingContext,
          resumeFromThisContext,
        },
      },
    }));
  }, [flowId, passExistingContext, resumeFromThisContext, routingType, selectedFlow]);

  return (
    <div className="flex flex-col gap-4 px-4 py-4">
      {/* Flow selection */}
      <FloatingField
        label="Target Flow"
        as="select"
        value={flowId}
        onChange={setFlowId}
        options={flowOptions}
      />
      <div className="flex items-center justify-around">
        <Switch checked={passExistingContext} onCheckedChange={setPassExistingContext} />
        <Label className="text-xs">{t('Remember Context between connected flows')}</Label>
      </div>

      {/* Routing Type (Optional UI control) */}
      {/* <FloatingField
        label="Routing Type"
        as="select"
        value={routingType}
        onChange={setRoutingType}
        options={[
          { label: 'Conditional', value: 'conditional' },
          { label: 'Sequential', value: 'sequential' },
          { label: 'Parallel', value: 'parallel' },
        ]}
      /> */}

      {/* Pass context switch */}
    </div>
  );
}
