import React from "react";
import CustomInput from "./utils/custom-input";

const Repeat = (props) => {
  const { details, updateDetails, error } = props;

  return (
    <>
      <CustomInput
        onChange={updateDetails}
        name="process.repeat_count"
        value={details.process.repeat_count}
        error={error}
        label="Repeat Count"
      />
    </>
  );
};

export default Repeat;
