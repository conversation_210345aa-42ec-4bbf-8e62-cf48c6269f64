import React from 'react';
import {
  Button,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography,
  MenuItem,
} from '@mui/material';
import { Cancel, Save } from '@mui/icons-material';
// import { pluginCardStyles } from '../styles/generalStyles'

const Default = props => {
  console.log('[chethan] props.moduleData : ' + JSON.stringify(props.moduleData));

  return (
    <div
    // style={pluginCardStyles}
    >
      <form>
        <Typography variant="h5" gutterBottom>
          {props.moduleData.coordinates.nodeData.title}:::{' '}
          {props.moduleData.coordinates.nodeData.id}
        </Typography>
        <TextField label="Name" fullWidth />
        <FormControl fullWidth sx={{ mt: 2 }}>
          <FormLabel>Enter your option</FormLabel>
          <Select>
            <MenuItem value="usa">USA</MenuItem>
            <MenuItem value="uk">UK</MenuItem>
            <MenuItem value="india">India</MenuItem>
          </Select>
        </FormControl>
        <TextField label="Comments" fullWidth multiline sx={{ mt: 2 }} />
        <FormControl sx={{ mt: 2 }}>
          <FormLabel>Enrollment</FormLabel>
          <RadioGroup>
            <FormControlLabel value="yes" control={<Radio />} label="Yes" />
            <FormControlLabel value="no" control={<Radio />} label="No" />
          </RadioGroup>
          <FormHelperText>Choose Yes or No</FormHelperText>
        </FormControl>
        <Button variant="contained" startIcon={<Save />} sx={{ mt: 2 }}>
          Save
        </Button>
        <Button
          variant="contained"
          startIcon={<Cancel />}
          sx={{ mt: 2, ml: 2 }}
          onClick={props.handleClose}
        >
          Cancel
        </Button>
      </form>
    </div>
  );
};

export default Default;
