import React from "react";
import CustomInput from "./utils/custom-input";
import CustomSelect from "./utils/custom-select";

const Zendesk = (props) => {
  const { details, updateDetails, error } = props;

  const options = [
    {
      value: "Create Ticket",
      label: "Create Ticket",
    },
    {
      value: "Delete Ticket",
      label: "Delete Ticket",
    },
    {
      value: "Update Ticket",
      label: "Update Ticket",
    },
  ];
  return (
    <>
      <h2 className="font-bold mb-2 mt-3 input-label">Configure Zendesk</h2>

      <CustomInput
        onChange={updateDetails}
        name="settings.api_key"
        value={details.settings.api_key}
        error={error}
        label="API key"
      />

      <CustomInput
        onChange={updateDetails}
        name="settings.domain"
        value={details.settings.domain}
        error={error}
        label="Domain"
      />

      <CustomInput
        onChange={updateDetails}
        name="settings.email"
        value={details.settings.email}
        error={error}
        label="Email"
      />

      <CustomSelect
        onChange={updateDetails}
        name="process.type"
        value={details.process.type}
        error={error}
        label="Type"
        options={options}
      />

      <CustomInput
        onChange={updateDetails}
        name="process.subject"
        value={details.process.subject}
        error={error}
        label="Subject"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.description"
        value={details.process.description}
        error={error}
        label="Description"
      />
    </>
  );
};

export default Zendesk;
