import React from "react";
import TextArea from "./utils/text-area";
import CustomInput from "./utils/custom-input";

const WaitForResponseForm = (props) => {
  const { details, updateDetails, error } = props;

  return (
    <>
      <h2 className="font-bold mb-2 mt-3 input-label" style={{ color: "#232323" }}>
        Configure Wait
      </h2>

      <TextArea
        onChange={updateDetails}
        name="process.responseBody"
        value={details.process.responseBody}
        error={error}
        label="Response Body"
      />

      <CustomInput
        onChange={updateDetails}
        name="process.timeout"
        value={details.process.timeout}
        error={error}
        label="Timeout"
      />
    </>
  );
};

export default WaitForResponseForm;