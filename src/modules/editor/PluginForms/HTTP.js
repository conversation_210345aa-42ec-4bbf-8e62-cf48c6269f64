import React from "react";
import ErrorMessage from "./utils/ErrorMessage";
import CustomInput from "./utils/custom-input";
import CustomSelect from "./utils/custom-select";

const APIForm = (props) => {
  const { details, updateDetails, error } = props;

  const options = [
    {
      value: "GET",
      label: "GET",
    },
    {
      value: "POST",
      label: "POST",
    },

    {
      value: "PUT",
      label: "PUT",
    },

    {
      value: "DELETE",
      label: "DELETE",
    },
  ];

  const handleAddHeader = (event) => {
    event.preventDefault();
    const temp = [
      ...details.process.headers,
      { headerKey: "", headerValue: "" },
    ];
    updateDetails({ target: { value: temp, name: "process.headers" } });
  };

  const handleRemoveHeader = (index) => {
    updateDetails({
      target: {
        value: details.process.headers.filter((_, i) => i !== index),
        name: "process.headers",
      },
    });
  };

  const headerChange = (event, index) => {
    const { name, value } = event.target;
    const copy = [...details.process.headers];
    copy[index][name] = value;
    updateDetails({ target: { value: copy, name: "process.headers" } });
  };

  return (
    <>
      <CustomSelect
        onChange={updateDetails}
        name="process.requestType"
        value={details.process.requestType}
        error={error}
        label="Request Type"
        options={options}
      />

      <CustomInput
        onChange={updateDetails}
        name="process.URL"
        value={details.process.URL}
        error={error}
        label="URL"
      />

      <CustomInput
        onChange={updateDetails}
        name="settings.timeout"
        value={details.settings.timeout}
        error={error}
        label="Timeout"
      />

      <h2 className="font-bold mb-1 input-label">Headers</h2>
      {details.process.headers.map((header, index) => (
        <>
          <div key={index} className="flex items-center mt-2">
            <input
              className="text-md p-2 border border-gray-300 mr-1 rounded "
              style={{ width: "15ch" }}
              type="text"
              name={"headerKey"}
              value={header.headerKey}
              onChange={(event) => headerChange(event, index)}
              placeholder="Key"
            />
            <input
              className="text-md p-2 border border-gray-300 rounded "
              style={{ width: "18ch" }}
              type="text"
              name="headerValue"
              value={header.headerValue}
              onChange={(event) => headerChange(event, index)}
              placeholder="Value"
            />
            {index > 0 && (
              <button
                className="text-red-500"
                onClick={() => handleRemoveHeader(index)}
                style={{ fontSize: "30px", marginLeft: "5px" }}
              >
                -
              </button>
            )}
          </div>
          {/* <ErrorMessage
            name={`process.headers[${index}]`}
            error={error}
            style={{ marginTop: "0" }}
          /> */}
        </>
      ))}
      <button
        className="mt-2 p-1 text-red-500 text-sm text-right"
        onClick={handleAddHeader}
      >
        + Add more
      </button>

      <CustomInput
        onChange={updateDetails}
        name="process.requestBody"
        value={details.process.requestBody}
        error={error}
        label="Request Body"
      />
    </>
  );
};

export default APIForm;
