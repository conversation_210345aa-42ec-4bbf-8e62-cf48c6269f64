import React, { useEffect, useMemo } from 'react';
import { FloatingField } from '@/components/ui/floating-label';
import { useGetIntentItemsQuery, useUpdateIntentItemMutation } from '@/store/api';
import { useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem } from '@/components/ui/form';

export default function AppStartForm() {
  const activeFlowId = useSelector((state: any) => state.flows.activeFlowId);
  const { control, watch, setValue } = useFormContext();

  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;

  const [updateIntent] = useUpdateIntentItemMutation();

  const { data: intentsData } = useGetIntentItemsQuery({
    filter: {
      botId: {
        eq: botId,
      },
    },
  });
  const intentItems = useMemo(
    () =>
      intentsData?.data?.items.map(item => ({
        label: item.name,
        value: item.id,
      })) || [],
    [intentsData]
  );

  const currentIntentId = watch('process.intentId');

  useEffect(() => {
    if (currentIntentId && activeFlowId) {
      const selected = intentItems?.find(item => item.value === currentIntentId);
      if (selected) {
        // Set the intent label as well
        setValue('process.intentLabel', selected.label);

        updateIntent({
          id: currentIntentId,
          flowId: activeFlowId,
          intentId: currentIntentId,
          intentLabel: selected.label,
        });
      }
    }
  }, [currentIntentId, activeFlowId, intentItems, updateIntent, setValue]);

  return (
    <div className="flex-1 px-4 space-y-4 mt-4 overflow-auto">
      <FormField
        control={control}
        name="process.intentId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                as="select"
                label="Label"
                value={field.value || ''}
                onChange={field.onChange}
                options={intentItems}
                className="mt-4"
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
}
