.node-id {
  color: #a7a7a7;
  font-size: 10px;
}
.node-id-value {
  color: #5c5c5c;
  font-size: 10px;
  font-weight: 600;
}
.node-id-wrapper {
  width: fit-content;
  padding: 10px 10px;
}

.input-label {
  font-size: 14px;
  color: #646464;
  font-weight: bold;
}

.form-root {
  box-shadow: 0px 0px 15px #0000000d;
  border: 1px solid #e9e9e9;
  border-radius: 10px;
  z-index: 9999;
}

.form-root input {
  border: 1px solid #cacaca;
  border-radius: 5px;
}

.save-button {
  font-size: 14px;
  text-transform: uppercase;
  margin-top: 40px;
}
.save-button:disabled {
  opacity: 0.2;
}

.title-img-wrapper {
  align-items: center;
}
.title-img-wrapper img {
  height: 24px;
  width: 24px;
}

.modal-content-wrapper {
  height: calc(100% - 36px);
  overflow: auto;
}
.back-drop {
  width: 100%;
  height: 100%;
  position: fixed;
}

.modal-content-wrapper input,
.modal-content-wrapper select,
.modal-content-wrapper button {
  font-size: 14px;
}

.custom-file-upload {
  display: inline-block;

  cursor: pointer;
  color: #3b82f6;
  font-size: 12px;
  margin-bottom: 25px;
  text-decoration: underline;
}

.http-img {
  height: 50px;
  width: 50px;
}
