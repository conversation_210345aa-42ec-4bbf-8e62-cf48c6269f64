import React, { useState } from "react";

function InfoButton(props) {
  const [showInfo, setShowInfo] = useState(false);

  function handleMouseEnter() {
    setShowInfo(true);
  }

  function handleMouseLeave() {
    setShowInfo(false);
  }

  return (
    <div style={{ display: "inline-block" }}>
      <h4 style={{ fontSize: "15px", display: "inline-block", marginRight: "10px", color: "black" ,fontWeight: "bold"}}>{props.heading}</h4>
      <div style={{ display: "inline-block", position: "relative" }}>
        <button 
         style={{ border: '1.5px solid #555', color: '#555', borderRadius: '50%', width: '16px', height: '16px', display: 'flex', justifyContent: 'center', alignItems: 'center', fontSize: '10px', lineHeight: '1' }} 
        onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
          i
        </button>
        {showInfo && (
          <div 
           style={{ position: "absolute", 
                    left: "calc(100% + 10px)", 
                    top: "0", 
                    backgroundColor: "gray",
                    color: "white", 
                    padding: "10px", 
                    whiteSpace: "nowrap",
                    borderRadius: '10px' }}>
            {props.info}
          </div>
        )}
      </div>
    </div>
  );
}

export default InfoButton;
