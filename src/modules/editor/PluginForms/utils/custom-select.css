.custom-select-container {
    position: relative;
    margin-bottom: 16px;
  }
  
  .custom-select-wrapper {
    position: relative;
    cursor: pointer;
  }
  
  .custom-select-display {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    display: flex;
    align-items: center;
  }
  
  .custom-select-display.has-value {
    color: #000;
  }
  
  .hidden-select {
    display: none;
  }
  
  .custom-select-dropdown {
    position: absolute;
    width: 100%;
    background: white;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }
  
  .custom-select-search-input {
    width: calc(100% - 16px);
    padding: 8px;
    margin: 8px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .custom-select-options {
    max-height: 200px;
    overflow-y: auto;
  }
  
  .custom-select-option {
    padding: 8px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .custom-select-option:hover {
    background-color: #f1f1f1;
  }
  
  .custom-select-scroll {
    max-height: 140px;
    overflow-y: auto;
  }
  