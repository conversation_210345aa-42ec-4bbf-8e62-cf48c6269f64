import React, { useState } from "react";
import ErrorMessage from "./ErrorMessage";

const CustomTextArea = ({
  value,
  onChange,
  name,
  error,
  label,
  type = "text",
}) => {
  const [inputValue, setInputValue] = useState("");
  const [isInputFocused, setInputFocused] = useState(false);

  const handleInputChange = (event) => {
    onChange(event);
    setInputValue(event.target.value);
  };

  const handleInputFocus = () => {
    setInputFocused(true);
  };

  const handleInputBlur = () => {
    if (inputValue === "") {
      setInputFocused(false);
    }
  };

  return (
    <div className="relative">
      {value && (
        <label
          className={`absolute left-2 transform ${isInputFocused || value
            ? "top-0 text-xs text-purple-600 mt-1"
            : "top-2"
            } transition-all duration-300`}
          htmlFor="custom-input"
          style={{ fontSize: "10px" }}
        >
          {label}
        </label>
      )}
      <textarea
        id={name}
        type={type}
        name={name}
        className={`mb-4 border border-gray-300 rounded p-2 w-full focus:outline-none focus:border-blue-500 ${value ? "pt-4 pb-1" : ""
          }`}
        value={value}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onBlur={handleInputBlur}
        placeholder={label}
        rows={5}
      />
      <ErrorMessage name={name} error={error} />
    </div>
  );
};

export default CustomTextArea;
