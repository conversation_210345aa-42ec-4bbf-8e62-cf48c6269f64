import { object, string, array, number } from "yup";
import * as Yup from "yup";
import { isValidPhoneNumber } from "libphonenumber-js";

Yup.addMethod(Yup.string, "phoneNumber", function (errorMessage) {
  return this.test(`test-card-type`, errorMessage, function (value) {
    const { path, createError } = this;

    return (
      isValidPhoneNumber(value ?? "") || createError({ path, message: "error" })
    );
  });
});

const urlValidation =
  /^(ftp|http|https):\/\/(?:www\.)?(?:[\w\-]+\.)*([\w\-]+\.[a-zA-Z]+|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(?::\d{1,5})?(\/[\w\-./#?=&]+)?$/


const phoneNumberValidation = /^(?:\+\d{1,3}-?)?\d{3,}-?\d{3,}-?\d{1,}$/;

export const freshdesk = object({
  process: object({
    email: string().email().required(),
    type: string().required(),
    subject: string().required(),
    description: string().required(),
    priority: string().required(),
    status: string().matches(urlValidation).required(),
  }),
  settings: object({
    api_key: string().required(),
    domain: string().matches(urlValidation).required(),
  }),
});

export const addcontact = object({
  process: object({
    name: string().required(),
    phone: string().phoneNumber("error").required(),
    email: string().email().required(),
    address: string().required(),
  }),
  settings: object({
    username: string().required(),
    password: string().required(),
  }),
});

export const whatsapp = object({
  process: object({
    senderId: string().required(),
    message: string().required(),
    receiverNumber: string().required(),
  }),
});

export const sms = object({
  process: object({
    senderAddress: string().required(),
    receiverAddress: string().required(),
    text_message: string().required(),
  }),
});

export const email = object({
  process: object({
    body: string().required(),
    senderID: string().required(),
    subject: string().required(),
    receiverAddress: string().required(),
  }),
});

export const http = object({
  process: object({
    URL: string().matches(urlValidation).required(),
    requestType: string().required(),
    headers: array().of(
      object().shape({
        headerKey: string().required(),
        headerValue: string().required(),
      })
    ),
    requestBody: string().required(),
  }),
  settings: object({
    timeout: string().required(),
  }),
});
export const openai = object({
  process: object({
    prompt: string().required(),
  }),
  settings: object({
    api_key: string().required(),
  }),
});

export const waitforresponse = object({
  process: object({
    responseBody: string().required(),
    timeout: string().required(),
  }),
});

export const script = object({
  process: object({
    responseBody: string().required(),
  }),
});

export const shopify = object({
  process: object({
    tax: string().required(),
    currency: string().required(),
    title: string().required(),
    price: number().required(),
    rate_of_tax: number().required(),
  }),
  settings: object({
    access_token: string().required(),
    domain: string().matches(urlValidation).required(),
  }),
});

export const zendesk = object({
  process: object({
    subject: string().required(),
    description: string().required(),
  }),
  settings: object({
    api_key: string().required(),
    domain: string().matches(urlValidation).required(),
    email: string().email().required(),
  }),
});

export const repeat = object({
  process: object({
    repeat_count: number().required(),
  }),
});

export const menu = object({
  process: object({
    menuItems: array().of(
      object().shape({
        menuName: string().required(),
        menuValue: string().required(),
      })
    ),
    variable_name: string().required(),
  }),
  // menuNavigationOptions: object({
  //   mainMenu: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  //   backwardSkipMenu: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  //   firstPage: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  //   lastPage: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  //   previousPage: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  //   nextPage: object({
  //     code: string().required(),
  //     message: string().required(),
  //   }).required(),
  // }).required(),
});

export const rcs = object({
  process: object({
    senderAddress: string().required(),
    receiverAddress: string().phoneNumber("error").required(),
    text_message: string().required(),
  }),
});

export const network = object({
  process: object({
    URL: string().matches(urlValidation).required(),
    requestBody: string().required(),
  }),
});

const schemaMap = {
  freshdesk,
  sms,
  email,
  http,
  openai,
  shopify,
  zendesk,
  addcontact,
  whatsapp,
  repeat,
  menu,
  rcs,
  network,
  waitforresponse,
};

export const getFormSchema = (type) => {
  return { schema: schemaMap[type] || object({}) };
};
