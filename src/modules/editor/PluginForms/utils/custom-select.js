import React, { useState, useEffect } from "react";
import ErrorMessage from "./ErrorMessage";
import "./custom-select.css";

const CustomSelect = ({ value, onChange, name, error, label, options }) => {
  console.log("[custom] error : "+error);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [filteredOptions, setFilteredOptions] = useState(options);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  const handleSelectChange = (optionValue) => {
    onChange({ target: { value: optionValue, name } }); // Ensure name is passed here
    setIsDropdownOpen(false);
  };

  const handleFilterChange = (event) => {
    const searchValue = event.target.value.toLowerCase();
    setFilteredOptions(
      options.filter((option) =>
        option.label.toLowerCase().includes(searchValue)
      )
    );
  };

  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const nonCreateOptions = filteredOptions.filter(option => option.value !== 'create');
  const createOption = filteredOptions.find(option => option.value === 'create');

  return (
    <div className="relative custom-select-container">
      <div className="custom-select-wrapper" onClick={handleDropdownToggle}>
        <div className={`custom-select-display ${value ? "has-value" : ""}`}>
          <span>{value || label}</span>
        </div>
        <select
          id={name}
          name={name}
          className="hidden-select"
          value={value}
          onChange={(e) => handleSelectChange(e.target.value)}
          readOnly
        >
          <option value="" disabled hidden>
            {label}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
      {isDropdownOpen && (
        <div className="custom-select-dropdown">
          <input
            type="text"
            className="custom-select-search-input"
            placeholder="Search..."
            onChange={handleFilterChange}
            autoFocus
          />
          <div className="custom-select-options">
            {nonCreateOptions.slice(0, 7).map((option) => (
              <div
                key={option.value}
                className="custom-select-option"
                onClick={() => handleSelectChange(option.value)}
              >
                {option.label}
              </div>
            ))}
            {nonCreateOptions.length > 7 && (
              <div className="custom-select-scroll">
                {nonCreateOptions.slice(7).map((option) => (
                  <div
                    key={option.value}
                    className="custom-select-option"
                    onClick={() => handleSelectChange(option.value)}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            )}
          </div>
          {createOption && (
            <div
              className="custom-select-option custom-create-option"
              onClick={() => handleSelectChange(createOption.value)}
            >
              {createOption.label}
            </div>
          )}
        </div>
      )}
      <ErrorMessage name={name} error={error} />
    </div>
  );
};

export default CustomSelect;
