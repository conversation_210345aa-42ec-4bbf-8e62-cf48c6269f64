import React from "react";
import { isError } from "./useValidation";
import { useTranslation } from 'react-i18next';

const ErrorMessage = ({ name, error, style = {} }) => {
  const { t } = useTranslation();
  if (!isError(error, name)) {
    return null;
  }
  return (
    <div
      style={{
        fontSize: "11px",
        color: "red",
        marginTop: "-12px",
        marginBottom: "6px",
        ...style,
      }}
    >
      {t('common.enterValidValue')}
    </div>
  );
};

export default ErrorMessage;
