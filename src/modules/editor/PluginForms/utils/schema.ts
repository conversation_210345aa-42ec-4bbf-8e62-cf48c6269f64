import { z } from 'zod';
import { StencilNodes, StencilNodesType } from '../../utils/constants';
import { isValidPhoneNumber } from 'libphonenumber-js';

// Custom phone number validation
const phoneNumberValidation = z.string().refine(val => isValidPhoneNumber(val ?? ''), {
  message: 'Invalid phone number', //TODO: need to implement translation
});

// Custom URL validation
const urlValidation = z
  .string()
  .regex(
    /^(ftp|http|https):\/\/(?:www\.)?(?:[\w\-]+\.)*([\w\-]+\.[a-zA-Z]+|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(?::\d{1,5})?(\/[\w\-./#?=&]+)?$/,
    'Invalid URL'
  );

export const appStart = z.object({
  process: z.object({
    intentId: z.string().min(1, 'Intent ID is required'),
    intentLabel: z.string().optional(),
    formId: z.string().optional(),
    formConfig: z
      .object({
        formType: z.string().optional(),
        fields: z
          .array(
            z.object({
              name: z.string().optional(),
              type: z.string().optional(),
              label: z.string().optional(),
              required: z.boolean().optional(),
              options: z.array(z.string()).optional(),
            })
          )
          .optional(),
        prompt: z.string().optional(),
      })
      .optional(),
    prompt: z.string().optional(),
  }),
});

export const form = z.object({
  process: z.object({
    formId: z.string().optional(),
    formConfig: z
      .object({
        formType: z.string().optional(),
        fields: z
          .array(
            z.object({
              name: z.string().optional(),
              type: z.string().optional(),
              label: z.string().optional(),
              required: z.boolean().optional(),
              options: z.array(z.string()).optional(),
            })
          )
          .optional(),
        prompt: z.string().optional(),
      })
      .optional(),
    prompt: z.string().optional(),
  }),
});

export const message = z.object({
  process: z.object({
    messageText: z.string().min(1, 'Message text is required'),
    images: z.array(z.string()).optional(),
    videos: z.array(z.string()).optional(),
    files: z.array(z.string()).optional(),
  }),
});

const schemaMap: Record<StencilNodesType, z.ZodTypeAny> = {
  appStart,
  form,
  message,
};

export const getFormSchema = (type: StencilNodesType) => {
  return { schema: schemaMap[type] || z.object({}) };
};
