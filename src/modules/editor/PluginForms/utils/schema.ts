import { z } from 'zod';
import { StencilNodesType } from '../../utils/constants';

export const appStart = z.object({
  process: z.object({
    intentId: z.string().min(1, 'Intent ID is required'),
  }),
});

export const form = z.object({
  process: z.object({
    formId: z.string().optional(),
    formConfig: z
      .object({
        formType: z.string().optional(),
        fields: z
          .array(
            z.object({
              name: z.string().optional(),
              type: z.string().optional(),
              label: z.string().optional(),
              required: z.boolean().optional(),
              options: z.array(z.string()).optional(),
            })
          )
          .optional(),
      })
      .optional(),
  }),
});

export const message = z.object({
  process: z.object({
    messageText: z.string().min(1, 'Message text is required'),
    images: z.array(z.string()).optional(),
    videos: z.array(z.string()).optional(),
    files: z.array(z.string()).optional(),
  }),
});

// Basic schema for other node types
const basicSchema = z.object({
  settings: z.object({
    nodeName: z.string().min(1, 'Node name is required'),
  }),
  process: z.object({}).passthrough(),
});

const schemaMap: Record<StencilNodesType, z.ZodTypeAny> = {
  appStart,
  form,
  message,
  appEnd: basicSchema,
  whatsapp: basicSchema,
  agentTransfer: basicSchema,
  http: basicSchema,
  choice: basicSchema,
  flowConnector: basicSchema,
  language: basicSchema,
  payment: basicSchema,
  script: basicSchema,
  waitDelay: basicSchema,
  interactiveMessage: basicSchema,
  feedback: basicSchema,
  notification: basicSchema,
};

export const getFormSchema = (type: StencilNodesType) => {
  return { schema: schemaMap[type] || basicSchema };
};
