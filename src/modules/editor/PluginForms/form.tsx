import React, { useEffect } from 'react';
import { X, Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DropdownButton from '@/components/dropdownButton';
import { FloatingField } from '@/components/ui/floating-label';
import { useTranslation } from 'react-i18next';
import type { ModuleData } from '../types';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';

interface FormField {
  id: string;
  name: string;
  type: string;
  label: string;
  required?: boolean;
  options?: string[];
}

// type FormBuilderModalProps = {
//   // Removed details, setModuleDetails, updateDetails, error, isEdit
// };

const FIELD_TYPES = [
  { label: 'Text Field', value: 'text' },
  { label: 'Email', value: 'email' },
  { label: 'Select', value: 'select' },
  { label: 'Radio', value: 'radio' },
  { label: 'Checkbox', value: 'checkbox' },
];

export default function FormBuilderModal() {
  const { t } = useTranslation();
  const { control } = useFormContext();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'process.formConfig.fields',
  });

  useEffect(() => {
    if (fields.length === 0) {
      append({
        id: crypto.randomUUID(),
        name: '',
        type: 'text',
        label: '',
      });
    }
  }, [fields, append]);

  const addField = () => {
    append({
      id: crypto.randomUUID(),
      name: '',
      type: 'text',
      label: '',
    });
  };

  const handleRemoveField = (index: number) => {
    remove(index);
  };

  return (
    <>
      {/* Prompt + Validate Required */}
      <div className="px-4 pt-4 space-y-4">
        <FormField
          control={control}
          name="process.prompt"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  label="Prompt"
                  value={field.value}
                  onChange={field.onChange}
                  type="text"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      {/* Field list */}
      <div className="flex-1 px-4 space-y-4 mt-4 overflow-auto">
        {fields.map((field, index) => (
          <div key={field.id} className="flex gap-2 mt-4 items-center">
            <FormField
              control={control}
              name={`process.formConfig.fields.${index}.type`}
              render={({ field: typeField }) => (
                <FormItem>
                  <FormControl>
                    <DropdownButton
                      options={FIELD_TYPES}
                      value={typeField.value}
                      onChange={typeField.onChange}
                      className="w-auto text-xs"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`process.formConfig.fields.${index}.label`}
              render={({ field: labelField }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <FloatingField
                      label="Label"
                      value={labelField.value}
                      onChange={labelField.onChange}
                      className="top-0"
                      type="text"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <Button
              type="button"
              variant="ghost"
              className="text-destructive mt-2"
              onClick={() => handleRemoveField(index)}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        ))}

        {/* Single add button */}
        <div className="mt-4">
          <Button type="button" variant="ghost" className="text-primary" onClick={addField}>
            <Plus className="w-4 h-4 mr-1" />
            {'Add Field'}
          </Button>
        </div>
      </div>
    </>
  );
}