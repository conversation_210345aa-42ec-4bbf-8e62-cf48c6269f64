import React from 'react';
import Default from './PluginForms/Default';
import BaseModal from './PluginForms/BaseModal';
import AppStart from './PluginForms/appStart';
import FlowConnector from './PluginForms/flowConnector';
import Form from './PluginForms/form';
import Message from './PluginForms/message';
import { StencilNodes, StencilNodesType } from './utils/constants';

interface ModalTypeDetails {
  top: number;
  left: number;
}

interface NodeConfigSheetProps {
  type: StencilNodesType;
  id: string;
  handleClose: () => void;
  handleSave: (data: any, id: string, validate?: boolean) => void;
  moduleData: any;
  modalTypeDetails: ModalTypeDetails;
  startUrl?: string;
  isEdit?: boolean;
  isPublishedEnabled?: boolean;
}

// Map of node types to their corresponding components
const nodeComponentMap: Record<StencilNodesType, React.ComponentType> = {
  [StencilNodes.APP_START]: AppStart,
  [StencilNodes.FLOW_CONNECTOR]: FlowConnector,
  [StencilNodes.FORM]: Form,
  [StencilNodes.MESSAGE]: Message,

  // Add other components as they become available
  [StencilNodes.APP_END]: Default,
  [StencilNodes.WHATSAPP]: Default,
  [StencilNodes.AGENT_TRANSFER]: Default,
  [StencilNodes.HTTP]: Default,
  [StencilNodes.CHOICE]: Default,
  [StencilNodes.LANGUAGE]: Default,
  [StencilNodes.PAYMENT]: Default,
  [StencilNodes.SCRIPT]: Default,
  [StencilNodes.WAIT_DELAY]: Default,
  [StencilNodes.INTERACTIVE_MESSAGE]: Default,
  [StencilNodes.FEEDBACK]: Default,
  [StencilNodes.NOTIFICATION]: Default,
};

const nodesArr = Object.values(StencilNodes);

const NodeConfigSheet: React.FC<NodeConfigSheetProps> = ({
  type,
  id,
  handleClose,
  handleSave,
  moduleData,
  modalTypeDetails,
  startUrl,
  isEdit = false,
  isPublishedEnabled = false,
}) => {
  const { top, left } = modalTypeDetails;

  if (!nodesArr.includes(type)) {
    return (
      <Default
        handleSave={handleSave}
        handleClose={handleClose}
        moduleData={moduleData}
        id={id}
        isEdit={isEdit}
        position={{ top, left }}
      />
    );
  }

  const ModuleComponent = nodeComponentMap[type];

  return (
    <BaseModal
      handleSave={handleSave}
      startUrl={startUrl}
      handleClose={handleClose}
      moduleData={moduleData}
      isEdit={isEdit}
      id={id}
      position={{ top, left }}
      type={type}
      isPublishedEnabled={isPublishedEnabled}
    >
      <ModuleComponent />
    </BaseModal>
  );
};

export default NodeConfigSheet;
