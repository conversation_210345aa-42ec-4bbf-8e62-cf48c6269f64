import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetEntitiesQuery, useCreateEntityMutation } from '@/store/api';
import { useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import AddModal from '../components/AddModal';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import RenderButtons from '../components/RenderButtons';
import { CreateEntityRequest } from '@/types';

const createEntitySchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t('entities.validation.nameRequired')),
    intentId: z.string().min(1, t('entities.validation.intentIdRequired')),
    metadata: z.string().optional(),
  });

type EntityFormValues = z.infer<ReturnType<typeof createEntitySchema>>;

const EntitiesTab: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!;
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const {
    data: entitiesData,
    isLoading,
    isError,
  } = useGetEntitiesQuery({
    filter: { botId: { eq: botId } },
  });

  const [createEntity] = useCreateEntityMutation();

  const form = useForm<EntityFormValues>({
    resolver: zodResolver(createEntitySchema(t)),
    defaultValues: {
      name: '',
      intentId: '',
      metadata: '',
    },
  });

  const onSubmit = async (values: EntityFormValues) => {
    try {
      let metadata;
      if (values.metadata) {
        try {
          metadata = JSON.parse(values.metadata);
        } catch (e) {
          form.setError('metadata', { message: t('entities.validation.invalidJson') });
          return;
        }
      }
      const newEntity: CreateEntityRequest = {
        botId,
        intentId: values.intentId,
        name: values.name,
        metadata,
      };
      await createEntity(newEntity).unwrap();
      setIsAddModalOpen(false);
      form.reset();
    } catch (error) {
      console.error('Failed to create entity:', error);
    }
  };

  if (isLoading) return <div>{t('entities.loading')}</div>;
  if (isError) return <div>{t('entities.error')}</div>;

  const entities = entitiesData?.data?.items || [];

  return (
    <div className="p-4">
      <div className="flex justify-end mb-4">
        <AddModal
          title={t('entities.addTitle')}
          open={isAddModalOpen}
          onOpenChange={setIsAddModalOpen}
          trigger={
            <Button onClick={() => setIsAddModalOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> {t('entities.addTitle')}
            </Button>
          }
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('entities.nameLabel')}</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="intentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('entities.intentIdLabel')}</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="metadata"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('entities.metadataLabel')}</FormLabel>
                    <FormControl>
                      <Textarea placeholder={t('entities.metadataPlaceholder')} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <RenderButtons
                handleClose={() => setIsAddModalOpen(false)}
                handleAddClick={form.handleSubmit(onSubmit)}
              />
            </form>
          </Form>
        </AddModal>
      </div>
      <div className="space-y-4">
        {entities.map(entity => (
          <div
            key={entity.id}
            className="bg-background rounded-lg shadow-sm border border-tertiary-200 p-4"
          >
            <h3 className="font-semibold">{entity.name}</h3>
            <p className="text-sm text-tertiary-500">Intent ID: {entity.intentId}</p>
            {entity.metadata && (
              <pre className="mt-2 p-2 bg-tertiary-100 rounded text-sm">
                {JSON.stringify(entity.metadata, null, 2)}
              </pre>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EntitiesTab;
