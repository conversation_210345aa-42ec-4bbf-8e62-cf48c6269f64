import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import LeftPanel from '@/modules/train/components/LeftPanel';
import RightPanel from '@/modules/train/components/RightPanel';
import { useGetFaqCategoriesQuery } from '@/store/api';
import AddCategoryModalTrigger from '@/modules/train/FAQs/AddCategoryModalTrigger';
import { FaqCategory } from '@/types';
import { useSearchParams } from 'react-router-dom';
import RightPanelContent from './RightPanelContent';

const FAQsTab: React.FC = () => {
  const { t } = useTranslation();

  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!; //TODO: we should store the key in a constant and than use it and rm fallback

  const {
    data: categoryList,
    isLoading,
    isError,
  } = useGetFaqCategoriesQuery({
    filter: {
      botId: {
        eq: botId,
      },
    },
  });

  const categories = categoryList?.data?.items || [];
  const [selectedCategory, setSelectedCategory] = useState<FaqCategory | null>(null);

  useEffect(() => {
    if (categories.length && !selectedCategory) {
      setSelectedCategory(categories[0]);
    }
  }, [categories, selectedCategory]);

  const handleSelectCategory = (category: FaqCategory) => {
    setSelectedCategory(category);
  };

  const renderCategoryItem = (category: FaqCategory) => <span>{category.name}</span>;

  if (isLoading) return <div>{t('faqs.loading')}</div>; //TODO: need add right loader
  if (isError) return <div>{t('faqs.loadingError')}</div>; //TODO: need to do proper error handling

  return (
    <div className="flex h-full">
      <LeftPanel
        title={t('faqs.category.title')}
        items={categories}
        selectedItemId={selectedCategory?.id}
        onSelectItem={handleSelectCategory}
        addTrigger={
          <AddCategoryModalTrigger botId={botId} onCategoryAdded={handleSelectCategory} />
        }
        renderItem={renderCategoryItem}
      />

      <RightPanel title={t('faqs.title')}>
        {selectedCategory ? (
          <RightPanelContent selectedCategory={selectedCategory} />
        ) : (
          <div className="flex-1 flex items-center justify-center text-tertiary-500">
            <p>{t('faqs.category.selectToManage')}</p>
          </div>
        )}
      </RightPanel>
    </div>
  );
};

export default FAQsTab;
