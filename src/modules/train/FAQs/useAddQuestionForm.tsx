import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useToast } from '@/hooks/use-toast';
import {
  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useGetTranslationByFaqIdAndLangIdQuery,
} from '@/store/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { createAddQuestionFormSchema, QuestionFormInputs } from '../schema';
import { FaqItem } from '@/types';
import SuccessToastMessage from '@/components/SuccessToastMessage';

interface UseAddQuestionFormProps {
  onClose: () => void;
  categoryId: string;
  botId: string;
  faqItemNode?: FaqItem | null;
  selectedLangId: string;
}

export const useAddQuestionForm = ({
  onClose,
  categoryId,
  botId,
  faqItemNode,
  selectedLangId,
}: UseAddQuestionFormProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { faqId, flowId } = faqItemNode ?? {};

  const defaultValues = {
    questions: [{ value: '' }],
    answer: '',
    langId: selectedLangId || '',
    translateTo: '',
    flowId: flowId || '',
  };

  const formSchema = createAddQuestionFormSchema(t);
  const form = useForm<QuestionFormInputs>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const { watch } = form;
  const currentLangId = watch('langId');
  const isSameLang = currentLangId === faqItemNode?.langId;

  const { data: existingTranslationData, isLoading: isLoadingTranslation } =
    useGetTranslationByFaqIdAndLangIdQuery(
      {
        faqId: faqId || '',
        langId: currentLangId,
      },
      { skip: !faqItemNode?.id || isSameLang }
    );

  const initialData = isSameLang ? faqItemNode : existingTranslationData?.data;

  useEffect(() => {
    if (initialData) {
      form.reset(prevValue => ({
        ...prevValue,
        questions: initialData.questions.map(q => ({ value: q })),
        answer: initialData.answer,
        langId: initialData.langId,
        translateTo: '',
      }));
    } else {
      form.reset({ ...defaultValues, langId: currentLangId });
    }
  }, [initialData, selectedLangId, form]);

  const [createFaqTranslation] = useCreateFaqTranslationMutation();
  const [updateFaqTranslation] = useUpdateFaqTranslationMutation();

  const onSubmit = async (data: QuestionFormInputs) => {
    try {
      if (initialData) {
        await updateFaqTranslation({
          id: initialData.id,
          flowId: data.flowId || undefined,
          questions: data.questions.map(q => q.value),
          answer: data.answer,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('faqs.items.questionsUpdated')} />,
        });
      } else {
        await createFaqTranslation({
          botId,
          categoryId,
          faqId: faqId,
          flowId: data.flowId || undefined,
          langId: data.langId,
          questions: data.questions.map(q => q.value),
          answer: data.answer,
        }).unwrap();
        toast({
          title: <SuccessToastMessage message={t('faqs.items.questionsAdded')} />,
        });
      }

      form.reset();
      onClose();
    } catch (error) {
      //TODO: need to add error toaster
      console.error(`Failed to ${initialData ? 'update' : 'create'} FAQ item:`, error);
    } finally {
      onClose();
    }
  };

  return {
    form,
    onSubmit,
    isLoadingTranslation,
    initialData,
  };
};
