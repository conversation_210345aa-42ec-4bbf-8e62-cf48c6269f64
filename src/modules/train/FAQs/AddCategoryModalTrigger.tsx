import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import AddModal from '@/modules/train/components/AddModal';
import { useCreateFaqCategoryMutation } from '@/store/api';
import { FloatingField } from '@/components/ui/floating-label';
import { CreateFaqCategoryRequest, FaqCategory } from '@/types';
import RenderButtons from '@/modules/train/components/RenderButtons';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { createAddCategoryFormSchema, AddCategoryFormInputs } from '../schema';

interface AddCategoryModalTriggerProps {
  onCategoryAdded: (category: FaqCategory) => void;
  botId: string;
}

function AddCategoryModalTrigger(props: AddCategoryModalTriggerProps) {
  const { onCategoryAdded, botId } = props;
  const { t } = useTranslation();
  const [createCategoryItem] = useCreateFaqCategoryMutation();
  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<AddCategoryFormInputs>({
    resolver: zodResolver(createAddCategoryFormSchema(t)),
    defaultValues: {
      categoryName: '',
    },
  });

  const handleAddCategory = async (data: AddCategoryFormInputs) => {
    try {
      const newCategoryItem: CreateFaqCategoryRequest = {
        name: data.categoryName,
        botId,
      };
      const result = await createCategoryItem(newCategoryItem).unwrap();
      if (result.data) {
        onCategoryAdded(result.data);
      }
      setIsOpen(false);
      form.reset();
    } catch (error) {
      console.error('Failed to create category:', error);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    form.reset();
  };

  return (
    <AddModal
      title={t('faqs.category.addTitle')}
      open={isOpen}
      onOpenChange={setIsOpen}
      trigger={
        <Button
          size="icon"
          className="w-fit h-fit rounded-sm"
          onClick={() => setIsOpen(true)}
          data-testid="add-category-button"
          aria-label="Add category"
        >
          <Plus className="!h-6 !w-6" />
        </Button>
      }
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleAddCategory)} className="space-y-4">
          <FormField
            control={form.control}
            name="categoryName"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <FloatingField
                    id="addInput"
                    label={t('faqs.category.nameLabel')}
                    className="col-span-4"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <RenderButtons
            handleClose={handleClose}
            handleAddClick={form.handleSubmit(handleAddCategory)}
          />
        </form>
      </Form>
    </AddModal>
  );
}

export default AddCategoryModalTrigger;
