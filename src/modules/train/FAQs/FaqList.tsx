import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import QuestionCard from './QuestionCard';
import { FaqCategory, FaqItem } from '@/types';
import { useGetFaqsByCategoryAndLanguageQuery, useDeleteFaqTranslationMutation } from '@/store/api';
import AddModal from '../components/AddModal';
import AddQuestionForm from './AddQuestionForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';

interface FaqListProps {
  selectedCategory: FaqCategory;
  language: string;
  AddQuestionModal: React.ReactNode;
}

const FaqList: React.FC<FaqListProps> = ({ selectedCategory, language, AddQuestionModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [faqToEdit, setFaqToEdit] = useState<FaqItem | null>(null);

  const { data, error, isLoading, refetch } = useGetFaqsByCategoryAndLanguageQuery(
    {
      categoryId: selectedCategory.id,
      langId: language,
    },
    {
      skip: !language,
    }
  );

  const [deleteFaqTranslation] = useDeleteFaqTranslationMutation();

  const handleEdit = (faqItem: FaqItem) => {
    setFaqToEdit(faqItem);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (faqItem: FaqItem) => {
    try {
      await deleteFaqTranslation({ id: faqItem.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('faqs.items.questionsDeleted')} />,
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete FAQ item:', error);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setFaqToEdit(null);
  };

  if (isLoading) {
    return <div className="text-tertiary-700">{t('faqs.items.loading')}</div>;
  }

  if (error) {
    return <div className="text-error-500">{t('faqs.items.loadingError')}</div>;
  }

  const faqItems = data?.data?.items;

  if (!faqItems?.length)
    return (
      <EmptyState title={t('faqs.items.startAdding')} description={t('common.nothingToShow')}>
        <div className="mt-5">{AddQuestionModal}</div>
      </EmptyState>
    );

  return (
    <div className="space-y-4 overflow-auto h-full">
      {faqItems.map(item => (
        <QuestionCard key={item.id} faqItem={item} onEdit={handleEdit} onDelete={handleDelete} />
      ))}

      {isEditModalOpen && (
        <AddModal
          title={t('faqs.items.editTitle')}
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          className="sm:max-w-[850px]"
        >
          <AddQuestionForm
            categoryId={selectedCategory.id}
            botId={selectedCategory.botId}
            onClose={handleCloseEditModal}
            faqItemNode={faqToEdit}
            selectedLangId={language}
          />
        </AddModal>
      )}
    </div>
  );
};

export default FaqList;
