import * as z from 'zod';
import { TFunction } from 'i18next';

export const createAddQuestionFormSchema = (t: TFunction) =>
  z.object({
    questions: z
      .array(z.object({ value: z.string().min(1, t('faqs.items.questionEmpty')) }))
      .min(1, t('faqs.items.atLeastOne')),
    answer: z.string().min(1, t('faqs.items.answerEmpty')),
    translateTo: z.string().optional(),
    flowId: z.string().optional(),
    langId: z.string(),
  });

export type QuestionFormInputs = z.infer<ReturnType<typeof createAddQuestionFormSchema>>;

export const createAddIntentFormSchema = (t: TFunction) =>
  z.object({
    intentName: z.string().min(1, { message: t('intents.nameRequired') }),
  });

export type AddIntentFormValues = z.infer<ReturnType<typeof createAddIntentFormSchema>>;

export const createAddUtteranceFormSchema = (t: TFunction) =>
  z.object({
    text: z.string().min(1, t('intents.utterances.emptyError')),
    langId: z.string(),
    translateTo: z.string().optional(),
  });

export type UtteranceForm = z.infer<ReturnType<typeof createAddUtteranceFormSchema>>;

export const createAddCategoryFormSchema = (t: TFunction) =>
  z.object({
    categoryName: z.string().min(1, t('faqs.category.nameRequired')),
  });

export type AddCategoryFormInputs = z.infer<ReturnType<typeof createAddCategoryFormSchema>>;
