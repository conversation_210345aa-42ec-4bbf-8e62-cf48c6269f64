import EmptyState from '@/components/EmptyState';
import {
  useGetIntentUtteranceTranslationsQuery,
  useDeleteIntentUtteranceTranslationMutation,
} from '@/store/api';
import { IntentItem, IntentUtteranceTranslation } from '@/types';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import UtteranceCard from './UtteranceCard';
import AddModal from '../components/AddModal';
import AddUtteranceForm from './AddUtteranceForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';

interface IProps {
  selectedIntent: IntentItem;
  language: string;
  AddUtteranceModal: React.ReactNode;
}

const UtteranceList: React.FC<IProps> = ({ selectedIntent, language, AddUtteranceModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [utteranceToEdit, setUtteranceToEdit] = useState<IntentUtteranceTranslation | null>(null);

  const { data, error, isLoading, refetch } = useGetIntentUtteranceTranslationsQuery(
    {
      intentId: selectedIntent.id,
      langId: language,
    },
    { skip: !language }
  );

  const [deleteIntentUtteranceTranslation] = useDeleteIntentUtteranceTranslationMutation();

  const handleEdit = (utterance: IntentUtteranceTranslation) => {
    setUtteranceToEdit(utterance);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (utterance: IntentUtteranceTranslation) => {
    try {
      await deleteIntentUtteranceTranslation({ id: utterance.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('intents.utterances.utteranceDeleted')} />,
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete utterance:', error);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setUtteranceToEdit(null);
  };

  if (isLoading) {
    return <div className="text-tertiary-700">{t('intents.utterances.loading')}</div>;
  }

  if (error) {
    return <div className="text-error-500">{t('intents.utterances.loadingError')}</div>;
  }

  const utteranceItems = data?.data?.items;

  if (!utteranceItems?.length)
    return (
      <EmptyState
        title={t('intents.utterances.startAdding')}
        description={t('common.nothingToShow')}
      >
        <div className="mt-5">{AddUtteranceModal}</div>
      </EmptyState>
    );

  return (
    <div className="space-y-4 overflow-auto h-full">
      {utteranceItems.map(utterance => (
        <UtteranceCard
          key={utterance.id}
          utteranceTranslation={utterance}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      ))}

      {isEditModalOpen && (
        <AddModal
          title={t('intents.utterances.editTitle')}
          open={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          className="sm:max-w-[850px]"
        >
          <AddUtteranceForm
            intentId={selectedIntent.id}
            onClose={handleCloseEditModal}
            utteranceTranslationNode={utteranceToEdit}
            selectedLangId={language}
          />
        </AddModal>
      )}
    </div>
  );
};

export default UtteranceList;
