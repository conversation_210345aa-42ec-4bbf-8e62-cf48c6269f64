import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import LeftPanel from '@/modules/train/components/LeftPanel';
import RightPanel from '@/modules/train/components/RightPanel';
import AddModal from '@/modules/train/components/AddModal';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import EmptyState from '@/components/EmptyState';
import { useGetIntentItemsQuery } from '@/store/api';
import AddIntentForm from './AddIntentForm';
import { IntentItem } from '@/types';
import { useSearchParams } from 'react-router-dom';
import RightPanelContent from './RightPanelContent';
import TagBox from '@/components/TagBox';

const IntentUtterancesTab: React.FC = () => {
  const { t } = useTranslation();

  const [searchParams] = useSearchParams();
  const botId = searchParams.get('id')!; //TODO: we should store the key in a constant and than use it and rm fallback

  const {
    data: intentsData,
    isLoading,
    error,
  } = useGetIntentItemsQuery({
    filter: {
      botId: {
        eq: botId,
      },
    },
  });

  const intents = intentsData?.data?.items || [];
  const [selectedIntent, setSelectedIntent] = useState<IntentItem | null>(null);
  const [isAddIntentModalOpen, setIsAddIntentModalOpen] = useState(false);

  const onClose = async () => {
    setIsAddIntentModalOpen(false);
  };

  useEffect(() => {
    if (intents.length && !selectedIntent) {
      setSelectedIntent(intents[0]);
    }
  }, [intents, selectedIntent]);

  if (isLoading) return <div>{t('intents.loading')}</div>;
  if (error) return <div>{t('intents.loadingError')}</div>;

  const renderIntentItem = (intent: IntentItem) => (
    <>
      <span>{intent.name}</span>
      {!intent.flowId && <TagBox text={t('intents.noFlowsConnected')} />}
    </>
  );

  const addIntentTrigger = (
    <AddModal
      title={t('intents.addTitle')}
      open={isAddIntentModalOpen}
      onOpenChange={setIsAddIntentModalOpen}
      trigger={
        <Button size="icon" className="w-fit h-fit rounded-sm" data-testid="add-category-button">
          <Plus className="!h-6 !w-6" />
        </Button>
      }
    >
      <AddIntentForm onClose={onClose} botId={botId} />
    </AddModal>
  );

  const addIntentButtonInEmptyState = (
    <AddModal
      title={t('intents.addTitle')}
      open={isAddIntentModalOpen}
      onOpenChange={setIsAddIntentModalOpen}
      trigger={
        <Button variant="outline" className="mt-4" onClick={() => setIsAddIntentModalOpen(true)}>
          {t('intents.addTitle')}
        </Button>
      }
    >
      <AddIntentForm onClose={onClose} botId={botId} />
    </AddModal>
  );

  if (intents.length === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center">
        <EmptyState title={t('intents.startAdding')} description={t('common.nothingToShow')}>
          {addIntentButtonInEmptyState}
        </EmptyState>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      <LeftPanel
        title={t('intents.title')}
        items={intents}
        selectedItemId={selectedIntent?.id}
        onSelectItem={setSelectedIntent}
        addTrigger={addIntentTrigger}
        renderItem={renderIntentItem}
      />

      <RightPanel title={t('intents.utterances.title')}>
        {selectedIntent ? (
          <RightPanelContent selectedIntent={selectedIntent} />
        ) : (
          <div className="flex-1 flex items-center justify-center text-tertiary-500">
            <p>{t('intents.selectToManage')}</p>
          </div>
        )}
      </RightPanel>
    </div>
  );
};

export default IntentUtterancesTab;
