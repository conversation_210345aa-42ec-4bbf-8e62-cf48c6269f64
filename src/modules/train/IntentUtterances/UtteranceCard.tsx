import { IntentUtteranceTranslation } from '@/types';
import React from 'react';
import ActionDropdown from '../components/ActionDropdown';
import TagBox from '@/components/TagBox';

interface IProps {
  utteranceTranslation: IntentUtteranceTranslation;
  onEdit: (utterance: IntentUtteranceTranslation) => void;
  onDelete: (utterance: IntentUtteranceTranslation) => void;
}

const UtteranceCard: React.FC<IProps> = ({ utteranceTranslation, onEdit, onDelete }) => {
  return (
    <div
      className="bg-background rounded-lg shadow-sm border border-tertiary-200 p-4 mb-4 flex justify-between items-start"
      data-testid="question-card"
    >
      <h3 className="text-tertiary-700">{utteranceTranslation.text}</h3>

      <div className="flex gap-3">
        <div className="flex gap-2">
          {utteranceTranslation.availableLanguages?.map(lang => (
            <TagBox key={lang.id} text={lang.name} />
          ))}
        </div>
        <div className="flex items-center space-x-4">
          <ActionDropdown
            onEdit={() => onEdit(utteranceTranslation)}
            onDelete={() => onDelete(utteranceTranslation)}
          />
        </div>
      </div>
    </div>
  );
};

export default UtteranceCard;
