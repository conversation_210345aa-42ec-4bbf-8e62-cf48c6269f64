import React from 'react';
import { useTranslation } from 'react-i18next';
import { FloatingField } from '@/components/ui/floating-label';
import RenderButtons from '@/modules/train/components/RenderButtons';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { createAddIntentFormSchema, AddIntentFormValues } from '../schema';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { useCreateIntentItemMutation } from '@/store/api';
import { CreateIntentItemRequest } from '@/types';

interface AddIntentFormProps {
  onClose: () => void;
  botId: string;
}

function AddIntentForm({ botId, onClose }: AddIntentFormProps) {
  const { t } = useTranslation();
  const [createIntent] = useCreateIntentItemMutation();

  const form = useForm<AddIntentFormValues>({
    resolver: zodResolver(createAddIntentFormSchema(t)),
    defaultValues: {
      intentName: '',
    },
  });

  const onSubmit = async (data: AddIntentFormValues) => {
    try {
      const newIntent: CreateIntentItemRequest = {
        name: data.intentName,
        botId,
      };
      await createIntent(newIntent).unwrap();
    } catch (error) {
      console.error('Failed to create intent:', error);
    }
    onClose();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="intentName"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <FloatingField
                  id="addInput"
                  label={t('intents.nameLabel')}
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <RenderButtons handleClose={onClose} handleAddClick={form.handleSubmit(onSubmit)} />
      </form>
    </Form>
  );
}

export default AddIntentForm;
