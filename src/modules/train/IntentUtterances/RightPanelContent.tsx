import React, { useState } from 'react';
import { Import, SquareArrowOutUpRight } from 'lucide-react';
import AddModal from '../components/AddModal';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

import { IntentItem } from '@/types';
import AddUtteranceForm from './AddUtteranceForm';
import LanguageDropdown from '@/components/LanguageDropdown';
import UtteranceList from './UtteranceList';

interface RightPanelContentProps {
  selectedIntent: IntentItem;
}

const RightPanelContent: React.FC<RightPanelContentProps> = ({ selectedIntent }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { t } = useTranslation();
  const [language, setLanguage] = useState('');

  const AddUtteranceModal = (
    <AddModal
      title={t('intents.utterances.addTitle')}
      open={isModalOpen}
      onOpenChange={setIsModalOpen}
      className="sm:max-w-[850px]"
      trigger={
        <Button variant="outline" onClick={() => setIsModalOpen(true)}>
          {t('intents.utterances.addTitle')}
        </Button>
      }
    >
      <AddUtteranceForm
        intentId={selectedIntent.id}
        onClose={() => setIsModalOpen(false)}
        selectedLangId={language}
      />
    </AddModal>
  );

  return (
    <div className="rounded-lg shadow-sm flex-1 h-0 flex flex-col">
      <div className="flex justify-between space-x-4 items-center mb-6">
        <LanguageDropdown onChange={setLanguage} />

        <div className="flex mb-4 gap-7 items-center">
          <div className="flex gap-4 items-end">
            <Import className="w-6 h-6 text-tertiary-600 cursor-pointer" />
            <SquareArrowOutUpRight className="w-7 h-7 text-tertiary-600 cursor-pointer" />
          </div>
          {AddUtteranceModal}
        </div>
      </div>

      <UtteranceList
        selectedIntent={selectedIntent}
        language={language}
        AddUtteranceModal={AddUtteranceModal}
      />
    </div>
  );
};

export default RightPanelContent;
