import { cn } from '@/lib/utils';
import React, { ReactNode } from 'react';

interface LeftPanelProps<T extends { id: string }> {
  title: string;
  items: T[];
  selectedItemId: string | undefined;
  onSelectItem: (item: T) => void;
  addTrigger: ReactNode;
  renderItem: (item: T) => ReactNode;
}

function LeftPanel<T extends { id: string }>({
  title,
  items,
  selectedItemId,
  onSelectItem,
  addTrigger,
  renderItem,
}: LeftPanelProps<T>) {
  return (
    <div className="flex-[0.4] border-r border-tertiary-200 py-4 flex flex-col">
      <div className="flex items-center justify-between mb-2 px-4">
        <h2 className="text-lg">{title}</h2>
        <div className="flex items-center space-x-2">{addTrigger}</div>
      </div>
      <div className="flex-1 overflow-y-auto">
        <ul>
          {items.map(item => (
            <li
              key={item.id}
              className={cn(
                'px-3 py-5 rounded-sm cursor-pointer flex justify-between items-center border-b',
                selectedItemId === item.id ? 'bg-primary-100 bg-opacity-20' : 'hover:bg-tertiary-50'
              )}
              onClick={() => onSelectItem(item)}
            >
              {renderItem(item)}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default LeftPanel;
