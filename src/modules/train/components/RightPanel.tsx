import React, { ReactNode } from 'react';
import EmptyState from '@/components/EmptyState';

interface RightPanelProps {
  title: string;
  children?: ReactNode;
}

const RightPanel: React.FC<RightPanelProps> = ({ title, children }) => {
  return (
    <div className="flex-1 w-0 px-5 py-6 flex flex-col">
      <h2 className="text-lg  mb-4">{title}</h2>
      {children}
    </div>
  );
};

export default RightPanel;
