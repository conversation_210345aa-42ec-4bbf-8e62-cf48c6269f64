import { Button } from '@/components/ui/button';
import { LoaderCircle } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useFormContext } from 'react-hook-form';

interface RenderButtonsProps {
  handleClose: () => void;
  handleAddClick: () => void;
  isEdit?: boolean;
}

function RenderButtons({ handleClose, handleAddClick, isEdit }: RenderButtonsProps) {
  const { t } = useTranslation();
  const { formState } = useFormContext();
  const isLoading = formState.isSubmitting;
  const disabled = !formState.isValid || !formState.isDirty || formState.isSubmitting;

  return (
    <div className="flex justify-end gap-2 p-6">
      <Button className="w-24" variantColor={'tertiary'} variant="outline" onClick={handleClose}>
        {t('common.cancel')}
      </Button>
      <Button
        className="w-24"
        type="submit"
        aria-label="Form Submit Button"
        disabled={disabled}
        onClick={handleAddClick}
      >
        {isLoading && <LoaderCircle className="w-4 h-4 animate-spin" />}
        {isEdit ? t('common.update') : t('common.add')}
      </Button>
    </div>
  );
}

export default RenderButtons;
