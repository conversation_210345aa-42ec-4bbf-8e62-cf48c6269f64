import { render, screen, waitFor } from '@/test/utils';
import userEvent from '@testing-library/user-event';
import * as storeApi from '@/store/api';
import IntentUtterancesTab from '../IntentUtterances';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/store/api', async () => {
  const actual = await vi.importActual('@/store/api');
  return {
    ...actual,
    useGetIntentItemsQuery: vi.fn(),
    useCreateIntentItemMutation: vi.fn(),
    useGetIntentUtteranceTranslationsQuery: vi.fn(),
    useCreateIntentUtteranceTranslationMutation: vi.fn(),
    useGetBotLanguagesQuery: vi.fn(),
    useGetLanguagesQuery: vi.fn(),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams('?id=bot1'), vi.fn()],
  };
});

describe('IntentUtterancesTab', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(storeApi.useGetIntentItemsQuery).mockReturnValue({
      data: {
        data: {
          items: [
            { id: 'intent1', name: 'Greeting', botId: 'bot1', type: 'INTENT' },
            { id: 'intent2', name: 'Farewell', botId: 'bot1', type: 'INTENT' },
          ],
          totalCount: 2,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    vi.mocked(storeApi.useGetIntentUtteranceTranslationsQuery).mockReturnValue({
      data: {
        data: {
          items: [
            { id: 'item1', text: 'Hello there', intentId: 'intent1', langId: 'en' },
            { id: 'item2', text: 'Hi', intentId: 'intent1', langId: 'en' },
          ],
          totalCount: 2,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    const createIntentItemMock = vi.fn().mockResolvedValue({
      data: {
        id: 'newIntent',
        name: 'New Intent',
        botId: 'bot1',
        type: 'INTENT',
      },
    });
    vi.mocked(storeApi.useCreateIntentItemMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: createIntentItemMock,
      })),
      { isLoading: false, error: false },
    ] as any);

    const createIntentUtteranceTranslationMock = vi.fn().mockResolvedValue({
      data: {
        id: 'newItem',
        text: 'New utterance',
        intentId: 'intent1',
        langId: 'en',
      },
    });
    vi.mocked(storeApi.useCreateIntentUtteranceTranslationMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: createIntentUtteranceTranslationMock,
      })),
      { isLoading: false, error: false },
    ] as any);

    vi.mocked(storeApi.useGetBotLanguagesQuery).mockReturnValue({
      data: {
        data: {
          items: [{ id: 'en', name: 'English', code: 'en', langId: 'en', isDefault: true }],
        },
      },
      isLoading: false,
      isError: false,
    } as any);

    vi.mocked(storeApi.useGetLanguagesQuery).mockReturnValue({
      data: {
        data: {
          items: [{ id: 'en', name: 'English', code: 'en' }],
        },
      },
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    });
  });

  it('renders IntentUtterancesTab and displays intents', async () => {
    render(<IntentUtterancesTab />);

    expect(screen.getByText('Intents')).toBeInTheDocument();
    expect(screen.getByText('Utterances')).toBeInTheDocument();
    expect(screen.getByText('Greeting')).toBeInTheDocument();
    expect(screen.getByText('Farewell')).toBeInTheDocument();
  });

  it('allows selecting an intent and displays utterances', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
      expect(screen.getByText('Hi')).toBeInTheDocument();
    });
  });

  it('allows adding a new intent', async () => {
    render(<IntentUtterancesTab />);

    const addIntentButton = screen.getByTestId('add-category-button');
    await userEvent.click(addIntentButton);

    await waitFor(() => {
      expect(screen.getByText('ADD INTENT')).toBeInTheDocument();
    });

    const intentNameInput = screen.getByLabelText('Intent name');

    await userEvent.type(intentNameInput, 'New Test Intent');
    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(screen.queryByText('ADD INTENT')).not.toBeInTheDocument();
    });
  });

  it('displays empty state when no intents are present', async () => {
    vi.mocked(storeApi.useGetIntentItemsQuery).mockReturnValue({
      data: {
        data: {
          items: [],
          totalCount: 0,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    expect(screen.getByText('Start adding intents')).toBeInTheDocument();
    expect(screen.getByText('Nothing to show')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'ADD INTENT' })).toBeInTheDocument();
  });

  it('handles loading state', () => {
    vi.mocked(storeApi.useGetIntentItemsQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: false,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    expect(screen.getByText('Loading intents...')).toBeInTheDocument();
  });

  it('handles error state', () => {
    vi.mocked(storeApi.useGetIntentItemsQuery).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: true,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    expect(screen.getByText('Error loading intents.')).toBeInTheDocument();
  });

  it('displays message when no intent is selected', () => {
    vi.mocked(storeApi.useGetIntentItemsQuery).mockReturnValue({
      data: {
        data: {
          items: [{ id: 'intent1', name: 'Greeting', botId: 'bot1', type: 'INTENT' }],
          totalCount: 1,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    vi.mocked(storeApi.useGetIntentUtteranceTranslationsQuery).mockReturnValue({
      data: {
        data: {
          items: [],
          totalCount: 0,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    expect(screen.getByText('Start adding utterances')).toBeInTheDocument();
  });

  it('closes add intent modal on close button click', async () => {
    render(<IntentUtterancesTab />);

    const addIntentButton = screen.getByTestId('add-category-button');
    await userEvent.click(addIntentButton);

    await waitFor(() => {
      expect(screen.getByText('ADD INTENT')).toBeInTheDocument();
    });

    const cancelButton = screen.getByRole('button', { name: 'CANCEL' });
    await userEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('ADD INTENT')).not.toBeInTheDocument();
    });
  });

  it('handles intent creation failure', async () => {
    const mockCreateIntent = vi.fn(() => ({
      unwrap: () => Promise.reject(new Error('Failed to create intent')),
    }));

    vi.mocked(storeApi.useCreateIntentItemMutation).mockReturnValue([
      mockCreateIntent,
      { isLoading: false, error: true },
    ] as any);

    render(<IntentUtterancesTab />);

    const addIntentButton = screen.getByTestId('add-category-button');
    await userEvent.click(addIntentButton);

    const intentNameInput = screen.getByLabelText('Intent name');
    await userEvent.type(intentNameInput, 'Failing Intent');

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(mockCreateIntent).toHaveBeenCalled();
      expect(screen.queryByText('ADD INTENT')).not.toBeInTheDocument();
    });
  });

  it('allows adding utterances', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
    });

    const addUtteranceButton = screen.getByRole('button', { name: 'ADD UTTERANCE' });
    await userEvent.click(addUtteranceButton);

    expect(screen.getByRole('heading', { name: 'ADD UTTERANCE' })).toBeInTheDocument();

    const utteranceInput = screen.getByPlaceholderText('Enter utterance');

    await userEvent.type(utteranceInput, 'Hello there!');
    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    expect(screen.queryByRole('heading', { name: 'ADD UTTERANCE' })).not.toBeInTheDocument();
  });

  it('handles utterance creation with validation', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
    });

    const addUtteranceButton = screen.getByRole('button', { name: 'ADD UTTERANCE' });
    await userEvent.click(addUtteranceButton);

    expect(screen.getByRole('heading', { name: 'ADD UTTERANCE' })).toBeInTheDocument();

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    expect(screen.getByRole('heading', { name: 'ADD UTTERANCE' })).toBeInTheDocument();
  });

  it('handles language dropdown interaction', async () => {
    render(<IntentUtterancesTab />, {
      preloadedState: {
        auth: {
          accessToken: 'mockAccessToken',
          refreshToken: 'mockRefreshToken',
          expires: 1234567890,
          isLoggedIn: true,
        },
      },
    });

    await userEvent.click(screen.getByText('Greeting'));

    expect(screen.getByText('Hello there')).toBeInTheDocument();

    expect(screen.getByTestId('country-flag')).toBeInTheDocument();
  });

  it('displays no flows connected message for intents without flowId', async () => {
    render(<IntentUtterancesTab />);

    expect(screen.getAllByText('No flows connected')).toHaveLength(2);
  });

  it('handles intent selection properly', async () => {
    render(<IntentUtterancesTab />);

    const greetingIntent = screen.getByText('Greeting');
    expect(greetingIntent.closest('li')).toHaveClass('bg-primary-100');

    await userEvent.click(screen.getByText('Farewell'));

    await waitFor(() => {
      expect(screen.getByText('Farewell').closest('li')).toHaveClass('bg-primary-100');
    });
  });

  it('handles utterance form validation', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
    });

    const addUtteranceButton = screen.getByRole('button', { name: 'ADD UTTERANCE' });
    await userEvent.click(addUtteranceButton);

    expect(screen.getByRole('heading', { name: 'ADD UTTERANCE' })).toBeInTheDocument();

    expect(screen.getByPlaceholderText('Enter utterance')).toBeInTheDocument();
    expect(screen.getByText('Generate')).toBeInTheDocument();
    expect(screen.getByText('Translate to:')).toBeInTheDocument();
    expect(screen.getByText('TRANSLATE')).toBeInTheDocument();
  });

  it('handles utterance creation success', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
    });

    const addUtteranceButton = screen.getByRole('button', { name: 'ADD UTTERANCE' });
    await userEvent.click(addUtteranceButton);

    const utteranceInput = screen.getByPlaceholderText('Enter utterance');
    await userEvent.type(utteranceInput, 'Good Morning');

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    expect(
      screen.queryByRole('heading', {
        name: 'ADD UTTERANCE',
      })
    ).not.toBeInTheDocument();
  });

  it('handles utterance creation failure', async () => {
    const mockCreateIntentUtteranceTranslation = vi
      .fn()
      .mockRejectedValue(new Error('Failed to create utterance'));
    vi.mocked(storeApi.useCreateIntentUtteranceTranslationMutation).mockReturnValue([
      vi.fn().mockImplementation(() => ({
        unwrap: mockCreateIntentUtteranceTranslation,
      })),
      { isLoading: false, error: true },
    ] as any);

    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
    });

    const addUtteranceButton = screen.getByRole('button', { name: 'ADD UTTERANCE' });
    await userEvent.click(addUtteranceButton);

    const utteranceInput = screen.getByPlaceholderText('Enter utterance');
    await userEvent.type(utteranceInput, 'Good Morning');

    const addButton = screen.getByLabelText('Form Submit Button');
    await userEvent.click(addButton);

    await waitFor(() => {
      expect(mockCreateIntentUtteranceTranslation).toHaveBeenCalled();
    });
  });

  it('displays utterance cards correctly', async () => {
    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Hello there')).toBeInTheDocument();
      expect(screen.getByText('Hi')).toBeInTheDocument();
    });
  });

  it('handles empty utterances state', async () => {
    vi.mocked(storeApi.useGetIntentUtteranceTranslationsQuery).mockReturnValue({
      data: {
        data: {
          items: [],
          totalCount: 0,
        },
      },
      isLoading: false,
      error: false,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Start adding utterances')).toBeInTheDocument();
    });
  });

  it('handles utterances loading state', async () => {
    vi.mocked(storeApi.useGetIntentUtteranceTranslationsQuery).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: false,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    await waitFor(() => {
      expect(screen.getByText('Loading utterances...')).toBeInTheDocument();
    });
  });

  it('handles utterances error state', async () => {
    vi.mocked(storeApi.useGetIntentUtteranceTranslationsQuery).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: true,
      refetch: vi.fn(),
    } as any);

    render(<IntentUtterancesTab />);

    await userEvent.click(screen.getByText('Greeting'));

    expect(screen.getByText('Error loading utterances.')).toBeInTheDocument();
  });
});
