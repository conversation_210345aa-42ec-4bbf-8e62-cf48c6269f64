import { render, screen, waitFor } from '@/test/utils';
import TrainTabContent from '../index';
import { trainSubTabs } from '../config';
import { describe, expect, it, vi } from 'vitest';
import userEvent from '@testing-library/user-event';

vi.mock('../Entities', () => ({ default: () => <div>Entities Content</div> }));
vi.mock('../FAQs', () => ({ default: () => <div>FAQs Tab Content</div> }));
vi.mock('../IntentUtterances', () => ({ default: () => <div>Intent Utterances Tab Content</div> }));
vi.mock('../SmallTalk', () => ({ default: () => <div>Small Talk Content</div> }));
vi.mock('../Synonyms', () => ({ default: () => <div>Synonyms Content</div> }));
vi.mock('../TrainFromLogs', () => ({ default: () => <div>Train from Logs Content</div> }));

describe('TrainTabContent', () => {
  it('renders the tabs and defaults to Intent Utterances tab', () => {
    render(<TrainTabContent />);

    expect(screen.getByRole('tab', { name: 'Utterances' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Entities' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'FAQs' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Synonyms' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Small Talk' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'Train from Logs' })).toBeInTheDocument();

    expect(screen.getByText('Intent Utterances Tab Content')).toBeInTheDocument();
    expect(screen.queryByText('FAQs Tab Content')).not.toBeInTheDocument();
  });

  it('switches to FAQs tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'FAQs' }));

    await waitFor(() => {
      expect(screen.getByText('FAQs Tab Content')).toBeInTheDocument();
      expect(screen.queryByText('Intent Utterances Tab Content')).not.toBeInTheDocument();
    });
  });

  it('switches to Entities tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Entities' }));

    await waitFor(() => {
      expect(screen.getByText('Entities Content')).toBeInTheDocument();
    });
  });

  it('switches to Synonyms tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Synonyms' }));

    await waitFor(() => {
      expect(screen.getByText('Synonyms Content')).toBeInTheDocument();
    });
  });

  it('switches to Small Talk tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Small Talk' }));

    await waitFor(() => {
      expect(screen.getByText('Small Talk Content')).toBeInTheDocument();
    });
  });

  it('switches to Train from Logs tab when clicked', async () => {
    render(<TrainTabContent />);

    await userEvent.click(screen.getByRole('tab', { name: 'Train from Logs' }));

    await waitFor(() => {
      expect(screen.getByText('Train from Logs Content')).toBeInTheDocument();
    });
  });

  it('has correct tab configuration', () => {
    expect(trainSubTabs).toHaveLength(6);
    expect(trainSubTabs[0].id).toBe('Intent Utterances');
    expect(trainSubTabs[1].id).toBe('Entities');
    expect(trainSubTabs[2].id).toBe('FAQs');
    expect(trainSubTabs[3].id).toBe('Synonyms');
    expect(trainSubTabs[4].id).toBe('Small Talk');
    expect(trainSubTabs[5].id).toBe('Train from Logs');
  });

  it('maintains active tab state correctly', async () => {
    render(<TrainTabContent />);

    expect(screen.getByRole('tab', { name: 'Utterances' })).toHaveAttribute(
      'aria-selected',
      'true'
    );
    expect(screen.getByRole('tab', { name: 'FAQs' })).toHaveAttribute('aria-selected', 'false');

    await userEvent.click(screen.getByRole('tab', { name: 'FAQs' }));

    await waitFor(() => {
      expect(screen.getByRole('tab', { name: 'FAQs' })).toHaveAttribute('aria-selected', 'true');
      expect(screen.getByRole('tab', { name: 'Utterances' })).toHaveAttribute(
        'aria-selected',
        'false'
      );
    });
  });

  it('renders all tab components correctly', () => {
    render(<TrainTabContent />);

    
    trainSubTabs.forEach(tab => {
      const tabElement = screen.getByRole('tab', {
        name: tab.labelKey.includes('intents.utterances') ? 'Utterances' : tab.id,
      });
      expect(tabElement).toBeInTheDocument();
    });
  });
});
