import { createSlice, createAsyncThunk, type PayloadAction } from "@reduxjs/toolkit"

export interface Flow {
  id: string
  name: string
  type: "Default" | "Custom"
  nodes: any[]
  connections: any[]
  isActive: boolean
}

interface FlowsState {
  flows: Flow[]
  activeFlowId: string | null
  loading: boolean
  error: string | null
}

const initialState: FlowsState = {
  flows: [
    {
      id: "welcome",
      name: "Welcome",
      type: "Default",
      nodes: [],
      connections: [],
      isActive: false,
    },
    {
      id: "fallback",
      name: "Fallback",
      type: "Default",
      nodes: [],
      connections: [],
      isActive: false,
    },
  ],
  activeFlowId: "welcome",
  loading: false,
  error: null,
}

// Mock API calls
export const fetchFlows = createAsyncThunk("flows/fetchFlows", async () => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 1000))
  return initialState.flows
})

export const createFlow = createAsyncThunk("flows/createFlow", async (flowData: Partial<Flow>) => {
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 500))
  return {
    id: Date.now().toString(),
    name: flowData.name || "New Flow",
    type: "Custom" as const,
    nodes: [],
    connections: [],
    isActive: false,
  }
})

const flowsSlice = createSlice({
  name: "flows",
  initialState,
  reducers: {
    setActiveFlow: (state, action: PayloadAction<string>) => {
      state.activeFlowId = action.payload
    },
    updateFlowName: (state, action: PayloadAction<{ id: string; name: string }>) => {
      const flow = state.flows.find((f) => f.id === action.payload.id)
      if (flow) {
        flow.name = action.payload.name
      }
    },
    deleteFlow: (state, action: PayloadAction<string>) => {
      state.flows = state.flows.filter((f) => f.id !== action.payload)
      if (state.activeFlowId === action.payload) {
        state.activeFlowId = state.flows[0]?.id || null
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFlows.pending, (state) => {
        state.loading = true
      })
      .addCase(fetchFlows.fulfilled, (state, action) => {
        state.loading = false
        state.flows = action.payload
      })
      .addCase(fetchFlows.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || "Failed to fetch flows"
      })
      .addCase(createFlow.fulfilled, (state, action) => {
        state.flows.push(action.payload)
      })
  },
})

export const { setActiveFlow, updateFlowName, deleteFlow } = flowsSlice.actions
export default flowsSlice.reducer
