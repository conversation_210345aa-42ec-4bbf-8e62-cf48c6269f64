import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { EditorState, Application, ModuleData, ModalTypeDetails, AutoSuggestionPosition } from "../../types"

const initialState: EditorState = {
  currentApp: null,
  settingDetails: {},
  modalTypeDetails: null,
  autoSuggestionPosition: null,
  isSimulatorEnabled: false,
  loading: false,
  error: null,
}

const editorSlice = createSlice({
  name: "editor",
  initialState,
  reducers: {
    setCurrentApp: (state, action: PayloadAction<Application | null>) => {
      state.currentApp = action.payload
    },
    setSettingDetails: (state, action: PayloadAction<Record<string, ModuleData>>) => {
      state.settingDetails = action.payload
    },
    updateSettingDetails: (state, action: PayloadAction<{ id: string; data: ModuleData }>) => {
      state.settingDetails[action.payload.id] = action.payload.data
    },
    setModalTypeDetails: (state, action: PayloadAction<ModalTypeDetails | null>) => {
      state.modalTypeDetails = action.payload
    },
    setAutoSuggestionPosition: (state, action: PayloadAction<AutoSuggestionPosition | null>) => {
      state.autoSuggestionPosition = action.payload
    },
    setSimulatorEnabled: (state, action: PayloadAction<boolean>) => {
      state.isSimulatorEnabled = action.payload
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
  },
})

export const {
  setCurrentApp,
  setSettingDetails,
  updateSettingDetails,
  setModalTypeDetails,
  setAutoSuggestionPosition,
  setSimulatorEnabled,
  setLoading,
  setError,
} = editorSlice.actions

export default editorSlice.reducer
