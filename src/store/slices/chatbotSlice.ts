import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

interface ChatbotState {
  title: string
  domain: string
  description: string
}

const initialState: ChatbotState = {
  title: "My Chatbot",
  domain: "Ecom",
  description: "Help customers navigate the digital purchasing process.",
}

const chatbotSlice = createSlice({
  name: "chatbot",
  initialState,
  reducers: {
    updateTitle: (state, action: PayloadAction<string>) => {
      state.title = action.payload
    },
    updateDomain: (state, action: PayloadAction<string>) => {
      state.domain = action.payload
    },
    updateDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload
    },
  },
})

export const { updateTitle, updateDomain, updateDescription } = chatbotSlice.actions
export default chatbotSlice.reducer
