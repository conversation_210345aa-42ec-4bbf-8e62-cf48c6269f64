// src/store/slices/apiSlice.ts

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { ApiResponse } from '@/types';
import type { UpdateAppRequest, StudioApp } from '@/types/app';

export const apiSlice = createApi({
  reducerPath: 'api', // default reducer name
  baseQuery: fetchBaseQuery({
    baseUrl: 'http://localhost:3000/api/v1/', // update this if you're using a proxy
  }),
  tagTypes: ['Application', 'Flow'], // optional, useful for invalidation
  endpoints: builder => ({
    updateApplicationDetails: builder.mutation<
      ApiResponse<StudioApp>, // Return type
      { appId: string; payload: UpdateAppRequest } // Args passed from hook
    >({
      query: ({ appId, payload }) => {
        console.log("Update triggerd",payload, appId);
        
        return {
          url: `/apps/${appId}/`,
          method: 'PUT',
          body: payload,
        };
      },
      invalidatesTags: ['Application'],
    }),
    getApplicationDetails: builder.mutation<
      ApiResponse<StudioApp>, // Return type
      { appId: string }
    >({
      query: ({ appId }) => {
        // console.log('[getApplicationDetails] Request App ID:', appId);
        return {
          url: `/apps/39e98160-5f1d-11f0-8ec3-b58905071e9b/`,
          method: 'GET',
        };
      },
      invalidatesTags: ['Application'],
    }),
  }),
});

export const { useUpdateApplicationDetailsMutation, useGetApplicationDetailsMutation } = apiSlice;
