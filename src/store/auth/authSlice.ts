import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../../redux/store';

export interface AuthState {
  isLoggedIn: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
}

export interface AuthResData {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
  'not-before-policy': number;
  session_state: string;
  scope: string;
}

const initialState: AuthState = {
  accessToken: localStorage.getItem('accessToken') ?? null,
  refreshToken: localStorage.getItem('refreshToken') ?? null,
  expires: +localStorage.getItem('expires')! || null,
  isLoggedIn: !!localStorage.getItem('accessToken'),
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const { access_token, refresh_token, expires_in } = action.payload;
      state.accessToken = access_token;
      state.refreshToken = refresh_token;
      state.expires = expires_in;
      state.isLoggedIn = true;
    },
    unsetCredentials: state => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
    },
  },
});

export const { setCredentials, unsetCredentials } = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) => state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) => state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) => state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
