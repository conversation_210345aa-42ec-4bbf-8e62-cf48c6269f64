import {
  ApiResponse,
  BotLanguage,
  CreateBotLanguageRequest,
  CreateLanguageRequest,
  Language,
  PaginatedResponse,
  PaginationParams,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Language', 'BotLanguage'],
});

export const languageApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // Languages
    getLanguages: builder.query<ApiResponse<PaginatedResponse<Language>>, PaginationParams>({
      query: params => ({
        url: '/languages',
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['Language'],
    }),
    getLanguage: builder.query<ApiResponse<Language>, UuidParams>({
      query: ({ id }) => ({ url: `/languages/${id}` }),
      providesTags: ['Language'],
    }),
    createLanguage: builder.mutation<ApiResponse<Language>, CreateLanguageRequest>({
      query: body => ({
        url: '/languages',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Language'],
    }),

    // Bot Languages
    getBotLanguages: builder.query<ApiResponse<PaginatedResponse<BotLanguage>>, PaginationParams>({
      query: params => ({
        url: '/bot-languages',
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['BotLanguage'],
    }),
    getBotLanguage: builder.query<ApiResponse<BotLanguage>, UuidParams>({
      query: ({ id }) => ({ url: `/bot-languages/${id}` }),
      providesTags: ['BotLanguage'],
    }),
    createBotLanguage: builder.mutation<ApiResponse<BotLanguage>, CreateBotLanguageRequest>({
      query: body => ({
        url: '/bot-languages',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BotLanguage'],
    }),
    deleteBotLanguage: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/bot-languages/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BotLanguage'],
    }),
  }),
});

export const {
  // Languages
  useGetLanguagesQuery,
  useGetLanguageQuery,
  useCreateLanguageMutation,

  // Bot Languages
  useGetBotLanguagesQuery,
  useGetBotLanguageQuery,
  useCreateBotLanguageMutation,
  useDeleteBotLanguageMutation,
} = languageApi;
