import {
  ApiResponse,
  AssignFlowToIntentRequest,
  CreateIntentItemRequest,
  CreateUtteranceTranslationRequest,
  IntentItem,
  IntentUtteranceGetAllParams,
  IntentUtteranceTranslation,
  PaginatedResponse,
  PaginationParams,
  UpdateIntentItemRequest,
  UpdateUtteranceTranslationRequest,
  UtteranceIdParam,
  UtteranceTranslationByLangParam,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['IntentItem', 'IntentUtteranceTranslation'],
});

export const intentApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // Intent Items
    getIntentItems: builder.query<ApiResponse<PaginatedResponse<IntentItem>>, PaginationParams>({
      query: params => ({
        url: '/intent-items',
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['IntentItem'],
    }),
    getIntentItem: builder.query<ApiResponse<IntentItem>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-items/${id}` }),
      providesTags: ['IntentItem'],
    }),
    createIntentItem: builder.mutation<ApiResponse<IntentItem>, CreateIntentItemRequest>({
      query: body => ({
        url: '/intent-items',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['IntentItem'],
    }),
    updateIntentItem: builder.mutation<
      ApiResponse<IntentItem>,
      UuidParams & UpdateIntentItemRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-items/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['IntentItem'],
    }),
    deleteIntentItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-items/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['IntentItem'],
    }),
    assignFlowToIntent: builder.mutation<ApiResponse<any>, AssignFlowToIntentRequest>({
      query: body => ({
        url: '/intent-items/assign-flow',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['IntentItem'],
    }),

    // Intent Utterance Translations
    getIntentUtteranceTranslations: builder.query<
      ApiResponse<PaginatedResponse<IntentUtteranceTranslation>>,
      IntentUtteranceGetAllParams
    >({
      query: ({ intentId, langId, query = {} }) => {
        const { filter, ...restQuery } = query;
        return {
          url: `/intent/${intentId}/lang/${langId}/intent-utterance`,
          params: {
            ...restQuery,
            ...(filter && { filter: JSON.stringify(filter) }),
          },
        };
      },
      providesTags: ['IntentUtteranceTranslation'],
    }),

    getIntentUtteranceTranslation: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams
    >({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
      }),
      providesTags: ['IntentUtteranceTranslation'],
    }),
    createIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      CreateUtteranceTranslationRequest
    >({
      query: ({ intentId, langId, ...body }) => ({
        url: `/intent/${intentId}/lang/${langId}/intent-utterance`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['IntentUtteranceTranslation'],
    }),

    updateIntentUtteranceTranslation: builder.mutation<
      ApiResponse<IntentUtteranceTranslation>,
      UuidParams & UpdateUtteranceTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/intent-utterance/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['IntentUtteranceTranslation'],
    }),
    deleteIntentUtteranceTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-utterance/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['IntentUtteranceTranslation'],
    }),
    getUtteranceTranslationsByUtteranceId: builder.query<
      ApiResponse<IntentUtteranceTranslation[]>,
      UtteranceIdParam
    >({
      query: ({ utteranceId }) => ({
        url: `/utterance/${utteranceId}/translations`,
      }),
      providesTags: ['IntentUtteranceTranslation'],
    }),
    getTranslationByUtteranceIdAndLangId: builder.query<
      ApiResponse<IntentUtteranceTranslation>,
      UtteranceTranslationByLangParam
    >({
      query: ({ utteranceId, langId }) => ({
        url: `/utterance/${utteranceId}/lang/${langId}/translation`,
      }),
      providesTags: ['IntentUtteranceTranslation'],
    }),
  }),
});

export const {
  useGetIntentItemsQuery,
  useGetIntentItemQuery,
  useCreateIntentItemMutation,
  useUpdateIntentItemMutation,
  useDeleteIntentItemMutation,
  useAssignFlowToIntentMutation,
  useGetIntentUtteranceTranslationsQuery,
  useGetIntentUtteranceTranslationQuery,
  useCreateIntentUtteranceTranslationMutation,
  useUpdateIntentUtteranceTranslationMutation,
  useDeleteIntentUtteranceTranslationMutation,
  useGetUtteranceTranslationsByUtteranceIdQuery,
  useGetTranslationByUtteranceIdAndLangIdQuery,
} = intentApi;
