import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { Application, Template, PaginatedResponse } from '../../types';
import config from '../../config';
import { apiSlice } from '../apiSlice';
import { ApiResponse } from '@/types/api.type';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['Application', 'Template', 'AppDetails'],
});

export const studioApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // Applications
    getApplications: builder.query<
      PaginatedResponse<Application>,
      {
        page?: number;
        size?: number;
        token?: string;
        isAdmin?: boolean;
        isPlatform?: boolean;
      }
    >({
      query: ({ page = 1, size = 10, token, isAdmin = false, isPlatform = false }) => {
        const endpoint = isPlatform
          ? 'leap_gw/apps/getAppsForPlatform'
          : isAdmin
            ? 'leap_gw/apps/getAppsforAdmin'
            : 'leap_gw/apps/getApps';

        const params = new URLSearchParams({
          page: page.toString(),
          size: size.toString(),
          ...(token && { token }),
        });

        return `${endpoint}?${params}`;
      },
      providesTags: ['Application'],
    }),

    updateApplicationDetails: builder.mutation<
      ApiResponse<StudioApp>, // Response
      { appId: string; payload: UpdateAppRequest } // Request
    >({
      query: ({ appId, payload }) => {
        return {
          url: `/apps/${appId}/`,
          method: 'PUT',
          body: payload,
        };
      },
      invalidatesTags: ['Application'],
    }),

    getPluginsEnabled: builder.query<any, void>({
      query: () => {
        return `/apps/getPluginsEnabled`;
      },

      // providesTags: [""],
    }),

    getApplicationDetails: builder.query<
      ApiResponse<StudioApp>, // Response
      { appId: string } // Request
    >({
      query: ({ appId }) => {
        return {
          url: `/apps/${appId}/`,
          method: 'GET',
        };
      },
      providesTags: ['Application'],
    }),

    createApplication: builder.mutation<{ id: string }, Partial<Application>>({
      query: data => ({
        url: 'leap_gw/apps/createAppInfo',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    updateApplication: builder.mutation<
      Application,
      {
        appId: string;
        payload: Partial<Application>;
        type?: string;
      }
    >({
      query: ({ appId, payload, type }) => ({
        url: `leap_gw/apps/updateApplicationDetails/${appId}${type ? `?type=${type}` : ''}`,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, { appId }) => [
        'Application',
        { type: 'AppDetails', id: appId },
      ],
    }),

    deleteApplication: builder.mutation<void, string>({
      query: id => ({
        url: `leap_gw/apps/deleteApp/${id}`,
        method: 'POST',
      }),
      invalidatesTags: ['Application'],
    }),

    cloneApplication: builder.mutation<
      { id: string },
      {
        id: string;
        data: { name: string; desc: string; svg?: string };
      }
    >({
      query: ({ id, data }) => ({
        url: `leap_gw/apps/cloneApp/${id}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    exportApplication: builder.query<any, string>({
      query: id => `leap_gw/apps/exportApp/${id}`,
    }),

    importApplication: builder.mutation<void, FormData>({
      query: formData => ({
        url: 'leap_gw/apps/importApp',
        method: 'POST',
        body: formData,
        formData: true,
      }),
      invalidatesTags: ['Application'],
    }),

    // Templates
    getTemplates: builder.query<{ data: Template[] }, void>({
      query: () => 'leap_gw/apps/getTemplatesForStudio',
      providesTags: ['Template'],
    }),

    cloneTemplate: builder.mutation<
      { id: string },
      {
        name: string;
        appTemplateId: string;
        svg: string;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/cloneTemplate',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // AI
    createAppWithAI: builder.mutation<{ id: string }, { prompt: string }>({
      query: data => ({
        url: 'leap_gw/apps/createAppWithAI',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    getQuickAccessList: builder.query<
      { data: { questions: Array<{ prompt: string; description: string }> } },
      void
    >({
      query: () => 'leap_gw/apps/getQuickAccessList',
    }),

    // Platform Admin
    updateOTCAndMRC: builder.mutation<
      void,
      {
        id: string;
        nodeExecCharge: number;
        freeNodeExec: number;
        OTC: number;
        MRC: number;
      }
    >({
      query: data => ({
        url: 'leap_gw/apps/updateOTCAndMRC',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Application'],
    }),

    // Logs
    getAppReports: builder.query<any, { appId: string }>({
      query: ({ appId }) => ({
        url: 'leap_gw/apps/getAppReports',
        method: 'POST',
        body: { appId },
      }),
    }),

    getDetailedAppReport: builder.query<
      any,
      {
        appId: string;
        page: number;
        size: number;
      }
    >({
      query: ({ appId, page, size }) => ({
        url: `leap_gw/apps/getDetailedAppReport?page=${page}&size=${size}`,
        method: 'POST',
        body: { appId, name: 'getAllData' },
      }),
    }),

    // Start URL
    getApplicationStartUrl: builder.query<
      {
        data: { url: { production: string } };
      },
      { payload: { engine: string; appId: string } }
    >({
      query: ({ payload }) => ({
        url: 'leap_gw/apps/getApplicationStartUrl',
        method: 'POST',
        body: payload,
      }),
    }),
  }),
});

export const {
  useGetApplicationsQuery,
  useGetApplicationDetailsQuery,
  useCreateApplicationMutation,
  useUpdateApplicationMutation,
  useDeleteApplicationMutation,
  useCloneApplicationMutation,
  useLazyExportApplicationQuery,
  useImportApplicationMutation,
  useGetTemplatesQuery,
  useCloneTemplateMutation,
  useCreateAppWithAIMutation,
  useGetQuickAccessListQuery,
  useUpdateOTCAndMRCMutation,
  useGetAppReportsQuery,
  useGetDetailedAppReportQuery,
  useGetApplicationStartUrlQuery,
  useGetPluginsEnabledQuery,
  useUpdateApplicationDetailsMutation,
} = studioApi;
