import {
  ApiResponse,
  CreateFaqCategoryRequest,
  CreateFaqTranslationRequest,
  FaqCategory,
  FaqIdParam,
  FaqItem,
  FaqTranslation,
  FaqTranslationByLangParam,
  GetFaqsByCategoryAndLanguageParams,
  PaginatedResponse,
  PaginationParams,
  UpdateFaqCategoryRequest,
  UpdateFaqTranslationRequest,
  UuidParams,
} from '@/types';
import { apiSlice } from '../apiSlice';

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: ['FaqCategory', 'FaqTranslation'],
});

export const faqApi = updatedApiSlice.injectEndpoints({
  endpoints: builder => ({
    // FAQ Categories
    getFaqCategories: builder.query<ApiResponse<PaginatedResponse<FaqCategory>>, PaginationParams>({
      query: params => ({
        url: '/faq-categories',
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['FaqCategory'],
    }),
    getFaqCategory: builder.query<ApiResponse<FaqCategory>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-categories/${id}` }),
      providesTags: ['FaqCategory'],
    }),
    createFaqCategory: builder.mutation<ApiResponse<FaqCategory>, CreateFaqCategoryRequest>({
      query: body => ({
        url: '/faq-categories',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['FaqCategory'],
    }),
    updateFaqCategory: builder.mutation<
      ApiResponse<FaqCategory>,
      UuidParams & UpdateFaqCategoryRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-categories/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['FaqCategory'],
    }),
    deleteFaqCategory: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-categories/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['FaqCategory'],
    }),

    // FAQ Translations
    getFaqsByCategoryAndLanguage: builder.query<
      ApiResponse<PaginatedResponse<FaqItem>>,
      GetFaqsByCategoryAndLanguageParams
    >({
      query: ({ categoryId, langId, ...params }) => ({
        url: `/faqs/category/${categoryId}/language/${langId}`,
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['FaqTranslation'],
    }),
    getFaqTranslations: builder.query<
      ApiResponse<PaginatedResponse<FaqTranslation>>,
      PaginationParams
    >({
      query: params => ({
        url: '/faq-translations',
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ['FaqTranslation'],
    }),
    getFaqTranslation: builder.query<ApiResponse<FaqTranslation>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-translations/${id}` }),
      providesTags: ['FaqTranslation'],
    }),
    createFaqTranslation: builder.mutation<
      ApiResponse<FaqTranslation>,
      CreateFaqTranslationRequest
    >({
      query: body => ({
        url: '/faq-translations',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['FaqTranslation'],
    }),
    updateFaqTranslation: builder.mutation<
      ApiResponse<FaqTranslation>,
      UuidParams & UpdateFaqTranslationRequest
    >({
      query: ({ id, ...body }) => ({
        url: `/faq-translations/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['FaqTranslation'],
    }),
    deleteFaqTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-translations/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['FaqTranslation'],
    }),
    getTranslationByFaqIdAndLangId: builder.query<
      ApiResponse<FaqTranslation>,
      FaqTranslationByLangParam
    >({
      query: ({ faqId, langId }) => ({
        url: `/faq/${faqId}/lang/${langId}/translation`,
      }),
      providesTags: ['FaqTranslation'],
    }),

    getFaqTranslationsByFaqId: builder.query<ApiResponse<FaqTranslation[]>, FaqIdParam>({
      query: ({ faqId }) => ({
        url: `/faq/${faqId}/translations`,
      }),
      providesTags: ['FaqTranslation'],
    }),
  }),
});

export const {
  useGetFaqCategoriesQuery,
  useGetFaqCategoryQuery,
  useCreateFaqCategoryMutation,
  useUpdateFaqCategoryMutation,
  useDeleteFaqCategoryMutation,
  useGetFaqsByCategoryAndLanguageQuery,
  useGetFaqTranslationsQuery,
  useGetFaqTranslationQuery,
  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useDeleteFaqTranslationMutation,
  useGetFaqTranslationsByFaqIdQuery,
  useGetTranslationByFaqIdAndLangIdQuery,
} = faqApi;
