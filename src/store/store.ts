import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth/authSlice';
import { apiSlice } from './apiSlice';
import chatbotsReducer from './slices/chatbotsSlice';
import flowsReducer from './slices/flowsSlice';
import uiReducer from './slices/uiSlice';
import chatbotReducer from './slices/chatbotSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    flows: flowsReducer,
    ui: uiReducer,
    chatbot: chatbotReducer,
    chatbots: chatbotsReducer,
    [apiSlice.reducerPath]: apiSlice.reducer,
  },
  middleware: getDefaultMiddleware => getDefaultMiddleware().concat(apiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
