import { createContext, useContext } from 'react';

interface PaginationContextType<T> {
  items: T[];
  isLoading: boolean;
  isFetching: boolean;
  error: any;
  hasMore: boolean;
  fetchNextPage: () => void;
  refetch: () => void;
}

const PaginationContext = createContext<PaginationContextType<any> | undefined>(undefined);

export function usePaginationContext<T>() {
  const context = useContext(PaginationContext);
  if (context === undefined) {
    throw new Error('usePaginationContext must be used within a PaginationProvider');
  }
  return context as PaginationContextType<T>;
}

export function PaginationProvider<T>({ children, value }: {
  children: React.ReactNode;
  value: PaginationContextType<T>;
}) {
  return (
    <PaginationContext.Provider value={value}>
      {children}
    </PaginationContext.Provider>
  );
}
