            import React, { createContext, useContext, useState } from "react";
import { FormValues } from "../Pages/RevampRuleEngine/RevampRuleCriteria";




const RuleContext = createContext(undefined);

 export const RuleProvider = ({ children }) => {
    const defaultCriteria = [
        { key: "", value: "", entityTypeMapping: "", entityEndPointMapping: "" },
      ];
      
 const [formData, setFormData] = useState<FormValues>({
    criteria: defaultCriteria,
      ruleName: "",
    responseJson: "",
    description: "",
  });
const [ruleID,setRuleID] = useState<string>("");
  return (
    <RuleContext.Provider value={{ formData, setFormData, ruleID, setRuleID }}>
      {children}
    </RuleContext.Provider>
  );
};

export const useRule = () => {
  const context = useContext(RuleContext);
  if (!context) {
    throw new Error("useRule must be used within a RuleProvider");
  }
  return context;
};
