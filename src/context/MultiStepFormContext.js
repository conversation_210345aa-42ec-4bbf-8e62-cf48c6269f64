import React, { useState } from "react";

export const multiStepFormContext = React.createContext();
const StepContext = ({ children }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [stepsCompleted, setStepsCompleted] = useState([]);
  const [formData, setFormData] = useState([]);
  const [finalData, setFinalData] = useState([]);
  const [WAdynamicVarObj, setWAdynamicVarObj] = useState({});
  const [aiComposerSmsChannelData, setAiComposerSmsChannelData] = useState({});
  const [markerPosition, setMarkerPosition] = useState(null);

  const handleNextClick = (increment) => {
    const jumpNextStepBy = increment || 1;
    setCurrentStep(currentStep + jumpNextStepBy);
    if (jumpNextStepBy > 1) {
      stepsCompleted.splice(currentStep - 1, 5, currentStep);
    } else {
      if (!stepsCompleted.some((x) => x === currentStep))
        setStepsCompleted([...stepsCompleted, currentStep]);
    }
    // else () {
    //
    // }
  };

  const handleCancelClick = () => {
    setCurrentStep(1);
    setStepsCompleted([]);
    setFormData([]);
    setFinalData([]);
  };
  const handlePrevClick = () => {
    const index = stepsCompleted.indexOf(currentStep);
    if (currentStep !== 1) {
      if (index.toString() === "-1") {
        setCurrentStep(stepsCompleted[stepsCompleted.length - 1]);
      } else {
        setCurrentStep(stepsCompleted[index - 1]);
      }
    }
  };

  return (
    <div>
      <multiStepFormContext.Provider
        value={{
          currentStep,
          setCurrentStep,
          stepsCompleted,
          setStepsCompleted,
          formData,
          setFormData,
          finalData,
          setFinalData,
          handleNextClick,
          handlePrevClick,
          handleCancelClick,
          aiComposerSmsChannelData,
          setAiComposerSmsChannelData,
          WAdynamicVarObj,
          setWAdynamicVarObj,
          markerPosition,
          setMarkerPosition,
        }}
      >
        {children}
      </multiStepFormContext.Provider>
    </div>
  );
};
export default StepContext;
