import { FilterOptions } from "@/pages/Home/Home";

interface DateRange {
  lowerBound: string | null;
  upperBound: string | null;
}

export const getDateRange = (type: FilterOptions['dateFilter']): DateRange => {
  const now = new Date();
  const ranges: Record<FilterOptions['dateFilter'], DateRange> = {
    Today: {
      lowerBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        0,
        0,
        0,
        0
      ).toISOString(),
      upperBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59,
        999
      ).toISOString(),
    },
    Yesterday: {
      lowerBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() - 1,
        0,
        0,
        0,
        0
      ).toISOString(),
      upperBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() - 1,
        23,
        59,
        59,
        999
      ).toISOString(),
    },
    'Last 7 days': {
      lowerBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() - 7,
        0,
        0,
        0,
        0
      ).toISOString(),
      upperBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59,
        999
      ).toISOString(),
    },
    'This Month': {
      lowerBound: new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0).toISOString(),
      upperBound: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString(),
    },
    'Last 30 days': {
      lowerBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() - 30,
        0,
        0,
        0,
        0
      ).toISOString(),
      upperBound: new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59,
        999
      ).toISOString(),
    },
    All: { lowerBound: null, upperBound: null },
  };

  return ranges[type];
};

export const getDaysAgo = (dateString: string): string => {
  const updatedDate = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - updatedDate.getTime()) / 1000);

  if (diffInSeconds < 30) return 'Just now';
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 1) return '1 minute ago';
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 1) return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 1) return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
  return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
};
