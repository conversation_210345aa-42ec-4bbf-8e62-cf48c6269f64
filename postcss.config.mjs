import tailwindcss from 'tailwindcss';
import prefixer from 'postcss-prefix-selector';

/** @type {import('postcss-load-config').Config} */
export default {
  plugins: [
    tailwindcss,
    prefixer({
      prefix: '.mfe-app',
      transform(prefix, selector, prefixedSelector) {
        if (
          selector.startsWith('html') ||
          selector.startsWith('body') ||
          selector.startsWith('*') ||
          selector.includes(':root') 
          // ||
          // selector.includes('::before') ||
          // selector.includes('::after')
        ) {
          return selector;
        }
        return prefixedSelector;
      },
    }),
  ],
};
