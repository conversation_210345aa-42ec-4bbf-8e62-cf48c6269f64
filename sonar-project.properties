sonar.host.url=http://sonar.comviva.com/sonar 
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-chatbot-ui.git
sonar.projectVersion=8.0
sonar.sources=src/
sonar.branch.name=ng-mainline-dev
sonar.nodejs.executable=/usr/bin/node
sonar.cfamily.build-wrapper-output.bypass=true 
sonar.projectName=MCS_CPAAS_8x_RM_Chatbot-UI
sonar.projectKey=my:MCS_CPAAS_8x_RM_Chatbot-UI
sonar.exclusions=**/test/**,**/*.js,src/**/*.js,**/node_modules/**,**/whatfix*/**,app/js/**,app/online_help/**,**/customers*/**,app/css/static/*,scripts/**,app/fonts/font-awesome-4.4.0/**,app/styles/elements/**
